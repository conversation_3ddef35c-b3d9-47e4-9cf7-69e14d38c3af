{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {"id": "Fvdt_pMpf7EK"}, "source": ["# Task Generation Cookbook"]}, {"cell_type": "markdown", "metadata": {"id": "pf-CrViNgJqn"}, "source": ["You can also check this cookbook in colab [here](https://colab.research.google.com/drive/1n_SjiE7NRmpUUBcRge-gqKAv5mzWPcU0?usp=sharing)\n", "\n", "⭐ <i>Star us on [*Github*](https://github.com/camel-ai/camel), join our [*Discord*](https://discord.camel-ai.org) or follow our [*X*](https://x.com/camelaiorg)"]}, {"cell_type": "markdown", "metadata": {"id": "2XXT4d1tf7EM"}, "source": ["In this tutorial, we will focus on demonstrating how to use the task module in the CAMEL framework. We will guide you through creating, evolving, and decomposing tasks to illustrate how the task module can be utilized for efficient task management in agent-based systems."]}, {"cell_type": "markdown", "metadata": {"id": "ph2dWXb-f7EM"}, "source": ["Sections included:\n", "\n", "- Setting up a ChatAgent with the CAMEL framework\n", "- Creating a Task and evolving it with the agent using TaskManager\n", "- Task decomposition using the CAMEL task module\n", "\n", "Let's go step by step!"]}, {"cell_type": "markdown", "metadata": {"id": "4uoXafBRf7EN"}, "source": ["## Step 1: Import necessary CAMEL modules\n", "\n", "First, we need to import the required CAMEL modules for creating the ChatAgent and handling tasks."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "My_O9chyf7EN"}, "outputs": [], "source": ["!pip install \"camel-ai==0.2.16\""]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "3povzm9qf7EO"}, "outputs": [], "source": ["from camel.agents import ChatAgent\n", "from camel.configs import ChatGPTConfig\n", "from camel.messages import BaseMessage\n", "from camel.models import ModelFactory\n", "from camel.tasks import (\n", "    Task,\n", "    Task<PERSON>anager,\n", ")\n", "from camel.types import (\n", "    ModelPlatformType,\n", "    ModelType,\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "H9ANkBsuf7EO"}, "source": ["Set your OpenAI key"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "t7u1PNhnf7EO", "outputId": "e507099a-cba2-443c-aef1-b015f97aa584"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter your API key: ··········\n"]}], "source": ["import os\n", "from getpass import getpass\n", "\n", "# Prompt for the API key securely\n", "openai_api_key = getpass('Enter your API key: ')\n", "os.environ[\"OPENAI_API_KEY\"] = openai_api_key"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Alternatively, if running on Colab, you could save your API keys and tokens as **Colab Secrets**, and use them across notebooks.\n", "\n", "To do so, **comment out** the above **manual** API key prompt code block(s), and **uncomment** the following codeblock.\n", "\n", "⚠️ Don't forget granting access to the API key you would be using to the current notebook."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# from google.colab import userdata\n", "\n", "# os.environ[\"OPENAI_API_KEY\"] = userdata.get(\"OPENAI_API_KEY\")"]}, {"cell_type": "markdown", "metadata": {"id": "brQ0v3kaf7EP"}, "source": ["## Step 2: Set up the Large Language Model (LLM)\n", "\n", "Next, we set up the model configuration. We are using a GPT-4O Mini in this case for our assistant agent. The configuration is designed to ensure the agent’s behavior remains deterministic by setting temperature=0.0, meaning no randomness will be introduced in the responses."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "DLXsqm7mf7EP"}, "outputs": [], "source": ["# Create the model using the configuration\n", "model = ModelFactory.create(\n", "    model_platform=ModelPlatformType.OPENAI,\n", "    model_type=ModelType.GPT_4O_MINI,\n", "    model_config_dict=ChatGPTConfig(temperature=0.0).as_dict(), # [Optional] the config for model\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "IAstp4cyf7EP"}, "source": ["## Step 3: Initialize the ChatAgent\n", "\n", "We now create a ChatAgent using the previously defined model. This agent will interact with tasks in the CAMEL framework, following the role of a personal math tutor and programmer."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "rSo-keNnf7EP"}, "outputs": [], "source": ["# Set up the assistant's system message\n", "assistant_sys_msg = BaseMessage.make_user_message(\n", "    role_name=\"Teacher\",\n", "    content=\"You are a personal math tutor and programmer.\",\n", ")\n", "\n", "# Initialize the ChatAgent\n", "agent = ChatAgent(assistant_sys_msg, model)\n", "agent.reset()  # Reset the agent's internal state"]}, {"cell_type": "markdown", "metadata": {"id": "b-RGszDYf7EP"}, "source": ["## Step 4: Evolve and decompose tasks\n", "\n", "We now create a Task that represents a math problem for the assistant to solve. In this case, we are asking the assistant to calculate how much <PERSON><PERSON> earned for babysitting based on her hourly rate and the time she worked."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "YZ8TaZzjf7EQ", "outputId": "c30e51d5-7ae7-41f6-825f-28e654bffc40"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Task 0: <PERSON><PERSON> earns $12 an hour for babysitting. Yesterday, she just did 51 minutes of babysitting. How much did she earn?\n", "\n"]}], "source": ["# Create a Task for the agent to solve\n", "task = Task(\n", "    content=\"<PERSON><PERSON> earns $12 an hour for babysitting. Yesterday, she just did 51 minutes of babysitting. How much did she earn?\",\n", "    id=\"0\",  # Task identifier\n", ")\n", "\n", "# Print the task to see the original form\n", "print(task.to_string())"]}, {"cell_type": "markdown", "metadata": {"id": "R1xHH7UAf7EQ"}, "source": ["### Evolve the Task\n", "\n", "We can evolve the task using the TaskManager, which allows the agent to potentially update or reframe the task based on its internal logic and context."]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ThFlgKehf7EQ", "outputId": "3811ae11-1c78-4b3a-e011-e5430f9dd11c"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Task 0.0: <PERSON><PERSON> earns $12 an hour for babysitting. Yesterday, she babysat for 1 hour and 45 minutes. If she also received a bonus of $5 for exceptional service, how much did she earn in total?\n", "\n"]}], "source": ["task_manager = TaskManager(task)\n", "\n", "evolved_task = task_manager.evolve(task, agent=agent)\n", "if evolved_task is not None:\n", "    print(evolved_task.to_string())\n", "else:\n", "    print(\"Evolved task is None.\")"]}, {"cell_type": "markdown", "metadata": {"id": "b_6IxCWPf7EQ"}, "source": ["### Decompose the Task\n", "\n", "Sometimes, tasks are complex and need to be broken down into smaller subtasks. We use decompose() to allow the agent to split the original task into simpler parts."]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "bWNpUckzf7EQ", "outputId": "4012310b-6210-4360-aef1-9620bd621785"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Task 0.0: Convert the babysitting time from minutes to hours.\n", "\n", "Task 0.1: Calculate the earnings based on the hourly rate of $12 and the converted time in hours.\n", "\n", "Task 0.2: Present the final earnings amount clearly.\n", "\n"]}], "source": ["new_tasks = task.decompose(agent=agent)\n", "for t in new_tasks:\n", "    print(t.to_string())"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}