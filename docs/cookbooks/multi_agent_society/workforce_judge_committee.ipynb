{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {"id": "k-xIA7Qjuiwu"}, "source": ["# Create A Hackathon Judge Committee with Workforce\n", "\n", "Workforce is a system where multiple agents collaborate to solve a given task. In this notebook, we will walk through it with a demo of a hackathon judge committee, where judges with different personas collaborate together to give scores to hackathon projects.\n", "\n", "You can also check this cookbook in colab [here](https://colab.research.google.com/drive/*********************************?usp=sharing)\n", "\n", "⭐ <i>Star us on [*Github*](https://github.com/camel-ai/camel), join our [*Discord*](https://discord.camel-ai.org) or follow our [*X*](https://x.com/camelaiorg)\n", "\n", "## Dependency Installation\n", "\n", "To get started, make sure you have `camel-ai` installed.\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "id": "kR2UeIHFUBMf"}, "outputs": [], "source": ["%pip install \"camel-ai[all]==0.2.16\""]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"id": "mY3G7K9swEzz"}, "source": ["Workforce employs an asynchronous design with coroutines. However, since **coroutines cannot directly run in notebooks**, we need to do specific handlings in this demo. Note that, under most normal cases (not inside notebook environment), we don't need to do this."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "smfCqcqNUJHX", "outputId": "073a6e1c-f747-4527-ce85-6913bda900a1"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: nest_asyncio in /usr/local/lib/python3.10/dist-packages (1.6.0)\n"]}], "source": ["%pip install nest_asyncio\n", "import nest_asyncio\n", "nest_asyncio.apply()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"id": "FIz0b9O_wqkW"}, "source": ["## Key Configuration\n", "\n", "In this demo, we will use tools related to web searching. Therefore, we need to configure the OpenAI API key, along with the Google API keys beforehand."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "HGpgR_XitX_B", "outputId": "11700088-16c8-4602-96ad-9b4289f2ffd2"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Please input your OpenAI API key: ··········\n", "Please input your Google API key: ··········\n", "Please input your Search Engine ID: ··········\n"]}], "source": ["from getpass import getpass\n", "import os\n", "\n", "openai_api_key = getpass(\"Please input your OpenAI API key: \")\n", "os.environ[\"OPENAI_API_KEY\"] = openai_api_key\n", "# https://developers.google.com/custom-search/v1/overview\n", "google_api_key = getpass(\"Please input your Google API key: \")\n", "os.environ[\"GOOGLE_API_KEY\"] = google_api_key\n", "# https://cse.google.com/cse/all\n", "search_engine_id = getpass(\"Please input your Search Engine ID: \")\n", "os.environ[\"SEARCH_ENGINE_ID\"] = search_engine_id"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Alternatively, if running on Colab, you could save your API keys and tokens as **Colab Secrets**, and use them across notebooks.\n", "\n", "To do so, **comment out** the above **manual** API key prompt code block(s), and **uncomment** the following codeblock.\n", "\n", "⚠️ Don't forget granting access to the API key you would be using to the current notebook."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# from google.colab import userdata\n", "\n", "# os.environ[\"OPENAI_API_KEY\"] = userdata.get(\"OPENAI_API_KEY\")\n", "# os.environ[\"GOOGLE_API_KEY\"] = userdata.get(\"GOOGLE_API_KEY\")\n", "# os.environ[\"SEARCH_ENGINE_ID\"] = userdata.get(\"SEARCH_ENGINE_ID\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"id": "PnMcEXo_xxGF"}, "source": ["## Define a Function for Making Judge Agent\n", "\n", "In this demo, we will create multiple judge agents with different personas and scoring criteria. For reusability, we first create a function to make judge agents."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "vYz3PT3YyOUC"}, "outputs": [], "source": ["import textwrap\n", "\n", "from camel.agents import ChatAgent\n", "from camel.messages import BaseMessage\n", "from camel.models import ModelFactory\n", "from camel.tasks import Task\n", "from camel.toolkits import FunctionTool, SearchToolkit\n", "from camel.types import ModelPlatformType, ModelType\n", "from camel.societies.workforce import Workforce\n", "\n", "def make_judge(\n", "    persona: str,\n", "    example_feedback: str,\n", "    criteria: str,\n", ") -> ChatAgent:\n", "    msg_content = textwrap.dedent(\n", "        f\"\"\"\\\n", "        You are a judge in a hackathon.\n", "        This is your persona that you MUST act with: {persona}\n", "        Here is an example feedback that you might give with your persona, you MUST try your best to align with this:\n", "        {example_feedback}\n", "        When evaluating projects, you must use the following criteria:\n", "        {criteria}\n", "        You also need to give scores based on these criteria, from 1-4. The score given should be like 3/4, 2/4, etc.\n", "        \"\"\"  # noqa: E501\n", "    )\n", "\n", "    sys_msg = BaseMessage.make_assistant_message(\n", "        role_name=\"Ha<PERSON><PERSON>on Judge\",\n", "        content=msg_content,\n", "    )\n", "\n", "    model = ModelFactory.create(\n", "        model_platform=ModelPlatformType.OPENAI,\n", "        model_type=ModelType.GPT_4O,\n", "    )\n", "\n", "    agent = ChatAgent(\n", "        system_message=sys_msg,\n", "        model=model,\n", "    )\n", "\n", "    return agent"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"id": "HtP_ej5CyUNT"}, "source": ["## Make a Mocked Hackathon Project\n", "\n", "Then we will create a mocked hackathon project description, which will be later sent to the judges for scoring."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "ClqLd4HKypTI"}, "outputs": [], "source": ["proj_content = textwrap.dedent(\n", "    \"\"\"\\\n", "    Project name: CAMEL-Powered Adaptive Learning Assistant\n", "    How does your project address a real problem: Our CAMEL-Powered Adaptive Learning Assistant addresses the challenge of personalized education in an increasingly diverse and fast-paced learning environment. Traditional one-size-fits-all approaches to education often fail to meet the unique needs of individual learners, leading to gaps in understanding and reduced engagement. Our project leverages CAMEL-AI's advanced capabilities to create a highly adaptive, intelligent tutoring system that can understand and respond to each student's learning style, pace, and knowledge gaps in real-time.\n", "    Explain your tech and which parts work: Our system utilizes CAMEL-AI's in-context learning and multi-domain application features to create a versatile learning assistant. The core components include:\n", "    1. Learner Profile Analysis: Uses natural language processing to assess the student's current knowledge, learning preferences, and goals.\n", "    2. Dynamic Content Generation: Leverages CAMEL-AI to create personalized learning materials, explanations, and practice questions tailored to each student's needs.\n", "    3. Adaptive Feedback Loop: Continuously analyzes student responses and adjusts the difficulty and style of content in real-time.\n", "    4. Multi-Modal Integration: Incorporates text, images, and interactive elements to cater to different learning styles.\n", "    5. Progress Tracking: Provides detailed insights into the student's learning journey, identifying strengths and areas for improvement.\n", "    Currently, we have successfully implemented the Learner Profile Analysis and Dynamic Content Generation modules. The Adaptive Feedback Loop is partially functional, while the Multi-Modal Integration and Progress Tracking features are still in development.\n", "    \"\"\"  # noqa: E501\n", ")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"id": "z35CIla3zBF4"}, "source": ["## Create Agents\n", "\n", "Then we will create five unique agents that will later collaborate together. Among these five agents, one of them is the helper that will help collect information and summarize the final result. We add search functions to this agent so that it can obtain information from online searches.\n", "\n", "The other four agents, on the other hand, are judges with different personas and criteria. They will give scores to the project according to the description, along with the information collected by the helper."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"id": "QkURpvVUT9Rk"}, "outputs": [], "source": ["# Create helper agent\n", "search_toolkit = SearchToolkit()\n", "search_tools = [\n", "    FunctionTool(search_toolkit.search_google),\n", "    FunctionTool(search_toolkit.search_duckduckgo),\n", "]\n", "\n", "researcher_model = ModelFactory.create(\n", "    model_platform=ModelPlatformType.OPENAI,\n", "    model_type=ModelType.GPT_4O,\n", ")\n", "\n", "researcher_agent = ChatAgent(\n", "    system_message=BaseMessage.make_assistant_message(\n", "        role_name=\"Researcher\",\n", "        content=\"You are a researcher who does research on AI and Open\"\n", "        \"Sourced projects. You use web search to stay updated on the \"\n", "        \"latest innovations and trends.\",\n", "    ),\n", "    model=researcher_model,\n", "    tools=search_tools,\n", ")\n", "\n", "# Create venture capitalist judge\n", "vc_persona = (\n", "    'You are a venture capitalist who is obsessed with how projects can '\n", "    'be scaled into \"unicorn\" companies. You peppers your speech with '\n", "    'buzzwords like \"disruptive,\" \"synergistic,\" and \"market penetration.\"'\n", "    ' You do not concerned with technical details or innovation unless '\n", "    'it directly impacts the business model.'\n", ")\n", "\n", "vc_example_feedback = (\n", "    '\"Wow, this project is absolutely disruptive in the blockchain-enabled'\n", "    ' marketplace! I can definitely see synergistic applications in the '\n", "    'FinTech ecosystem. The scalability is through the roof--this is '\n", "    'revolutionary!'\n", ")\n", "\n", "vc_criteria = textwrap.dedent(\n", "    \"\"\"\\\n", "    ### **Applicability to Real-World Usage (1-4 points)**\n", "    - **4**: The project directly addresses a significant real-world problem with a clear, scalable application.\n", "    - **3**: The solution is relevant to real-world challenges but requires more refinement for practical or widespread use.\n", "    - **2**: Some applicability to real-world issues, but the solution is not immediately practical or scalable.\n", "    - **1**: Little or no relevance to real-world problems, requiring substantial changes for practical use.\n", "    \"\"\"  # noqa: E501\n", ")\n", "\n", "vc_agent = make_judge(\n", "    vc_persona,\n", "    vc_example_feedback,\n", "    vc_criteria,\n", ")\n", "\n", "# Create experience engineer judge\n", "eng_persona = (\n", "    'You are an experienced engineer and a perfectionist. You are highly '\n", "    'detail-oriented and critical of any technical flaw, no matter how '\n", "    'small. He evaluates every project as though it were going into a '\n", "    'mission-critical system tomorrow, so his feedback is thorough but '\n", "    'often harsh.'\n", ")\n", "\n", "eng_example_feedback = (\n", "    'There are serious code inefficiencies in this project. The '\n", "    'architecture is unstable, and the memory management is suboptimal. '\n", "    'I expect near-perfect performance, but this solution barely functions'\n", "    ' under stress tests. It has potential, but it is nowhere near '\n", "    'deployment-ready.'\n", ")\n", "\n", "eng_criteria = textwrap.dedent(\n", "    \"\"\"\\\n", "    ### **Technical Implementation (1-4 points)**\n", "    - **4**: Flawless technical execution with sophisticated design, efficient performance, and robust architecture.\n", "    - **3**: Strong technical implementation, though there may be areas for improvement or further development.\n", "    - **2**: The project works, but technical limitations or inefficiencies hinder its overall performance.\n", "    - **1**: Poor technical implementation with major issues in functionality, coding, or structure.\n", "    \"\"\"  # noqa: E501\n", ")\n", "\n", "eng_agent = make_judge(\n", "    eng_persona,\n", "    eng_example_feedback,\n", "    eng_criteria,\n", ")\n", "\n", "# Create AI founder judge\n", "founder_persona = (\n", "    'You are a well-known AI startup founder who is always looking for the'\n", "    ' \"next big thing\" in AI. You value bold, inventive ideas and '\n", "    'prioritizes projects that break new ground over those that improve '\n", "    'existing systems.'\n", ")\n", "\n", "founder_example_feedback = (\n", "    'This is interesting, but I have seen similar approaches before. I am '\n", "    'looking for something that pushes boundaries and challenges norms. '\n", "    'What is the most revolutionary part of this project? Let us see what '\n", "    'is trending on Internet to make sure this is not already out there!'\n", ")\n", "\n", "founder_criteria = textwrap.dedent(\n", "    \"\"\"\\\n", "    ### **Innovation (1-4 points)**\n", "    - **4**: The project showcases a groundbreaking concept or a unique approach that significantly departs from existing methods.\n", "    - **3**: The project demonstrates a novel twist on known solutions or introduces some innovative aspects.\n", "    - **2**: Some level of innovation is present, but the project largely builds on existing ideas without major new contributions.\n", "    - **1**: Little or no innovation; the project is based on standard approaches with minimal creativity.\n", "    \"\"\"  # noqa: E501\n", ")\n", "\n", "founder_agent = make_judge(\n", "    founder_persona,\n", "    founder_example_feedback,\n", "    founder_criteria,\n", ")\n", "\n", "# Create CAMEL contributor judge\n", "contributor_persona = (\n", "    'You are a contributor to the CAMEL-AI project and is always excited '\n", "    'to see how people are using it. You are kind and optimistic, always '\n", "    'offering positive feedback, even for projects that are still rough '\n", "    'around the edges.'\n", ")\n", "\n", "contributor_example_feedback = (\n", "    'Oh, I love how you have implemented CAMEL-AI here! The use of its '\n", "    'adaptive learning capabilities is fantastic, and you have really '\n", "    'leveraged the contextual reasoning in a great way! Let me just pull '\n", "    'up the GitHub README to check if there is any more potential '\n", "    'optimizations.'\n", ")\n", "\n", "contributor_criteria = textwrap.dedent(\n", "    \"\"\"\\\n", "    ### **Use of CAMEL-AI (1-4 points)**\n", "    - **4**: Excellent integration of CAMEL-AI, fully leveraging its advanced features like in-context learning, adaptability, or multi-domain applications.\n", "    - **3**: Good use of CAMEL-AI, but there are opportunities to exploit more of its advanced capabilities.\n", "    - **2**: Limited use of CAMEL-AI, relying mostly on basic features without taking advantage of its full potential.\n", "    - **1**: CAMEL-AI integration is minimal or poorly implemented, adding little value to the project.\n", "    \"\"\"  # noqa: E501\n", ")\n", "\n", "contributor_agent = make_judge(\n", "    contributor_persona,\n", "    contributor_example_feedback,\n", "    contributor_criteria,\n", ")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"id": "GG3CR8hX0p5w"}, "source": ["## Create Workforce\n", "\n", "Then we will do the most important part of the demo: create a workforce. Despite its importance, this is actually easy. First, we can simply instantiate a workforce by passing a description to it. Then, we just call `add_single_agent_workder()` to add agents into it, along with their descriptions.\n", "\n", "Note that, the description is very important in workforce, because it helps the coordinator agent in the workforce to do the task designation. Therefore, it's recommended to clearly mark the responsibility and capability of an agent when adding it to the workforce."]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "yGXWp4Lw0pEM", "outputId": "c64f554e-81f8-4374-8002-47af60b37a12"}, "outputs": [{"data": {"text/plain": ["Workforce 134971161063008 (Hackathon Judges)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["workforce = Workforce('Hackathon Judges')\n", "\n", "workforce.add_single_agent_worker(\n", "    '<PERSON><PERSON> (Judge), a venture capitalist who is '\n", "    'obsessed with how projects can be scaled into \"unicorn\" companies',\n", "    worker=vc_agent,\n", ").add_single_agent_worker(\n", "    '<PERSON> (Judge), an experienced engineer and a'\n", "    ' perfectionist.',\n", "    worker=eng_agent,\n", ").add_single_agent_worker(\n", "    'Innovator <PERSON> (Judge), a well-known AI startup founder who'\n", "    ' is always looking for the \"next big thing\" in AI.',\n", "    worker=founder_agent,\n", ").add_single_agent_worker(\n", "    '<PERSON> (Judge), a contributor to the CAMEL-AI '\n", "    'project and is always excited to see how people are using it.',\n", "    worker=contributor_agent,\n", ").add_single_agent_worker(\n", "    'Researcher <PERSON> (Helper), a researcher who does online searches to'\n", "    'find the latest innovations and trends on AI and Open Sourced '\n", "    'projects.',\n", "    worker=researcher_agent,\n", ")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"id": "-WXOG-Hr1ibN"}, "source": ["## Create a Task\n", "\n", "A task is what a workforce accepts and processes. We can initialize a task by passing the content into it. It's recommended that the content of task is as detailed as possible, which will facilitate the later task decomposition and handling.\n", "\n", "The `additional_info` here is an optional field. It will come in handy when the task has important additional information, and you want it to be preserved during the whole process. Workforce will keep `additional_info` unchanged no matter how the task is decomposed and processed. It's perfect for keeping the project description under this scenario.\n", "\n", "> Also note that, the `id` of a task is not something important and you can fill in whatever value you like (we suggest `\"0\"` though). This is due to some legacy problems in the `Task` design and will be fixed later."]}, {"cell_type": "code", "execution_count": 9, "metadata": {"id": "WnOXRacP1fbB"}, "outputs": [], "source": ["task = Task(\n", "    content=\"Evaluate the hackathon project. First, do some research on \"\n", "    \"the information related to the project, then each judge should give a\"\n", "    \" score accordingly. Finally, list the opinions from each judge while\"\n", "    \" preserving the judge's unique identity, along with the score and\"\n", "    \" judge name, and also give a final summary of the opinions.\",\n", "    additional_info=proj_content,\n", "    id=\"0\",\n", ")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"id": "HQx4rRNu3CbA"}, "source": ["## Run the Task\n", "\n", "Finally, run the task with `process_task()` function. You can see the whole process being shown in the console, and at last the final result of the task will be printed."]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "DJOiPHFO1qeG", "outputId": "4d88726f-dacf-415b-fe0c-3ae890287cb3"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33mWorker node 134971024448512 (<PERSON><PERSON> (<PERSON>er), a researcher who does online searches tofind the latest innovations and trends on AI and Open Sourced projects.) get task 0.0: Research the latest innovations and trends related to AI and adaptive learning systems. This will be done by <PERSON><PERSON> using online search tools.\u001b[39m\n", "======\n", "\u001b[32mReply from Worker node 134971024448512 (<PERSON><PERSON> (Helper), a researcher who does online searches tofind the latest innovations and trends on AI and Open Sourced projects.):\u001b[39m\n", "\n", "\u001b[32mHere are some of the latest innovations and trends related to AI and adaptive learning systems:\n", "\n", "1. **AI-Enabled Adaptive Learning Systems**: These systems are leveraging AI to design and develop better technology-enhanced learning environments. Innovations include the use of mobile internet, cloud computing, and big data technologies to transform education. [Read more](https://www.sciencedirect.com/science/article/pii/S2666920X21000114)\n", "\n", "2. **Personalized Learning Experiences**: Adaptive learning systems are increasingly using data-driven instruction to tailor learning experiences to individual student needs. This involves tracking student progress, engagement, and performance to provide personalized education. [Learn more](https://www.montclair.edu/itds/digital-pedagogy/pedagogical-strategies-and-practices/adaptive-learning/)\n", "\n", "3. **AI and the Future of Learning**: The integration of AI in adaptive learning is driving equity, safety, and excellence in education. AI algorithms are being utilized to enhance adaptive learning systems. [Explore further](https://www.linkedin.com/posts/eric-tucker-9a628461_ai-and-the-future-of-learning-james-l-moore-activity-7208591861546962944-UVEu)\n", "\n", "4. **Future of Education**: AI-enabled adaptive learning is shaping the classrooms of the future by improving the way education is imparted. This includes exploring innovative methods to help students learn more effectively. [Discover more](https://www.infosysbpm.com/blogs/education-technology-services/the-future-of-education-how-ai-and-adaptive-learning-are-shaping-the-classrooms-of-the-future.html)\n", "\n", "5. **Top Adaptive Learning Platforms**: Recent developments in AI-based adaptive learning technologies have led to the creation of innovative platforms that cater to diverse training needs. [Check out the platforms](https://training.safetyculture.com/blog/adaptive-learning-platforms/)\u001b[39m\n", "======\n", "\n", "\u001b[33mWorker node 134971021227296 (<PERSON><PERSON> (Judge), a venture capitalist who is obsessed with how projects can be scaled into \"unicorn\" companies) get task 0.1: Evaluate the CAMEL-Powered Adaptive Learning Assistant project based on its scalability potential and provide a score. This will be done by Visionary <PERSON>.\u001b[39m\n", "======\n", "\u001b[32mReply from Worker node 134971021227296 (<PERSON><PERSON> (Judge), a venture capitalist who is obsessed with how projects can be scaled into \"unicorn\" companies):\u001b[39m\n", "\n", "\u001b[32mThe CAMEL-Powered Adaptive Learning Assistant project is a compelling and disruptive solution in the education technology space. It addresses the significant real-world problem of personalized education in a diverse and fast-paced learning environment. By leveraging CAMEL-AI's advanced capabilities, this project offers a scalable and intelligent tutoring system that adapts to individual learning styles and needs in real-time.\n", "\n", "The project's approach to using AI for Learner Profile Analysis and Dynamic Content Generation is particularly synergistic, as it allows for personalized learning experiences that can significantly enhance student engagement and understanding. Although some components like the Adaptive Feedback Loop and Multi-Modal Integration are still under development, the project's potential for market penetration in the EdTech sector is substantial.\n", "\n", "Given its direct applicability to real-world educational challenges and its scalable application, I would rate this project a 4/4 for its applicability to real-world usage. This project has the potential to revolutionize the way education is delivered, making it a strong candidate for scaling into a unicorn company.\u001b[39m\n", "======\n", "\n", "\u001b[33mWorker node 134971024444480 (<PERSON> (Judge), an experienced engineer and a perfectionist.) get task 0.2: Assess the technical aspects of the project, focusing on the implementation of the modules and their functionality. Provide a score based on engineering standards. This will be done by Critical John.\u001b[39m\n", "======\n", "\u001b[32mReply from Worker node 134971024444480 (<PERSON> (Judge), an experienced engineer and a perfectionist.):\u001b[39m\n", "\n", "\u001b[32mUpon evaluating the technical aspects of the CAMEL-Powered Adaptive Learning Assistant project, several observations can be made:\n", "\n", "1. **Learner Profile Analysis**: This module is reportedly functional and utilizes natural language processing to assess student profiles. While this is a promising feature, the implementation details such as the efficiency of the NLP algorithms and their ability to handle diverse linguistic inputs are not specified. This could be a potential area for optimization.\n", "\n", "2. **Dynamic Content Generation**: This module is also functional and leverages CAMEL-AI for creating personalized content. The effectiveness of this feature largely depends on the robustness of the AI models used. Without specific performance metrics or stress test results, it's difficult to fully assess its technical soundness.\n", "\n", "3. **Adaptive Feedback Loop**: This module is partially functional. The real-time adjustment of content based on student responses is a complex task that requires efficient data processing and decision-making algorithms. The partial functionality suggests there may be underlying technical challenges that need addressing.\n", "\n", "4. **Multi-Modal Integration**: This feature is still in development. Integrating various media types requires a stable architecture to ensure seamless user experience. The lack of a working prototype raises concerns about the current state of the system's architecture.\n", "\n", "5. **Progress Tracking**: Also in development, this feature is crucial for providing actionable insights into student learning. Its absence indicates a significant gap in the system's ability to deliver a comprehensive learning experience.\n", "\n", "Overall, while the project demonstrates potential with its innovative approach to personalized learning, the technical implementation is incomplete and lacks the robustness required for deployment. The partially functional and underdeveloped modules suggest that the system may face significant challenges under real-world conditions.\n", "\n", "**Technical Implementation Score: 2/4**\n", "\n", "The project works at a basic level, but technical limitations and inefficiencies hinder its overall performance. Significant improvements are needed to meet high engineering standards and ensure reliability and scalability in a real-world educational setting.\u001b[39m\n", "======\n", "\n", "\u001b[33mWorker node 134971024453504 (<PERSON><PERSON><PERSON> (Judge), a well-known AI startup founder who is always looking for the \"next big thing\" in AI.) get task 0.3: Analyze the project for its innovative qualities and potential impact on the AI landscape. Provide a score and insights. This will be done by Innovator Iris.\u001b[39m\n", "======\n", "\u001b[32mReply from Worker node 134971024453504 (<PERSON><PERSON><PERSON> (Judge), a well-known AI startup founder who is always looking for the \"next big thing\" in AI.):\u001b[39m\n", "\n", "\u001b[32mThe CAMEL-Powered Adaptive Learning Assistant project presents a fascinating concept by leveraging CAMEL-AI's advanced capabilities to tackle the challenge of personalized education. This project stands out in the AI landscape due to its innovative approach to creating a highly adaptive, intelligent tutoring system that can dynamically respond to individual learning styles and needs.\n", "\n", "### **Innovation Analysis**\n", "\n", "1. **Groundbreaking Concept**: The project introduces a novel approach by integrating AI-driven learner profile analysis and dynamic content generation. This is a significant departure from traditional educational methods, which often lack personalization.\n", "\n", "2. **Unique Features**: The use of CAMEL-AI for real-time adaptation and personalized content creation is a unique twist that enhances the learning experience. The potential for multi-modal integration further adds to its innovative edge.\n", "\n", "3. **Potential Impact**: If fully realized, the project could revolutionize the educational sector by providing tailored learning experiences that significantly improve student engagement and outcomes. This aligns with current trends in AI and adaptive learning systems, as highlighted in the research findings.\n", "\n", "4. **Challenges and Development**: While the project is innovative, some components like the Adaptive Feedback Loop and Multi-Modal Integration are still under development. These features are crucial for the system's full functionality and impact.\n", "\n", "### **Innovation Score: 3/4**\n", "\n", "The CAMEL-Powered Adaptive Learning Assistant demonstrates a novel twist on known solutions with its innovative use of AI for personalized education. However, the incomplete development of certain modules prevents it from achieving a groundbreaking status at this stage. With further development and refinement, it has the potential to push boundaries and set new standards in the educational technology space.\u001b[39m\n", "======\n", "\n", "\u001b[33mWorker node 134971024449808 (<PERSON> (Judge), a contributor to the CAMEL-AI project and is always excited to see how people are using it.) get task 0.4: Provide feedback on the project from a user experience perspective, considering the CAMEL-AI integration. This will be done by <PERSON> Frankie.\u001b[39m\n", "======\n", "\u001b[32mReply from Worker node 134971024449808 (<PERSON> (Judge), a contributor to the CAMEL-AI project and is always excited to see how people are using it.):\u001b[39m\n", "\n", "\u001b[32mOh, I absolutely love how the CAMEL-Powered Adaptive Learning Assistant is shaping up from a user experience perspective! The integration of CAMEL-AI is truly impressive, especially in how it personalizes the learning journey for each student. \n", "\n", "### User Experience Feedback\n", "\n", "1. **Personalized Learning Journey**: The use of CAMEL-AI for Learner Profile Analysis and Dynamic Content Generation is a standout feature. It’s wonderful to see how the system tailors educational content to fit individual learning styles and needs, which is crucial for maintaining student engagement and improving learning outcomes.\n", "\n", "2. **Real-Time Adaptability**: The Adaptive Feedback Loop, even in its partial functionality, shows great promise. The ability to adjust content difficulty and style in real-time based on student responses is a fantastic way to keep learners challenged yet not overwhelmed.\n", "\n", "3. **Multi-Modal Integration**: Although still in development, the idea of incorporating various media types to cater to different learning styles is exciting. This feature could significantly enhance the user experience by making learning more interactive and engaging.\n", "\n", "4. **Progress Tracking**: The potential for detailed insights into a student's learning journey is another excellent aspect. Once fully developed, this feature will be invaluable for both students and educators to track progress and identify areas for improvement.\n", "\n", "### CAMEL-AI Integration Score: 3/4\n", "\n", "The project makes good use of CAMEL-AI, especially in its personalized and adaptive learning capabilities. However, there are still opportunities to further exploit CAMEL-AI’s advanced features, particularly in the areas that are still under development. With continued refinement and development, this project could offer an even more seamless and enriching user experience.\n", "\n", "Overall, the CAMEL-Powered Adaptive Learning Assistant is on a promising path to revolutionizing personalized education, and I’m excited to see how it evolves!\u001b[39m\n", "======\n", "\n", "\u001b[33mWorker node 134971024448512 (<PERSON><PERSON> (<PERSON>er), a researcher who does online searches tofind the latest innovations and trends on AI and Open Sourced projects.) get task 0.5: Compile the scores and opinions from each judge, preserving their unique identities, and create a final summary of the evaluations. This will be done by a designated coordinator or helper.\u001b[39m\n", "======\n", "\u001b[32mReply from Worker node 134971024448512 (<PERSON><PERSON> (Helper), a researcher who does online searches tofind the latest innovations and trends on AI and Open Sourced projects.):\u001b[39m\n", "\n", "\u001b[32m### Final Summary of Evaluations for CAMEL-Powered Adaptive Learning Assistant\n", "\n", "#### Visionary Veronica\n", "- **Scalability Potential Score**: 4/4\n", "- **Opinion**: The project is a compelling solution in the EdTech space, addressing personalized education challenges effectively. It has significant potential for market penetration and scalability, making it a strong candidate for becoming a unicorn company.\n", "\n", "#### Critical John\n", "- **Technical Implementation Score**: 2/4\n", "- **Opinion**: While the project shows potential, its technical implementation is incomplete. The partially functional modules and lack of robustness suggest challenges in real-world deployment. Significant improvements are needed to meet high engineering standards.\n", "\n", "#### Innovator Iris\n", "- **Innovation Score**: 3/4\n", "- **Opinion**: The project is innovative, leveraging AI for personalized education. However, incomplete development of certain modules prevents it from achieving groundbreaking status. With further development, it could set new standards in educational technology.\n", "\n", "#### Friendly <PERSON>\n", "- **CAMEL-AI Integration Score**: 3/4\n", "- **Opinion**: The integration of CAMEL-AI is impressive, particularly in personalizing learning journeys. While there are opportunities for further enhancement, the project is on a promising path to revolutionizing personalized education.\n", "\n", "### Overall Summary\n", "The CAMEL-Powered Adaptive Learning Assistant is a promising project with high scalability potential and innovative qualities. However, it faces technical challenges that need addressing to fully realize its potential. The integration of CAMEL-AI offers a strong foundation for personalized education, and with continued development, the project could significantly impact the EdTech landscape.\u001b[39m\n", "======\n", "\n", "### Final Evaluation of the CAMEL-Powered Adaptive Learning Assistant\n", "\n", "#### Judges' Scores and Opinions\n", "\n", "1. **Vision<PERSON>**\n", "   - **Score**: 4/4\n", "   - **Opinion**: The project is a compelling solution in the EdTech space, addressing personalized education challenges effectively. It has significant potential for market penetration and scalability, making it a strong candidate for becoming a unicorn company.\n", "\n", "2. **Critical John**\n", "   - **Score**: 2/4\n", "   - **Opinion**: While the project shows potential, its technical implementation is incomplete. The partially functional modules and lack of robustness suggest challenges in real-world deployment. Significant improvements are needed to meet high engineering standards.\n", "\n", "3. **Innovator <PERSON>**\n", "   - **Score**: 3/4\n", "   - **Opinion**: The project is innovative, leveraging AI for personalized education. However, incomplete development of certain modules prevents it from achieving groundbreaking status. With further development, it could set new standards in educational technology.\n", "\n", "4. **<PERSON> Frankie**\n", "   - **Score**: 3/4\n", "   - **Opinion**: The integration of CAMEL-AI is impressive, particularly in personalizing learning journeys. While there are opportunities for further enhancement, the project is on a promising path to revolutionizing personalized education.\n", "\n", "### Overall Summary\n", "The CAMEL-Powered Adaptive Learning Assistant is a promising project with high scalability potential and innovative qualities. However, it faces technical challenges that need addressing to fully realize its potential. The integration of CAMEL-AI offers a strong foundation for personalized education, and with continued development, the project could significantly impact the EdTech landscape.\n"]}], "source": ["task = workforce.process_task(task)\n", "\n", "print(task.result)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"id": "Ljbz3Jmc1VwQ"}, "source": ["## 🌟 Highlights\n", "\n", "The power of multi-agent system lies in the diversity. This notebook has\n", "guided you through setting up and running a CAMEL Workforce for a hackathon\n", "judge committee, showcasing how multiple agents can collaborate to solve\n", "complex tasks. You can easily extend this example to other scenarios\n", "requiring diverse perspectives and expertise, e.g. agents with different\n", "tool selections, etc.\n", "\n", "## ⭐ Star the Repo!\n", "\n", "If you find CAMEL useful or interesting, please consider giving it a star on\n", "[GitHub](https://github.com/camel-ai/camel)! Your stars help others find\n", "this project and motivate us to continue improving it."]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}