{"cells": [{"cell_type": "markdown", "metadata": {"id": "SQMBlxsBv1cH"}, "source": ["# Real Function Calls and Hermes Format Data Generation\n"]}, {"cell_type": "markdown", "metadata": {"id": "FcppXQ5prrB8"}, "source": ["You can also check this cookbook in colab [here](https://colab.research.google.com/drive/1N7V4jFFMp4zW5nkIS-qeGDg2ovV1fspN?usp=sharing)"]}, {"cell_type": "markdown", "metadata": {"id": "sOwRrlSFr3Vr"}, "source": ["<div class=\"align-center\">\n", "  <a href=\"https://www.camel-ai.org/\"><img src=\"https://i.postimg.cc/KzQ5rfBC/button.png\"width=\"150\"></a>\n", "  <a href=\"https://discord.camel-ai.org\"><img src=\"https://i.postimg.cc/L4wPdG9N/join-2.png\"  width=\"150\"></a></a>\n", "  \n", "⭐ <i>Star us on [*Github*](https://github.com/camel-ai/camel), join our [*Discord*](https://discord.camel-ai.org) or follow our [*X*](https://x.com/camelaiorg)\n", "</div>"]}, {"cell_type": "markdown", "metadata": {"id": "AwnfLQvMvtXo"}, "source": ["This notebook provides a comprehensive guide to generating user queries and structured tool call data using CAMEL's ChatAgent framework. By utilizing real tools and the Hermes JSON format for function calls, the tutorial demonstrates a structured approach to scalable and flexible data generation.\n", "\n", "In this notebook, you'll explore:\n", "\n", "- CAMEL's ChatAgent Framework: A multi-agent system for generating human-like queries and structured tool call data, leveraging its modular and adaptable design.\n", "\n", "- Hermes Function Calling Format: A standardized JSON-based format for encoding function calls, ensuring consistency and interoperability in structured data outputs.\n", "\n", "- OpenAI API Integration: Enabling advanced natural language understanding and generation for crafting user queries and processing tool responses.\n", "\n", "- Toolkits Integration: Leveraging tools such as MathToolkit, SearchToolkit, and others to provide diverse functionalities for realistic scenarios.\n", "\n", "- Automated Data Generation: End-to-end pipeline for generating tool-specific user queries, structuring their outputs, and saving them as JSON files."]}, {"cell_type": "markdown", "metadata": {"id": "XuZwbB1IIuUi"}, "source": ["### Installation"]}, {"cell_type": "markdown", "metadata": {"id": "gIJK4Lu5Iwm0"}, "source": ["Ensure you have CAMEL AI and desired dependencies installed in your Python environment:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "id": "HTKnWg9Xv_y4"}, "outputs": [], "source": ["!pip install 'camel-ai[rag,web_tools]==0.2.18'"]}, {"cell_type": "markdown", "metadata": {"id": "HCqghaGiwkdv"}, "source": ["## Step 1: Import Required Libraries and Modules\n", "Start by importing necessary libraries and modules."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "bcNqpJShI5ml"}, "outputs": [], "source": ["import json\n", "from typing import Callable, List, Union, Any\n", "\n", "# Import necessary classes and functions from camel library\n", "from camel.agents import ChatAgent\n", "from camel.messages import FunctionCallingMessage\n", "from camel.messages import HermesFunctionFormatter\n", "from camel.messages import ShareGPTConversation\n", "from camel.messages import ShareGPTMessage\n", "from camel.models import ModelFactory\n", "from camel.toolkits import FunctionTool, MathToolkit, SearchToolkit, \\\n", "    RetrievalToolkit\n", "from camel.types import ModelPlatformType, ModelType\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "RxG99FkYwm_p", "outputId": "2d328a6b-658b-4d92-afb9-fbac39c87760"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter your OpenAI API key: ··········\n"]}], "source": ["import os\n", "from getpass import getpass\n", "# Prompt for the OpenAI API key securely\n", "openai_api_key = getpass('Enter your OpenAI API key: ')\n", "os.environ[\"OPENAI_API_KEY\"] = openai_api_key"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Alternatively, if running on Colab, you could save your API keys and tokens as **Colab Secrets**, and use them across notebooks.\n", "\n", "To do so, **comment out** the above **manual** API key prompt code block(s), and **uncomment** the following codeblock.\n", "\n", "⚠️ Don't forget granting access to the API key you would be using to the current notebook."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# from google.colab import userdata\n", "\n", "# os.environ[\"OPENAI_API_KEY\"] = userdata.get(\"OPENAI_API_KEY\")"]}, {"cell_type": "markdown", "metadata": {"id": "yOTVA2ttJDaK"}, "source": ["## Step 2: Define Function to Generate User Queries\n", "This function leverages specific tools to generate human-like queries. We’ll set up a ChatAgent to create relevant, tool-specific user queries."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "tOy3RpJ5JDvv"}, "outputs": [], "source": ["def generate_user_query(selected_tool: Union[Callable, FunctionTool],\n", "                        n: int = 1) -> List[str]:\n", "    r\"\"\"Generates user queries by leveraging specific tools, helping the\n", "    ChatAgent craft\n", "    human-like queries that take advantage of each tool's functionality.\n", "\n", "    Args:\n", "        selected_tool (Union[Callable, FunctionTool]): The tool to leverage for\n", "        query generation.\n", "        n (int, optional): The number of queries to generate. Defaults to 1.\n", "    \"\"\"\n", "\n", "    tool_call_sys_msg = (\n", "        \"You are a smart query generator designed to utilize specific tools \"\n", "        \"based on user needs. \"\n", "        \"Formulate queries as a human user would.\"\n", "\n", "        \"Instructions:\\n\"\n", "        \"1. Envision a real-world scenario, but don't say it out loud.\"\n", "        \"2. Craft a realistically phrased actionable query fitting that scenario\"\n", "        \" that could be satisfied with the provided tool(s)\\n\"\n", "        \"3. With the tool(s) in mind, phrase the query naturally and \"\n", "        \"informatively.\\n\"\n", "        \"4. Only rely on information from tools they appear likely to \"\n", "        \"provide\\n\"\n", "        \"5. Pose the query as if the user doesn't know what tools are \"\n", "        \"available.\"\n", "    )\n", "\n", "    # Convert to FunctionTool if necessary\n", "    if not isinstance(selected_tool, FunctionTool):\n", "        selected_tool = FunctionTool(selected_tool)\n", "\n", "    # Create a model instance for generating queries\n", "    query_model = ModelFactory.create(\n", "        model_platform=ModelPlatformType.OPENAI,\n", "        model_type=ModelType.GPT_4O_MINI,\n", "        model_config_dict={\"temperature\": 1}\n", "    )\n", "\n", "    # Initialize ChatAgent with the system message\n", "    query_agent = ChatAgent(\n", "        system_message=tool_call_sys_msg,\n", "        model=query_model,\n", "    )\n", "    queries = []\n", "\n", "    # Prepare tools info message for guiding query generation\n", "    tools_info_message = (\n", "            \"Generate a relevant query based on the following tool \"\n", "            \"details:\\n\\n\" +\n", "            \"\\n\".join(f\"Tool Schema: {selected_tool.get_openai_tool_schema()}\")\n", "    )\n", "\n", "    # Generate queries\n", "    for _ in range(n):\n", "        response = query_agent.step(tools_info_message)\n", "        queries.append(response.msgs[0].content)  # Extract the generated query\n", "\n", "    return queries\n"]}, {"cell_type": "markdown", "metadata": {"id": "F2mFLXJBJIUc"}, "source": ["## Step 3: Define Function to Generate Structured Tool Call Data\n", "This function will structure tool call data based on user queries by leveraging each selected tool."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "3z4OSBY7JKCs"}, "outputs": [], "source": ["def generate_tool_call_data(user_messages: List[str],\n", "                            selected_tool: Union[Callable, FunctionTool]) -> \\\n", "        list[Any]:\n", "    r\"\"\"Generates structured tool call data for a list of user messages by\n", "    using each specified tool in selected_tools.\n", "    \"\"\"\n", "\n", "    # Convert to FunctionTool if necessary\n", "    if not isinstance(selected_tool, FunctionTool):\n", "        selected_tool = FunctionTool(selected_tool)\n", "\n", "    # Define system message guiding ChatAgent on function calls\n", "    base_system_msg = \"You are a function calling AI model. \"\n", "    hermes_tool_call_sys_msg = (\n", "        f\"You are a function calling AI model. You are provided with \"\n", "        f\"function signatures within <tools> </tools> XML tags. You may call \"\n", "        f\"one or more functions to assist with the user query. If available \"\n", "        f\"tools are not relevant in assisting with user query, just respond \"\n", "        f\"in natural conversational language. Don't make assumptions about \"\n", "        f\"what values to plug into functions. After calling & executing the \"\n", "        f\"functions, you will be provided with function results within \"\n", "        f\"<tool_response> </tool_response> XML tags.\"\n", "        f\"\\n <tools> \\n\"\n", "        f\"{[selected_tool.get_openai_tool_schema()]}\"\n", "        f\"\\n </tools>\\n\"\n", "        \"For each function call return a JSON object, with the following \"\n", "        \"pydantic model json schema:{'title': 'FunctionCall', 'type': \"\n", "        \"'object', 'properties': {'name': {'title': 'Name', 'type': \"\n", "        \"'string'}, 'arguments': {'title': 'Arguments', 'type': 'object'}}, \"\n", "        \"'required': ['arguments', 'name']}\\n\"\n", "        f\"Each function call should be enclosed within <tool_call> \"\n", "        f\"</tool_call> XML tags.\\n\"\n", "        f\"Example:\\n\"\n", "        f\"<tool_call>\\n\"\n", "        \"{'name': <function-name>, 'arguments': <args-dict>}\\n\"\n", "        \"</tool_call>\"\n", "        f\"\")\n", "    sys_hermes = ShareGPTMessage(from_='system',\n", "                                 value=hermes_tool_call_sys_msg)\n", "    # Initialize model for tool call data generation\n", "    tool_model = ModelFactory.create(\n", "        model_platform=ModelPlatformType.OPENAI,\n", "        model_type=ModelType.GPT_4O_MINI,\n", "    )\n", "\n", "    all_tool_data = {}\n", "\n", "    tool_output_data = []\n", "    for user_message in user_messages:\n", "        # Set up ChatAgent with the system message, model, and the single tool\n", "        tool_agent = ChatAgent(\n", "            system_message=base_system_msg,\n", "            model=tool_model,\n", "            tools=[selected_tool],\n", "        )\n", "\n", "        # Generate response using ChatAgent and structured output\n", "        try:\n", "            tool_agent.step(user_message)\n", "        except Exception as e:\n", "            print(f\"Error: {e}\")\n", "            continue\n", "        messages = [record.memory_record.message for record in\n", "                    tool_agent.memory.retrieve()]\n", "\n", "        sharegpt_hermes_msgs = \\\n", "            [sys_hermes] + [msg.to_sharegpt(HermesFunctionFormatter()) for msg\n", "                            in messages[1:]]\n", "\n", "        # Only include conversations with function calls\n", "        if any(type(message) == FunctionCallingMessage for message in\n", "               messages):\n", "            tool_output_data.append(\n", "                json.loads(\n", "                    ShareGPTConversation(sharegpt_hermes_msgs)\n", "                    .model_dump_json(by_alias=True))\n", "            )\n", "\n", "    # Add generated data to the dictionary with tool name as the key\n", "    all_tool_data[selected_tool.func.__name__] = tool_output_data\n", "\n", "    return tool_output_data\n"]}, {"cell_type": "markdown", "metadata": {"id": "_jF9tib9JKV7"}, "source": ["## Step 4: Initialize Toolkits and Define Tools List\n", "We’ll set up toolkits we want to be used"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "7NQHlG6yJL-T"}, "outputs": [], "source": ["# API keys can be setup in the notebook like this\n", "# google_api_key = getpass('Enter your API key: ')\n", "# os.environ[\"GOOGLE_API_KEY\"] = google_api_key\n", "# weather_api_key = getpass('Enter your API key: ')\n", "# os.environ[\"OPENWEATHERMAP_API_KEY\"] = weather_api_key\n", "\n", "selected_tools = [\n", "    *MathToolkit().get_tools(),\n", "    # Add more tools as needed, though they require API keys\n", "    *[  # Search tools with no API keys required\n", "        FunctionTool(SearchToolkit().search_duckduckgo),\n", "        FunctionTool(SearchToolkit().search_wiki),\n", "    ],\n", "    # FunctionTool(SearchToolkit().search_google),\n", "    # *ArxivToolkit().get_tools(),\n", "    # *GoogleMapsToolkit().get_tools(),\n", "    # *WeatherToolkit().get_tools(),\n", "]\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "ydfSYIpsJNnG"}, "source": ["## Step 5: Generate Data and Save to JSON\n", "We now loop through each tool, generate queries, create tool call data, and save it all in JSON format."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "-aEQRLJuJPUi"}, "outputs": [], "source": ["results = {\n", "    \"generated_queries\": [],\n", "    \"tool_call_data\": []\n", "}\n", "\n", "for selected_tool in selected_tools:\n", "    user_queries = generate_user_query(selected_tool=selected_tool, n=5)\n", "    tool_call_data = generate_tool_call_data(user_queries, selected_tool)\n", "\n", "    # Append results to the lists instead of overwriting\n", "    results[\"generated_queries\"].extend(user_queries)\n", "    results[\"tool_call_data\"].extend(tool_call_data)\n", "\n", "# Specify output file path\n", "output_file = \"generated_tool_call_data.json\"\n", "\n", "# Save data to JSON file\n", "with open(output_file, \"w\") as f:\n", "    json.dump(results, f, indent=4)\n", "\n", "print(f\"Data saved to {output_file}\")\n"]}, {"cell_type": "markdown", "metadata": {"id": "qiEYiaq0JQuZ"}, "source": ["## Step 6: Verify the JSON Output\n", "Open the generated JSON file and verify that the tool call data has been saved correctly."]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "fcTxbdqKJR8y", "outputId": "375084be-e640-4651-f03e-2787da9556f5"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sample data: [\n", "    \"Could you help me by adding two numbers together? I'd like to know the sum of the first number and the second number I provide.\",\n", "    \"I'm looking to add two numbers together. Can you help me with the calculation by providing the sum when I give you the first and second numbers?\",\n", "    \"Can you assist me in performing a simple addition by providing the sum of two numbers? I will give you the first and second numbers that I want to add together.\",\n", "    \"Could you please help me add two numbers together? I'll provide the first number and the second number, and I'd like to know the result of their addition.\",\n", "    \"Can you help me calculate the sum of two numbers? I would like to provide the first number and the second number, and I need the total of their addition.\",\n", "    \"I'm trying to perform a subtraction operation but I'm unsure how to go about it. Can you help me with how to subtract one number from another? For example, if I wanted to subtract 5 from 10, what would the result be?\",\n", "    \"I need to perform a subtraction of two numbers, but I'm not sure how to get the result. Can you assist me in figuring out how to subtract one number from another? For instance, if I want to subtract 8 from 12, what would be the answer?\",\n", "    \"I'm looking to perform a subtraction operation between two numbers but I need some guidance. Could you help me find out how to subtract one number from another? For instance, if I have the numbers 15 and 7, what would be the result of subtracting 7 from 15?\",\n", "    \"I need some help performing a subtraction. Can you explain how to subtract one number from another? For example, if I have 20 and I want to subtract 4, what would the result be?\",\n", "    \"I would like to perform a subtraction operation between two numbers, but I'm not certain how to do that. Could you help me understand how to subtract one number from another? For instance, what would be the result if I subtract 10 from 25?\",\n", "    \"Can you help me multiply two numbers together? I\\u2019d like to know the result, and if possible, please let me specify how many decimal places I want in the answer.\",\n", "    \"I need to multiply two numbers, but I'm also interested in the answer rounded to a specific number of decimal places. Can you help me with that?\",\n", "    \"Could you assist me with multiplying two numbers? I also want to have control over how many decimal places the result should be rounded to.\",\n", "    \"I need help multiplying two numbers together, and I'd like to round the result to a specific number of decimal places. Can you provide assistance with that?\",\n", "    \"Can you help me multiply two numbers together? I would also like to specify how many decimal places the result should be rounded to.\",\n", "    \"Can you help me divide two numbers and specify the number of decimal places I want in the result? For example, I have a dividend and a divisor, and I would like to see the answer rounded to a certain number of decimal places.\",\n", "    \"I need to perform a division operation where I can input a dividend and a divisor. Moreover, I want to specify how many decimal places the result should be rounded to. Could you assist me with that?\",\n", "    \"I'm looking to divide two numbers where I can enter the dividend and the divisor. Additionally, I would like to specify the number of decimal places I want the answer rounded to. Can you assist me with this calculation?\",\n", "    \"I need help dividing two numbers and would like to specify how many decimal places the result should be rounded to. Can you assist me with entering the dividend, the divisor, and the desired decimal precision for the result?\",\n", "    \"Can you help me divide two numbers? I would like to provide a dividend and a divisor, and also specify the number of decimal places I want the result rounded to. What details do you need from me to get this calculation done?\",\n", "    \"I'm working on a financial report and need to round a number to two decimal places for accuracy in my calculations. Can you help me round the number 123.45678 to two decimal places?\",\n", "    \"I'm preparing some data for a presentation and need to ensure the figures are shown with precision. Can you help me round the number 57.895 to 1 decimal place?\",\n", "    \"I'm working on a financial analysis and need to present a specific number rounded properly. Can you round the number 89.999 to two decimal places for me?\",\n", "    \"I'm analyzing some survey data and need to display the results with a specific level of precision. Can you round the number 45.6789 to three decimal places for me?\",\n", "    \"I need to calculate a final score for a project and would like to round the number 72.3456 to one decimal place. Can you help me with that?\",\n", "    \"I'm looking to conduct some research on the concept of mindfulness. Could you help me find some articles or resources related to mindfulness using a search tool? Ideally, I'd like to see about five results to start with.\",\n", "    \"I'm interested in exploring the impact of social media on mental health. Can you help me find some articles or resources about this topic? I'd like to have around five results to get started.\",\n", "    \"I'm looking to understand the benefits of regular exercise on mental health. Can you find me some articles or resources about this? I would appreciate having around five results to browse through.\",\n", "    \"I'm researching the effects of sleep deprivation on cognitive function. Can you help me find some articles or resources about this topic? I would like to see around five results to review.\",\n", "    \"I'm trying to find resources on the relationship between gratitude and happiness. Can you help me search for some articles or information on this topic? I would like to see about five relevant results.\",\n", "    \"I'm looking to find some detailed information about a specific historical event. Can you help me search for it on Wikipedia and provide a summary of what I find? The event I'm interested in is the signing of the Treaty of Versailles.\",\n", "    \"Could you help me look up information about the Great Wall of China on Wikipedia? I'm interested in a summary that covers its history, construction, and significance.\",\n", "    \"Can you search for information on <PERSON> in Wikipedia and provide me with a summary of his life, major contributions, and theories?\",\n", "    \"Could you help me find a summary about the planet Mars on Wikipedia? I'm looking for information on its characteristics, exploration history, and any interesting facts.\",\n", "    \"Can you look up the topic of climate change on Wikipedia and provide me with a summary that highlights its causes, effects, and possible solutions?\"\n", "]\n", "\n", "Sample tool call data: [\n", "    [\n", "        {\n", "            \"from\": \"system\",\n", "            \"value\": \"You are a function calling AI model. You are provided with function signatures within <tools> </tools> XML tags. You may call one or more functions to assist with the user query. If available tools are not relevant in assisting with user query, just respond in natural conversational language. Don't make assumptions about what values to plug into functions. After calling & executing the functions, you will be provided with function results within <tool_response> </tool_response> XML tags.\\n <tools> \\n[{'type': 'function', 'function': {'name': 'sub', 'description': 'Do subtraction between two numbers.', 'strict': True, 'parameters': {'properties': {'a': {'type': 'number', 'description': 'The minuend in subtraction.'}, 'b': {'type': 'number', 'description': 'The subtrahend in subtraction.'}}, 'required': ['a', 'b'], 'type': 'object', 'additionalProperties': False}}}]\\n </tools>\\nFor each function call return a JSON object, with the following pydantic model json schema:{'title': 'FunctionCall', 'type': 'object', 'properties': {'name': {'title': 'Name', 'type': 'string'}, 'arguments': {'title': 'Arguments', 'type': 'object'}}, 'required': ['arguments', 'name']}\\nEach function call should be enclosed within <tool_call> </tool_call> XML tags.\\nExample:\\n<tool_call>\\n{'name': <function-name>, 'arguments': <args-dict>}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"human\",\n", "            \"value\": \"I need to perform a subtraction of two numbers, but I'm not sure how to get the result. Can you assist me in figuring out how to subtract one number from another? For instance, if I want to subtract 8 from 12, what would be the answer?\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"<tool_call>\\n{'name': 'sub', 'arguments': {'a': 12, 'b': 8}}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"tool\",\n", "            \"value\": \"<tool_response>\\n{'name': 'sub', 'content': 4}\\n</tool_response>\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"To subtract 8 from 12, you simply perform the calculation \\\\( 12 - 8 \\\\), which results in 4.\"\n", "        }\n", "    ],\n", "    [\n", "        {\n", "            \"from\": \"system\",\n", "            \"value\": \"You are a function calling AI model. You are provided with function signatures within <tools> </tools> XML tags. You may call one or more functions to assist with the user query. If available tools are not relevant in assisting with user query, just respond in natural conversational language. Don't make assumptions about what values to plug into functions. After calling & executing the functions, you will be provided with function results within <tool_response> </tool_response> XML tags.\\n <tools> \\n[{'type': 'function', 'function': {'name': 'sub', 'description': 'Do subtraction between two numbers.', 'strict': True, 'parameters': {'properties': {'a': {'type': 'number', 'description': 'The minuend in subtraction.'}, 'b': {'type': 'number', 'description': 'The subtrahend in subtraction.'}}, 'required': ['a', 'b'], 'type': 'object', 'additionalProperties': False}}}]\\n </tools>\\nFor each function call return a JSON object, with the following pydantic model json schema:{'title': 'FunctionCall', 'type': 'object', 'properties': {'name': {'title': 'Name', 'type': 'string'}, 'arguments': {'title': 'Arguments', 'type': 'object'}}, 'required': ['arguments', 'name']}\\nEach function call should be enclosed within <tool_call> </tool_call> XML tags.\\nExample:\\n<tool_call>\\n{'name': <function-name>, 'arguments': <args-dict>}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"human\",\n", "            \"value\": \"I'm looking to perform a subtraction operation between two numbers but I need some guidance. Could you help me find out how to subtract one number from another? For instance, if I have the numbers 15 and 7, what would be the result of subtracting 7 from 15?\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"<tool_call>\\n{'name': 'sub', 'arguments': {'a': 15, 'b': 7}}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"tool\",\n", "            \"value\": \"<tool_response>\\n{'name': 'sub', 'content': 8}\\n</tool_response>\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"To subtract 7 from 15, you simply perform the operation \\\\( 15 - 7 \\\\), which results in 8.\"\n", "        }\n", "    ],\n", "    [\n", "        {\n", "            \"from\": \"system\",\n", "            \"value\": \"You are a function calling AI model. You are provided with function signatures within <tools> </tools> XML tags. You may call one or more functions to assist with the user query. If available tools are not relevant in assisting with user query, just respond in natural conversational language. Don't make assumptions about what values to plug into functions. After calling & executing the functions, you will be provided with function results within <tool_response> </tool_response> XML tags.\\n <tools> \\n[{'type': 'function', 'function': {'name': 'round', 'description': 'Rounds a number to a specified number of decimal places.', 'strict': True, 'parameters': {'properties': {'a': {'type': 'number', 'description': 'The number to be rounded.'}, 'decimal_places': {'type': 'integer', 'description': 'The number of decimal places\\\\nto round to. Defaults to 0.'}}, 'required': ['a', 'decimal_places'], 'type': 'object', 'additionalProperties': False}}}]\\n </tools>\\nFor each function call return a JSON object, with the following pydantic model json schema:{'title': 'FunctionCall', 'type': 'object', 'properties': {'name': {'title': 'Name', 'type': 'string'}, 'arguments': {'title': 'Arguments', 'type': 'object'}}, 'required': ['arguments', 'name']}\\nEach function call should be enclosed within <tool_call> </tool_call> XML tags.\\nExample:\\n<tool_call>\\n{'name': <function-name>, 'arguments': <args-dict>}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"human\",\n", "            \"value\": \"I'm working on a financial report and need to round a number to two decimal places for accuracy in my calculations. Can you help me round the number 123.45678 to two decimal places?\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"<tool_call>\\n{'name': 'round', 'arguments': {'a': 123.45678, 'decimal_places': 2}}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"tool\",\n", "            \"value\": \"<tool_response>\\n{'name': 'round', 'content': 123.46}\\n</tool_response>\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"The number 123.45678 rounded to two decimal places is 123.46.\"\n", "        }\n", "    ],\n", "    [\n", "        {\n", "            \"from\": \"system\",\n", "            \"value\": \"You are a function calling AI model. You are provided with function signatures within <tools> </tools> XML tags. You may call one or more functions to assist with the user query. If available tools are not relevant in assisting with user query, just respond in natural conversational language. Don't make assumptions about what values to plug into functions. After calling & executing the functions, you will be provided with function results within <tool_response> </tool_response> XML tags.\\n <tools> \\n[{'type': 'function', 'function': {'name': 'round', 'description': 'Rounds a number to a specified number of decimal places.', 'strict': True, 'parameters': {'properties': {'a': {'type': 'number', 'description': 'The number to be rounded.'}, 'decimal_places': {'type': 'integer', 'description': 'The number of decimal places\\\\nto round to. Defaults to 0.'}}, 'required': ['a', 'decimal_places'], 'type': 'object', 'additionalProperties': False}}}]\\n </tools>\\nFor each function call return a JSON object, with the following pydantic model json schema:{'title': 'FunctionCall', 'type': 'object', 'properties': {'name': {'title': 'Name', 'type': 'string'}, 'arguments': {'title': 'Arguments', 'type': 'object'}}, 'required': ['arguments', 'name']}\\nEach function call should be enclosed within <tool_call> </tool_call> XML tags.\\nExample:\\n<tool_call>\\n{'name': <function-name>, 'arguments': <args-dict>}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"human\",\n", "            \"value\": \"I'm preparing some data for a presentation and need to ensure the figures are shown with precision. Can you help me round the number 57.895 to 1 decimal place?\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"<tool_call>\\n{'name': 'round', 'arguments': {'a': 57.895, 'decimal_places': 1}}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"tool\",\n", "            \"value\": \"<tool_response>\\n{'name': 'round', 'content': 57.9}\\n</tool_response>\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"The number 57.895 rounded to 1 decimal place is 57.9.\"\n", "        }\n", "    ],\n", "    [\n", "        {\n", "            \"from\": \"system\",\n", "            \"value\": \"You are a function calling AI model. You are provided with function signatures within <tools> </tools> XML tags. You may call one or more functions to assist with the user query. If available tools are not relevant in assisting with user query, just respond in natural conversational language. Don't make assumptions about what values to plug into functions. After calling & executing the functions, you will be provided with function results within <tool_response> </tool_response> XML tags.\\n <tools> \\n[{'type': 'function', 'function': {'name': 'round', 'description': 'Rounds a number to a specified number of decimal places.', 'strict': True, 'parameters': {'properties': {'a': {'type': 'number', 'description': 'The number to be rounded.'}, 'decimal_places': {'type': 'integer', 'description': 'The number of decimal places\\\\nto round to. Defaults to 0.'}}, 'required': ['a', 'decimal_places'], 'type': 'object', 'additionalProperties': False}}}]\\n </tools>\\nFor each function call return a JSON object, with the following pydantic model json schema:{'title': 'FunctionCall', 'type': 'object', 'properties': {'name': {'title': 'Name', 'type': 'string'}, 'arguments': {'title': 'Arguments', 'type': 'object'}}, 'required': ['arguments', 'name']}\\nEach function call should be enclosed within <tool_call> </tool_call> XML tags.\\nExample:\\n<tool_call>\\n{'name': <function-name>, 'arguments': <args-dict>}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"human\",\n", "            \"value\": \"I'm working on a financial analysis and need to present a specific number rounded properly. Can you round the number 89.999 to two decimal places for me?\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"<tool_call>\\n{'name': 'round', 'arguments': {'a': 89.999, 'decimal_places': 2}}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"tool\",\n", "            \"value\": \"<tool_response>\\n{'name': 'round', 'content': 90.0}\\n</tool_response>\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"The number 89.999 rounded to two decimal places is 90.00.\"\n", "        }\n", "    ],\n", "    [\n", "        {\n", "            \"from\": \"system\",\n", "            \"value\": \"You are a function calling AI model. You are provided with function signatures within <tools> </tools> XML tags. You may call one or more functions to assist with the user query. If available tools are not relevant in assisting with user query, just respond in natural conversational language. Don't make assumptions about what values to plug into functions. After calling & executing the functions, you will be provided with function results within <tool_response> </tool_response> XML tags.\\n <tools> \\n[{'type': 'function', 'function': {'name': 'round', 'description': 'Rounds a number to a specified number of decimal places.', 'strict': True, 'parameters': {'properties': {'a': {'type': 'number', 'description': 'The number to be rounded.'}, 'decimal_places': {'type': 'integer', 'description': 'The number of decimal places\\\\nto round to. Defaults to 0.'}}, 'required': ['a', 'decimal_places'], 'type': 'object', 'additionalProperties': False}}}]\\n </tools>\\nFor each function call return a JSON object, with the following pydantic model json schema:{'title': 'FunctionCall', 'type': 'object', 'properties': {'name': {'title': 'Name', 'type': 'string'}, 'arguments': {'title': 'Arguments', 'type': 'object'}}, 'required': ['arguments', 'name']}\\nEach function call should be enclosed within <tool_call> </tool_call> XML tags.\\nExample:\\n<tool_call>\\n{'name': <function-name>, 'arguments': <args-dict>}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"human\",\n", "            \"value\": \"I'm analyzing some survey data and need to display the results with a specific level of precision. Can you round the number 45.6789 to three decimal places for me?\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"<tool_call>\\n{'name': 'round', 'arguments': {'a': 45.6789, 'decimal_places': 3}}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"tool\",\n", "            \"value\": \"<tool_response>\\n{'name': 'round', 'content': 45.679}\\n</tool_response>\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"The number 45.6789 rounded to three decimal places is 45.679.\"\n", "        }\n", "    ],\n", "    [\n", "        {\n", "            \"from\": \"system\",\n", "            \"value\": \"You are a function calling AI model. You are provided with function signatures within <tools> </tools> XML tags. You may call one or more functions to assist with the user query. If available tools are not relevant in assisting with user query, just respond in natural conversational language. Don't make assumptions about what values to plug into functions. After calling & executing the functions, you will be provided with function results within <tool_response> </tool_response> XML tags.\\n <tools> \\n[{'type': 'function', 'function': {'name': 'round', 'description': 'Rounds a number to a specified number of decimal places.', 'strict': True, 'parameters': {'properties': {'a': {'type': 'number', 'description': 'The number to be rounded.'}, 'decimal_places': {'type': 'integer', 'description': 'The number of decimal places\\\\nto round to. Defaults to 0.'}}, 'required': ['a', 'decimal_places'], 'type': 'object', 'additionalProperties': False}}}]\\n </tools>\\nFor each function call return a JSON object, with the following pydantic model json schema:{'title': 'FunctionCall', 'type': 'object', 'properties': {'name': {'title': 'Name', 'type': 'string'}, 'arguments': {'title': 'Arguments', 'type': 'object'}}, 'required': ['arguments', 'name']}\\nEach function call should be enclosed within <tool_call> </tool_call> XML tags.\\nExample:\\n<tool_call>\\n{'name': <function-name>, 'arguments': <args-dict>}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"human\",\n", "            \"value\": \"I need to calculate a final score for a project and would like to round the number 72.3456 to one decimal place. Can you help me with that?\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"<tool_call>\\n{'name': 'round', 'arguments': {'a': 72.3456, 'decimal_places': 1}}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"tool\",\n", "            \"value\": \"<tool_response>\\n{'name': 'round', 'content': 72.3}\\n</tool_response>\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"The rounded score is 72.3.\"\n", "        }\n", "    ],\n", "    [\n", "        {\n", "            \"from\": \"system\",\n", "            \"value\": \"You are a function calling AI model. You are provided with function signatures within <tools> </tools> XML tags. You may call one or more functions to assist with the user query. If available tools are not relevant in assisting with user query, just respond in natural conversational language. Don't make assumptions about what values to plug into functions. After calling & executing the functions, you will be provided with function results within <tool_response> </tool_response> XML tags.\\n <tools> \\n[{'type': 'function', 'function': {'name': 'search_duckduckgo', 'description': 'Use DuckDuckGo search engine to search information for\\\\nthe given query.\\\\n\\\\nThis function queries the DuckDuckGo API for related topics to\\\\nthe given search term. The results are formatted into a list of\\\\ndictionaries, each representing a search result.', 'strict': True, 'parameters': {'properties': {'query': {'type': 'string', 'description': 'The query to be searched.'}, 'source': {'type': 'string', 'description': 'The type of information to query (e.g., \\\"text\\\",\\\\n\\\"images\\\", \\\"videos\\\"). Defaults to \\\"text\\\".'}, 'max_results': {'type': 'integer', 'description': 'Max number of results, defaults to `5`.'}}, 'required': ['query', 'source', 'max_results'], 'type': 'object', 'additionalProperties': False}}}]\\n </tools>\\nFor each function call return a JSON object, with the following pydantic model json schema:{'title': 'FunctionCall', 'type': 'object', 'properties': {'name': {'title': 'Name', 'type': 'string'}, 'arguments': {'title': 'Arguments', 'type': 'object'}}, 'required': ['arguments', 'name']}\\nEach function call should be enclosed within <tool_call> </tool_call> XML tags.\\nExample:\\n<tool_call>\\n{'name': <function-name>, 'arguments': <args-dict>}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"human\",\n", "            \"value\": \"I'm looking to conduct some research on the concept of mindfulness. Could you help me find some articles or resources related to mindfulness using a search tool? Ideally, I'd like to see about five results to start with.\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"<tool_call>\\n{'name': 'search_duckduckgo', 'arguments': {'query': 'mindfulness', 'source': 'text', 'max_results': 5}}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"tool\",\n", "            \"value\": \"<tool_response>\\n{'name': 'search_duckduckgo', 'content': [{'result_id': 1, 'title': 'Mindfulness - Psychology Today', 'description': \\\"Mindfulness is a state of active, open attention to the present, observing one's thoughts and feelings without judging them. Learn about the history, benefits, and practices of mindfulness, and how it can help with stress, anxiety, pain, and more.\\\", 'url': 'https://www.psychologytoday.com/us/basics/mindfulness'}, {'result_id': 2, 'title': 'Mindfulness exercises - Mayo Clinic', 'description': \\\"Simple mindfulness exercises can be practiced anywhere and anytime. Research indicates that engaging your senses outdoors is especially beneficial. For more structured mindfulness exercises, such as body scan meditation or sitting meditation, you'll need to set aside time when you can be in a quiet place without distractions or interruptions. ...\\\", 'url': 'https://www.mayoclinic.org/healthy-lifestyle/consumer-health/in-depth/mindfulness-exercises/art-20046356'}, {'result_id': 3, 'title': 'What is Mindfulness: Benefits, How to Practice, and More - Healthline', 'description': 'Formal investigations into mindfulness in the Western world began in 1979 when <PERSON> developed what would become the Mindfulness-Based Stress Reduction (MBSR) program at the University ...', 'url': 'https://www.healthline.com/health/mind-body/what-is-mindfulness'}, {'result_id': 4, 'title': 'What is Mindfulness? - Mindful', 'description': \\\"What is mindfulness? Are you supposed to clear your mind, or focus on one thing? Here's the Mindful definition of Mindfulness.\\\", 'url': 'https://www.mindful.org/what-is-mindfulness/'}, {'result_id': 5, 'title': 'Mindfulness Definition | What Is Mindfulness - Greater Good', 'description': 'Mindfulness means maintaining a moment-by-moment awareness of our thoughts, feelings, bodily sensations, and surrounding environment, through a gentle, nurturing lens. Mindfulness also involves acceptance, meaning that we pay attention to our thoughts and feelings without judging them\\u2014without believing, for instance, that there\\\\'s a \\\"right\\\" or \\\"wrong\\\" way to think or feel in a given ...', 'url': 'https://greatergood.berkeley.edu/topic/mindfulness/definition'}]}\\n</tool_response>\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"Here are some articles and resources related to mindfulness:\\n\\n1. **[Mindfulness - Psychology Today](https://www.psychologytoday.com/us/basics/mindfulness)**  \\n   Description: Mindfulness is a state of active, open attention to the present, observing one's thoughts and feelings without judging them. Learn about the history, benefits, and practices of mindfulness, and how it can help with stress, anxiety, pain, and more.\\n\\n2. **[Mindfulness exercises - Mayo Clinic](https://www.mayoclinic.org/healthy-lifestyle/consumer-health/in-depth/mindfulness-exercises/art-20046356)**  \\n   Description: Simple mindfulness exercises can be practiced anywhere and anytime. Research indicates that engaging your senses outdoors is especially beneficial. For more structured mindfulness exercises, such as body scan meditation or sitting meditation, you'll need to set aside time when you can be in a quiet place without distractions or interruptions.\\n\\n3. **[What is Mindfulness: Benefits, How to Practice, and More - Healthline](https://www.healthline.com/health/mind-body/what-is-mindfulness)**  \\n   Description: Formal investigations into mindfulness in the Western world began in 1979 when <PERSON> developed what would become the Mindfulness-Based Stress Reduction (MBSR) program at the University.\\n\\n4. **[What is Mindfulness? - Mindful](https://www.mindful.org/what-is-mindfulness/)**  \\n   Description: What is mindfulness? Are you supposed to clear your mind, or focus on one thing? Here's the Mindful definition of Mindfulness.\\n\\n5. **[Mindfulness Definition | What Is Mindfulness - Greater Good](https://greatergood.berkeley.edu/topic/mindfulness/definition)**  \\n   Description: Mindfulness means maintaining a moment-by-moment awareness of our thoughts, feelings, bodily sensations, and surrounding environment, through a gentle, nurturing lens. Mindfulness also involves acceptance, meaning that we\"\n", "        }\n", "    ],\n", "    [\n", "        {\n", "            \"from\": \"system\",\n", "            \"value\": \"You are a function calling AI model. You are provided with function signatures within <tools> </tools> XML tags. You may call one or more functions to assist with the user query. If available tools are not relevant in assisting with user query, just respond in natural conversational language. Don't make assumptions about what values to plug into functions. After calling & executing the functions, you will be provided with function results within <tool_response> </tool_response> XML tags.\\n <tools> \\n[{'type': 'function', 'function': {'name': 'search_duckduckgo', 'description': 'Use DuckDuckGo search engine to search information for\\\\nthe given query.\\\\n\\\\nThis function queries the DuckDuckGo API for related topics to\\\\nthe given search term. The results are formatted into a list of\\\\ndictionaries, each representing a search result.', 'strict': True, 'parameters': {'properties': {'query': {'type': 'string', 'description': 'The query to be searched.'}, 'source': {'type': 'string', 'description': 'The type of information to query (e.g., \\\"text\\\",\\\\n\\\"images\\\", \\\"videos\\\"). Defaults to \\\"text\\\".'}, 'max_results': {'type': 'integer', 'description': 'Max number of results, defaults to `5`.'}}, 'required': ['query', 'source', 'max_results'], 'type': 'object', 'additionalProperties': False}}}]\\n </tools>\\nFor each function call return a JSON object, with the following pydantic model json schema:{'title': 'FunctionCall', 'type': 'object', 'properties': {'name': {'title': 'Name', 'type': 'string'}, 'arguments': {'title': 'Arguments', 'type': 'object'}}, 'required': ['arguments', 'name']}\\nEach function call should be enclosed within <tool_call> </tool_call> XML tags.\\nExample:\\n<tool_call>\\n{'name': <function-name>, 'arguments': <args-dict>}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"human\",\n", "            \"value\": \"I'm interested in exploring the impact of social media on mental health. Can you help me find some articles or resources about this topic? I'd like to have around five results to get started.\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"<tool_call>\\n{'name': 'search_duckduckgo', 'arguments': {'query': 'impact of social media on mental health', 'source': 'text', 'max_results': 5}}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"tool\",\n", "            \"value\": \"<tool_response>\\n{'name': 'search_duckduckgo', 'content': [{'result_id': 1, 'title': 'Social Media and Mental Health: Benefits, Risks, and Opportunities for ...', 'description': 'Despite the global reach of social media platforms, there is a dearth of research on the impact of these platforms on the mental health of individuals in diverse settings, as well as the ways in which social media could support mental health services in lower income countries where there is virtually no access to mental health providers.', 'url': 'https://pmc.ncbi.nlm.nih.gov/articles/PMC7785056/'}, {'result_id': 2, 'title': 'The Impact of Social Media on the Mental Health of Adolescents and ...', 'description': 'Mental health is represented as a state of well-being in which individuals recognize their potential, successfully navigate daily challenges, perform effectively at work, and make a substantial difference in the lives of others [].There is currently debate over the benefits and drawbacks of social media on mental health [].Social networking is an important part of safeguarding our mental health.', 'url': 'https://pmc.ncbi.nlm.nih.gov/articles/PMC10476631/'}, {'result_id': 3, 'title': \\\"Social media's impact on our mental health and tips to use it safely\\\", 'description': \\\"Social media can fuel anxiety, depression, loneliness and FOMO by activating the brain's reward center and creating false illusions. Learn how to decrease screen time, unfriend negative accounts, and find fulfillment outside of social media.\\\", 'url': 'https://health.ucdavis.edu/blog/cultivating-health/social-medias-impact-our-mental-health-and-tips-to-use-it-safely/2024/05'}, {'result_id': 4, 'title': 'Pros & cons: impacts of social media on mental health', 'description': \\\"This editorial reviews the latest research on how social media use can enhance or deteriorate mental health. It invites submissions to a new collection that explores the positive and negative effects of social media on users' well-being.\\\", 'url': 'https://bmcpsychology.biomedcentral.com/articles/10.1186/s40359-023-01243-x'}, {'result_id': 5, 'title': 'Social Media Use and Impact on Mental Health | NAMI', 'description': 'NAMI supports research and policies to protect youth and young adults from the risks of social media use, such as depression, cyberbullying, and disordered eating. NAMI also acknowledges the benefits and opportunities of social media for mental health, such as expression, community, and interventions.', 'url': 'https://www.nami.org/advocacy/policy-priorities/stopping-harmful-practices/social-media-use-and-impact-on-mental-health/'}]}\\n</tool_response>\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"Here are five articles and resources that explore the impact of social media on mental health:\\n\\n1. **[Social Media and Mental Health: Benefits, Risks, and Opportunities for ...](https://pmc.ncbi.nlm.nih.gov/articles/PMC7785056/)**  \\n   This article discusses the global reach of social media platforms and the lack of research on their impact on mental health, especially in diverse settings.\\n\\n2. **[The Impact of Social Media on the Mental Health of Adolescents and ...](https://pmc.ncbi.nlm.nih.gov/articles/PMC10476631/)**  \\n   This resource examines the debate over the benefits and drawbacks of social media on mental health, particularly focusing on adolescents.\\n\\n3. **[Social media's impact on our mental health and tips to use it safely](https://health.ucdavis.edu/blog/cultivating-health/social-medias-impact-our-mental-health-and-tips-to-use-it-safely/2024/05)**  \\n   This article highlights how social media can contribute to anxiety and depression and provides tips for using social media safely.\\n\\n4. **[Pros & cons: impacts of social media on mental health](https://bmcpsychology.biomedcentral.com/articles/10.1186/s40359-023-01243-x)**  \\n   This editorial reviews the latest research on the positive and negative effects of social media on mental health and invites further exploration of the topic.\\n\\n5. **[Social Media Use and Impact on Mental Health | NAMI](https://www.nami.org/advocacy/policy-priorities/stopping-harmful-practices/social-media-use-and-impact-on-mental-health/)**  \\n   This resource from NAMI discusses the risks of social media use, such as depression and cyberbullying, while also acknowledging its potential benefits for mental health.\\n\\nThese resources should provide a solid foundation for understanding the complex relationship between social media and mental health.\"\n", "        }\n", "    ],\n", "    [\n", "        {\n", "            \"from\": \"system\",\n", "            \"value\": \"You are a function calling AI model. You are provided with function signatures within <tools> </tools> XML tags. You may call one or more functions to assist with the user query. If available tools are not relevant in assisting with user query, just respond in natural conversational language. Don't make assumptions about what values to plug into functions. After calling & executing the functions, you will be provided with function results within <tool_response> </tool_response> XML tags.\\n <tools> \\n[{'type': 'function', 'function': {'name': 'search_duckduckgo', 'description': 'Use DuckDuckGo search engine to search information for\\\\nthe given query.\\\\n\\\\nThis function queries the DuckDuckGo API for related topics to\\\\nthe given search term. The results are formatted into a list of\\\\ndictionaries, each representing a search result.', 'strict': True, 'parameters': {'properties': {'query': {'type': 'string', 'description': 'The query to be searched.'}, 'source': {'type': 'string', 'description': 'The type of information to query (e.g., \\\"text\\\",\\\\n\\\"images\\\", \\\"videos\\\"). Defaults to \\\"text\\\".'}, 'max_results': {'type': 'integer', 'description': 'Max number of results, defaults to `5`.'}}, 'required': ['query', 'source', 'max_results'], 'type': 'object', 'additionalProperties': False}}}]\\n </tools>\\nFor each function call return a JSON object, with the following pydantic model json schema:{'title': 'FunctionCall', 'type': 'object', 'properties': {'name': {'title': 'Name', 'type': 'string'}, 'arguments': {'title': 'Arguments', 'type': 'object'}}, 'required': ['arguments', 'name']}\\nEach function call should be enclosed within <tool_call> </tool_call> XML tags.\\nExample:\\n<tool_call>\\n{'name': <function-name>, 'arguments': <args-dict>}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"human\",\n", "            \"value\": \"I'm looking to understand the benefits of regular exercise on mental health. Can you find me some articles or resources about this? I would appreciate having around five results to browse through.\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"<tool_call>\\n{'name': 'search_duckduckgo', 'arguments': {'query': 'benefits of regular exercise on mental health', 'source': 'text', 'max_results': 5}}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"tool\",\n", "            \"value\": \"<tool_response>\\n{'name': 'search_duckduckgo', 'content': [{'result_id': 1, 'title': 'Mental Health Benefits of Exercise: For Depression and More', 'description': 'Learn how exercise can help regulate stress hormones, release mood-boosting neurotransmitters, and improve various mental health conditions, such as depression and ADHD. Find out how exercise can also promote neuroplasticity, increase oxygen supply, and boost self-confidence, mood, sleep, and memory.', 'url': 'https://www.healthline.com/health/depression/exercise'}, {'result_id': 2, 'title': 'Role of Physical Activity on Mental Health and Well-Being: A Review', 'description': 'Regular exercise lowered the incidence of cardiometabolic illness, ... The effects of exercise on mental health have been shown to be beneficial. Among persons with schizophrenia, yoga was shown to have more positive effects with exercise when compared with no intervention. ... Translating the evidence of the benefits of physical exercise on ...', 'url': 'https://pmc.ncbi.nlm.nih.gov/articles/PMC9902068/'}, {'result_id': 3, 'title': 'Depression and anxiety: Exercise eases symptoms - Mayo Clinic', 'description': \\\"Regular exercise may improve depression or anxiety symptoms enough to make a big difference. That big difference can help kick-start further improvements. The mental health benefits of exercise and physical activity may last only if you stick with them over the long term. That's another good reason to find activities that you enjoy.\\\", 'url': 'https://www.mayoclinic.org/diseases-conditions/depression/in-depth/depression-and-exercise/art-20046495'}, {'result_id': 4, 'title': 'The Mental Health Benefits of Physical Exercise - Verywell Mind', 'description': 'Learn how exercise can improve your mood, reduce stress, and ease symptoms of mental illnesses like depression, anxiety, and PTSD. Find out which types of exercise are best for your mental well-being and how to start a safe and effective exercise plan.', 'url': 'https://www.verywellmind.com/mental-health-benefits-of-exercise-2584094'}, {'result_id': 5, 'title': 'Physical Activity Is Good for the Mind and the Body - Health', 'description': 'Learn how physical activity can improve brain health, cognitive function, sleep, and quality of life, and reduce the risk of anxiety and depression. Find out how to stay active in different seasons and circumstances, and how sports can benefit youth mental health.', 'url': 'https://odphp.health.gov/news/202112/physical-activity-good-mind-and-body'}]}\\n</tool_response>\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"Here are five articles that discuss the benefits of regular exercise on mental health:\\n\\n1. **[Mental Health Benefits of Exercise: For Depression and More](https://www.healthline.com/health/depression/exercise)** - This article explains how exercise can help regulate stress hormones, release mood-boosting neurotransmitters, and improve various mental health conditions, such as depression and ADHD.\\n\\n2. **[Role of Physical Activity on Mental Health and Well-Being: A Review](https://pmc.ncbi.nlm.nih.gov/articles/PMC9902068/)** - This review discusses the beneficial effects of regular exercise on mental health, including its impact on conditions like schizophrenia and the overall well-being of individuals.\\n\\n3. **[Depression and anxiety: Exercise eases symptoms - Mayo Clinic](https://www.mayoclinic.org/diseases-conditions/depression/in-depth/depression-and-exercise/art-20046495)** - This article highlights how regular exercise can significantly improve symptoms of depression and anxiety, emphasizing the importance of maintaining an active lifestyle.\\n\\n4. **[The Mental Health Benefits of Physical Exercise - Verywell Mind](https://www.verywellmind.com/mental-health-benefits-of-exercise-2584094)** - Learn how different types of exercise can improve mood, reduce stress, and ease symptoms of mental illnesses like depression, anxiety, and PTSD.\\n\\n5. **[Physical Activity Is Good for the Mind and the Body - Health](https://odphp.health.gov/news/202112/physical-activity-good-mind-and-body)** - This resource discusses how physical activity enhances brain health, cognitive function, and overall quality of life while reducing the risk of anxiety and depression.\\n\\nFeel free to explore these articles for more detailed information!\"\n", "        }\n", "    ],\n", "    [\n", "        {\n", "            \"from\": \"system\",\n", "            \"value\": \"You are a function calling AI model. You are provided with function signatures within <tools> </tools> XML tags. You may call one or more functions to assist with the user query. If available tools are not relevant in assisting with user query, just respond in natural conversational language. Don't make assumptions about what values to plug into functions. After calling & executing the functions, you will be provided with function results within <tool_response> </tool_response> XML tags.\\n <tools> \\n[{'type': 'function', 'function': {'name': 'search_duckduckgo', 'description': 'Use DuckDuckGo search engine to search information for\\\\nthe given query.\\\\n\\\\nThis function queries the DuckDuckGo API for related topics to\\\\nthe given search term. The results are formatted into a list of\\\\ndictionaries, each representing a search result.', 'strict': True, 'parameters': {'properties': {'query': {'type': 'string', 'description': 'The query to be searched.'}, 'source': {'type': 'string', 'description': 'The type of information to query (e.g., \\\"text\\\",\\\\n\\\"images\\\", \\\"videos\\\"). Defaults to \\\"text\\\".'}, 'max_results': {'type': 'integer', 'description': 'Max number of results, defaults to `5`.'}}, 'required': ['query', 'source', 'max_results'], 'type': 'object', 'additionalProperties': False}}}]\\n </tools>\\nFor each function call return a JSON object, with the following pydantic model json schema:{'title': 'FunctionCall', 'type': 'object', 'properties': {'name': {'title': 'Name', 'type': 'string'}, 'arguments': {'title': 'Arguments', 'type': 'object'}}, 'required': ['arguments', 'name']}\\nEach function call should be enclosed within <tool_call> </tool_call> XML tags.\\nExample:\\n<tool_call>\\n{'name': <function-name>, 'arguments': <args-dict>}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"human\",\n", "            \"value\": \"I'm researching the effects of sleep deprivation on cognitive function. Can you help me find some articles or resources about this topic? I would like to see around five results to review.\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"<tool_call>\\n{'name': 'search_duckduckgo', 'arguments': {'query': 'effects of sleep deprivation on cognitive function', 'source': 'text', 'max_results': 5}}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"tool\",\n", "            \"value\": \"<tool_response>\\n{'name': 'search_duckduckgo', 'content': [{'result_id': 1, 'title': 'Sleep deprivation: Impact on cognitive performance - PMC', 'description': 'The most thoroughly evaluated performances include different attentional functions, working memory, and long-term memory. Visuomotor and verbal functions as well as decision-making have also been assessed. Sleep deprivation effects on cognitive performance depend on the type of task or the modality it occupies (eg, verbal, visual, or auditory).', 'url': 'https://pmc.ncbi.nlm.nih.gov/articles/PMC2656292/'}, {'result_id': 2, 'title': 'The consequences of sleep deprivation on cognitive performance', 'description': 'For example, the dissociation between attentiveness and specific cognitive functions during SD can be explained by the incidence of local sleep in arousal subcortical promoting systems, ... demonstrating in vivo evidence of the effects of sleep deprivation on recognized neurodegenerative processes. 51.', 'url': 'https://pmc.ncbi.nlm.nih.gov/articles/PMC10155483/'}, {'result_id': 3, 'title': 'The effects of sleep deprivation on cognitive performance - Oxford Academic', 'description': 'The effects of sleep deprivation on frontal lobe functions include, mainly, decreased inhibition of response, decision making, divergent thinking and working memory. Objectives: To evaluate the acute effects of partial sleep deprivation on cognitive performance in young students of higher education.', 'url': 'https://academic.oup.com/eurpub/article/29/Supplement_1/ckz034.096/5480863'}, {'result_id': 4, 'title': 'How Does Lack of Sleep Affect Cognitive Impairment ... - Sleep Foundation', 'description': 'Levels of brain activity fluctuate during each stage of sleep \\u2014 including both rapid eye movement (REM) and non-REM (NREM) sleep \\u2014 and evidence increasingly suggests that sleep enhances most types of cognitive function. For people with sleep deprivation, insomnia, sleep apnea, or other conditions that prevent them from getting adequate rest ...', 'url': 'https://www.sleepfoundation.org/sleep-deprivation/lack-of-sleep-and-cognitive-impairment'}, {'result_id': 5, 'title': 'Effects of sleep deprivation on cognition - PubMed', 'description': 'Thus, the extent to which sleep deprivation affects a particular cognitive process may depend on several factors, including the magnitude of global decline in general alertness and attention, the degree to which the specific cognitive function depends on emotion-processing networks, and the extent to which that cognitive process can draw upon ...', 'url': 'https://pubmed.ncbi.nlm.nih.gov/21075236/'}]}\\n</tool_response>\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"Here are five articles and resources on the effects of sleep deprivation on cognitive function:\\n\\n1. **[Sleep deprivation: Impact on cognitive performance - PMC](https://pmc.ncbi.nlm.nih.gov/articles/PMC2656292/)**\\n   - This article evaluates various cognitive performances affected by sleep deprivation, including attentional functions, working memory, and long-term memory.\\n\\n2. **[The consequences of sleep deprivation on cognitive performance - PMC](https://pmc.ncbi.nlm.nih.gov/articles/PMC10155483/)**\\n   - This resource discusses the dissociation between attentiveness and specific cognitive functions during sleep deprivation, along with evidence of its effects on neurodegenerative processes.\\n\\n3. **[The effects of sleep deprivation on cognitive performance - Oxford Academic](https://academic.oup.com/eurpub/article/29/Supplement_1/ckz034.096/5480863)**\\n   - This study focuses on the acute effects of partial sleep deprivation on cognitive performance, particularly in young students, highlighting impacts on frontal lobe functions.\\n\\n4. **[How <PERSON> <PERSON>k of Sleep Affect Cognitive Impairment - Sleep Foundation](https://www.sleepfoundation.org/sleep-deprivation/lack-of-sleep-and-cognitive-impairment)**\\n   - This article explains how different stages of sleep affect brain activity and cognitive function, emphasizing the importance of adequate rest for cognitive health.\\n\\n5. **[Effects of sleep deprivation on cognition - PubMed](https://pubmed.ncbi.nlm.nih.gov/21075236/)**\\n   - This resource explores how sleep deprivation impacts various cognitive processes, influenced by factors such as alertness and emotional processing.\\n\\nFeel free to explore these articles for detailed insights into the topic!\"\n", "        }\n", "    ],\n", "    [\n", "        {\n", "            \"from\": \"system\",\n", "            \"value\": \"You are a function calling AI model. You are provided with function signatures within <tools> </tools> XML tags. You may call one or more functions to assist with the user query. If available tools are not relevant in assisting with user query, just respond in natural conversational language. Don't make assumptions about what values to plug into functions. After calling & executing the functions, you will be provided with function results within <tool_response> </tool_response> XML tags.\\n <tools> \\n[{'type': 'function', 'function': {'name': 'search_duckduckgo', 'description': 'Use DuckDuckGo search engine to search information for\\\\nthe given query.\\\\n\\\\nThis function queries the DuckDuckGo API for related topics to\\\\nthe given search term. The results are formatted into a list of\\\\ndictionaries, each representing a search result.', 'strict': True, 'parameters': {'properties': {'query': {'type': 'string', 'description': 'The query to be searched.'}, 'source': {'type': 'string', 'description': 'The type of information to query (e.g., \\\"text\\\",\\\\n\\\"images\\\", \\\"videos\\\"). Defaults to \\\"text\\\".'}, 'max_results': {'type': 'integer', 'description': 'Max number of results, defaults to `5`.'}}, 'required': ['query', 'source', 'max_results'], 'type': 'object', 'additionalProperties': False}}}]\\n </tools>\\nFor each function call return a JSON object, with the following pydantic model json schema:{'title': 'FunctionCall', 'type': 'object', 'properties': {'name': {'title': 'Name', 'type': 'string'}, 'arguments': {'title': 'Arguments', 'type': 'object'}}, 'required': ['arguments', 'name']}\\nEach function call should be enclosed within <tool_call> </tool_call> XML tags.\\nExample:\\n<tool_call>\\n{'name': <function-name>, 'arguments': <args-dict>}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"human\",\n", "            \"value\": \"I'm trying to find resources on the relationship between gratitude and happiness. Can you help me search for some articles or information on this topic? I would like to see about five relevant results.\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"<tool_call>\\n{'name': 'search_duckduckgo', 'arguments': {'query': 'relationship between gratitude and happiness', 'source': 'text', 'max_results': 5}}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"tool\",\n", "            \"value\": \"<tool_response>\\n{'name': 'search_duckduckgo', 'content': [{'result_id': 1, 'title': 'Gratitude and Happiness: The Causes and Consequences of Gratitude', 'description': 'In this chapter, we review recent research on the relationship between gratitude and happiness. First, we show how gratitude is a critical component of the good life. Because gratitude is vital to wellbeing, it is important to establish the causes of state and trait gratitude. We explain an appraisal approach to grateful emotion and show how certain benefit interpretations are critical to the ...', 'url': 'https://www.intechopen.com/chapters/82820'}, {'result_id': 2, 'title': 'Being Thankful for What You Have: A Systematic Review of Evidence for ...', 'description': 'Some of the reviewed correlational studies also explored the potential mechanisms for the relationship between gratitude and life satisfaction by examining mediating variables. 18, 28, 32, 35, 40, 44 For example, in one cross-sectional study of 427 Chinese undergraduates, <PERSON> and colleagues found that social support and self-esteem fully ...', 'url': 'https://pmc.ncbi.nlm.nih.gov/articles/PMC10693196/'}, {'result_id': 3, 'title': 'The Neuroscience of Gratitude and Effects on the Brain', 'description': 'The Relationship Between Resilience And Gratitude. Gratitude fosters adaptive coping mechanisms. By managing positive emotions like satisfaction, happiness, and pleasure, gratitude enhances our emotional resilience and builds our inner strength to combat stress (Gloria & Steinhardt, 2016).', 'url': 'https://positivepsychology.com/neuroscience-of-gratitude/'}, {'result_id': 4, 'title': 'The Science and Research on Gratitude and Happiness', 'description': 'There is a direct link between happiness and gratitude. Expressing gratitude brings about happiness for the one giving thanks. The more someone is thankful or feels gratitude, the less there is time or room for negative thoughts. ... Development of a measure of gratitude, relationships with subjective well-being, by Watkins, P.C., Woodward, K ...', 'url': 'https://positivepsychology.com/gratitude-happiness-research/'}, {'result_id': 5, 'title': 'Gratitude and Well Being: The Benefits of Appreciation - PMC', 'description': \\\"Associations between Gratitude and Well Being. A number of authors have espoused a theoretical relationship between gratitude and well being. 3-6 In a very pragmatic way, this association seems logical. Experiencing gratitude, thankfulness, and appreciation tends to foster positive feelings, which in turn, contribute to one's overall sense of ...\\\", 'url': 'https://pmc.ncbi.nlm.nih.gov/articles/PMC3010965/'}]}\\n</tool_response>\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"Here are five relevant articles on the relationship between gratitude and happiness:\\n\\n1. **[Gratitude and Happiness: The Causes and Consequences of Gratitude](https://www.intechopen.com/chapters/82820)**  \\n   This chapter reviews recent research on how gratitude is a critical component of the good life and its importance to well-being.\\n\\n2. **[Being Thankful for What You Have: A Systematic Review of Evidence for ...](https://pmc.ncbi.nlm.nih.gov/articles/PMC10693196/)**  \\n   This systematic review explores the potential mechanisms for the relationship between gratitude and life satisfaction, including mediating variables like social support and self-esteem.\\n\\n3. **[The Neuroscience of Gratitude and Effects on the Brain](https://positivepsychology.com/neuroscience-of-gratitude/)**  \\n   This article discusses how gratitude fosters adaptive coping mechanisms and enhances emotional resilience, linking it to happiness and satisfaction.\\n\\n4. **[The Science and Research on Gratitude and Happiness](https://positivepsychology.com/gratitude-happiness-research/)**  \\n   This resource outlines the direct link between happiness and gratitude, explaining how expressing gratitude can reduce negative thoughts and enhance well-being.\\n\\n5. **[Gratitude and Well Being: The Benefits of Appreciation - PMC](https://pmc.ncbi.nlm.nih.gov/articles/PMC3010965/)**  \\n   This article discusses the associations between gratitude and well-being, highlighting how experiencing gratitude fosters positive feelings that contribute to overall happiness.\\n\\nFeel free to explore these articles for more insights on the topic!\"\n", "        }\n", "    ],\n", "    [\n", "        {\n", "            \"from\": \"system\",\n", "            \"value\": \"You are a function calling AI model. You are provided with function signatures within <tools> </tools> XML tags. You may call one or more functions to assist with the user query. If available tools are not relevant in assisting with user query, just respond in natural conversational language. Don't make assumptions about what values to plug into functions. After calling & executing the functions, you will be provided with function results within <tool_response> </tool_response> XML tags.\\n <tools> \\n[{'type': 'function', 'function': {'name': 'search_wiki', 'description': 'Search the entity in WikiPedia and return the summary of the\\\\nrequired page, containing factual information about\\\\n    the given entity.', 'strict': True, 'parameters': {'properties': {'entity': {'type': 'string', 'description': 'The entity to be searched.'}}, 'required': ['entity'], 'type': 'object', 'additionalProperties': False}}}]\\n </tools>\\nFor each function call return a JSON object, with the following pydantic model json schema:{'title': 'FunctionCall', 'type': 'object', 'properties': {'name': {'title': 'Name', 'type': 'string'}, 'arguments': {'title': 'Arguments', 'type': 'object'}}, 'required': ['arguments', 'name']}\\nEach function call should be enclosed within <tool_call> </tool_call> XML tags.\\nExample:\\n<tool_call>\\n{'name': <function-name>, 'arguments': <args-dict>}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"human\",\n", "            \"value\": \"I'm looking to find some detailed information about a specific historical event. Can you help me search for it on Wikipedia and provide a summary of what I find? The event I'm interested in is the signing of the Treaty of Versailles.\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"<tool_call>\\n{'name': 'search_wiki', 'arguments': {'entity': 'Treaty of Versailles'}}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"tool\",\n", "            \"value\": \"<tool_response>\\n{'name': 'search_wiki', 'content': 'The Treaty of Versailles was a peace treaty signed on 28 June 1919. As the most important treaty of World War I, it ended the state of war between Germany and most of the Allied Powers. It was signed in the Palace of Versailles, exactly five years after the assassination of Arch<PERSON>ke <PERSON>, which led to the war. The other Central Powers on the German side signed separate treaties. Although the armistice of 11 November 1918 ended the actual fighting, and agreed certain principles and conditions including the payment of reparations, it took six months of Allied negotiations at the Paris Peace Conference to conclude the peace treaty.'}\\n</tool_response>\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"The Treaty of Versailles was a significant peace treaty signed on June 28, 1919, marking the end of World War I between Germany and most of the Allied Powers. The signing took place at the Palace of Versailles, coinciding with the five-year anniversary of the assassination of Archduke <PERSON>, an event that triggered the war.\\n\\nWhile the armistice on November 11, 1918, had halted the fighting and established certain principles and conditions, including reparations, it took six months of negotiations at the Paris Peace Conference to finalize the Treaty. The treaty was the most important of the agreements made to conclude the war, and other Central Powers aligned with Germany signed separate treaties.\"\n", "        }\n", "    ],\n", "    [\n", "        {\n", "            \"from\": \"system\",\n", "            \"value\": \"You are a function calling AI model. You are provided with function signatures within <tools> </tools> XML tags. You may call one or more functions to assist with the user query. If available tools are not relevant in assisting with user query, just respond in natural conversational language. Don't make assumptions about what values to plug into functions. After calling & executing the functions, you will be provided with function results within <tool_response> </tool_response> XML tags.\\n <tools> \\n[{'type': 'function', 'function': {'name': 'search_wiki', 'description': 'Search the entity in WikiPedia and return the summary of the\\\\nrequired page, containing factual information about\\\\n    the given entity.', 'strict': True, 'parameters': {'properties': {'entity': {'type': 'string', 'description': 'The entity to be searched.'}}, 'required': ['entity'], 'type': 'object', 'additionalProperties': False}}}]\\n </tools>\\nFor each function call return a JSON object, with the following pydantic model json schema:{'title': 'FunctionCall', 'type': 'object', 'properties': {'name': {'title': 'Name', 'type': 'string'}, 'arguments': {'title': 'Arguments', 'type': 'object'}}, 'required': ['arguments', 'name']}\\nEach function call should be enclosed within <tool_call> </tool_call> XML tags.\\nExample:\\n<tool_call>\\n{'name': <function-name>, 'arguments': <args-dict>}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"human\",\n", "            \"value\": \"Could you help me look up information about the Great Wall of China on Wikipedia? I'm interested in a summary that covers its history, construction, and significance.\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"<tool_call>\\n{'name': 'search_wiki', 'arguments': {'entity': 'Great Wall of China'}}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"tool\",\n", "            \"value\": \"<tool_response>\\n{'name': 'search_wiki', 'content': 'The Great Wall of China (traditional Chinese: \\u842c\\u91cc\\u9577\\u57ce; simplified Chinese: \\u4e07\\u91cc\\u957f\\u57ce; pinyin: W\\u00e0nl\\u01d0 Ch\\u00e1ngch\\u00e9ng, literally \\\"ten thousand li long wall\\\") is a series of fortifications in China. They were built across the historical northern borders of ancient Chinese states and Imperial China as protection against various nomadic groups from the Eurasian Steppe. The first walls date to the 7th century BC; these were joined together in the Qin dynasty. Successive dynasties expanded the wall system; the best-known sections were built by the Ming dynasty (1368\\u20131644).\\\\nTo aid in defense, the Great Wall utilized watchtowers, troop barracks, garrison stations, signaling capabilities through the means of smoke or fire, and its status as a transportation corridor.'}\\n</tool_response>\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"The Great Wall of China is a series of fortifications built across the northern borders of ancient Chinese states and Imperial China, primarily to protect against nomadic groups from the Eurasian Steppe. Its history dates back to the 7th century BC, with the first walls constructed during this period. These early walls were later joined together during the Qin dynasty.\\n\\nOver the centuries, successive dynasties expanded the wall system, with the most famous sections being built during the Ming dynasty (1368\\u20131644). The Great Wall was not only a defensive structure but also included features such as watchtowers, troop barracks, garrison stations, and signaling capabilities using smoke or fire. Additionally, it served as a transportation corridor, enhancing its significance in Chinese history.\"\n", "        }\n", "    ],\n", "    [\n", "        {\n", "            \"from\": \"system\",\n", "            \"value\": \"You are a function calling AI model. You are provided with function signatures within <tools> </tools> XML tags. You may call one or more functions to assist with the user query. If available tools are not relevant in assisting with user query, just respond in natural conversational language. Don't make assumptions about what values to plug into functions. After calling & executing the functions, you will be provided with function results within <tool_response> </tool_response> XML tags.\\n <tools> \\n[{'type': 'function', 'function': {'name': 'search_wiki', 'description': 'Search the entity in WikiPedia and return the summary of the\\\\nrequired page, containing factual information about\\\\n    the given entity.', 'strict': True, 'parameters': {'properties': {'entity': {'type': 'string', 'description': 'The entity to be searched.'}}, 'required': ['entity'], 'type': 'object', 'additionalProperties': False}}}]\\n </tools>\\nFor each function call return a JSON object, with the following pydantic model json schema:{'title': 'FunctionCall', 'type': 'object', 'properties': {'name': {'title': 'Name', 'type': 'string'}, 'arguments': {'title': 'Arguments', 'type': 'object'}}, 'required': ['arguments', 'name']}\\nEach function call should be enclosed within <tool_call> </tool_call> XML tags.\\nExample:\\n<tool_call>\\n{'name': <function-name>, 'arguments': <args-dict>}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"human\",\n", "            \"value\": \"Can you search for information on <PERSON> in Wikipedia and provide me with a summary of his life, major contributions, and theories?\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"<tool_call>\\n{'name': 'search_wiki', 'arguments': {'entity': '<PERSON>'}}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"tool\",\n", "            \"value\": \"<tool_response>\\n{'name': 'search_wiki', 'content': '<PERSON> (, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; German: [\\u02c8alb\\u025b\\u0281t \\u02c8\\u0294a\\u026an\\u0283ta\\u026an] ; 14 March 1879 \\u2013 18 April 1955) was a German-born theoretical physicist who is best known for developing the theory of relativity. <PERSON> also made important contributions to quantum mechanics. His mass\\u2013energy equivalence formula E = mc2, which arises from special relativity, has been called \\\"the world\\\\'s most famous equation\\\". He received the 1921 Nobel Prize in Physics for his services to theoretical physics, and especially for his discovery of the law of the photoelectric effect.\\\\nBorn in the German Empire, <PERSON> moved to Switzerland in 1895, forsaking his German citizenship the following year.'}\\n</tool_response>\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"<PERSON> (14 March 1879 \\u2013 18 April 1955) was a German-born theoretical physicist renowned for developing the theory of relativity. He also made significant contributions to quantum mechanics. One of his most famous contributions is the mass-energy equivalence formula \\\\(E = mc^2\\\\), which is derived from special relativity and is often referred to as \\\"the world's most famous equation.\\\"\\n\\<PERSON><PERSON><PERSON><PERSON> was awarded the Nobel Prize in Physics in 1921 for his services to theoretical physics, particularly for his discovery of the law of the photoelectric effect. Born in the German Empire, he moved to Switzerland in 1895 and renounced his German citizenship the following year. \\n\\nHis work has had a profound impact on the field of physics and our understanding of the universe.\"\n", "        }\n", "    ],\n", "    [\n", "        {\n", "            \"from\": \"system\",\n", "            \"value\": \"You are a function calling AI model. You are provided with function signatures within <tools> </tools> XML tags. You may call one or more functions to assist with the user query. If available tools are not relevant in assisting with user query, just respond in natural conversational language. Don't make assumptions about what values to plug into functions. After calling & executing the functions, you will be provided with function results within <tool_response> </tool_response> XML tags.\\n <tools> \\n[{'type': 'function', 'function': {'name': 'search_wiki', 'description': 'Search the entity in WikiPedia and return the summary of the\\\\nrequired page, containing factual information about\\\\n    the given entity.', 'strict': True, 'parameters': {'properties': {'entity': {'type': 'string', 'description': 'The entity to be searched.'}}, 'required': ['entity'], 'type': 'object', 'additionalProperties': False}}}]\\n </tools>\\nFor each function call return a JSON object, with the following pydantic model json schema:{'title': 'FunctionCall', 'type': 'object', 'properties': {'name': {'title': 'Name', 'type': 'string'}, 'arguments': {'title': 'Arguments', 'type': 'object'}}, 'required': ['arguments', 'name']}\\nEach function call should be enclosed within <tool_call> </tool_call> XML tags.\\nExample:\\n<tool_call>\\n{'name': <function-name>, 'arguments': <args-dict>}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"human\",\n", "            \"value\": \"Could you help me find a summary about the planet Mars on Wikipedia? I'm looking for information on its characteristics, exploration history, and any interesting facts.\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"<tool_call>\\n{'name': 'search_wiki', 'arguments': {'entity': 'Mars'}}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"tool\",\n", "            \"value\": \"<tool_response>\\n{'name': 'search_wiki', 'content': 'Mars is the fourth planet from the Sun. The surface of Mars is orange-red because it is covered in iron(III) oxide dust, giving it the nickname \\\"the Red Planet\\\". Mars is among the brightest objects in Earth\\\\'s sky, and its high-contrast albedo features have made it a common subject for telescope viewing. It is classified as a terrestrial planet and is the second smallest of the Solar System\\\\'s planets with a diameter of 6,779 km (4,212 mi). In terms of orbital motion, a Martian solar day (sol) is equal to 24.6 hours, and a Martian solar year is equal to 1.88 Earth years (687 Earth days).'}\\n</tool_response>\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"Mars is the fourth planet from the Sun and is often referred to as \\\"the Red Planet\\\" due to its orange-red surface, which is covered in iron(III) oxide dust. It is one of the brightest objects in Earth's sky, and its distinct features have made it a popular subject for telescope observation. Mars is classified as a terrestrial planet and is the second smallest in the Solar System, with a diameter of 6,779 km (4,212 mi).\\n\\nIn terms of its orbital characteristics, a Martian solar day (known as a sol) is approximately 24.6 hours, and a Martian solar year lasts about 1.88 Earth years (687 Earth days).\\n\\nIf you would like more detailed information on its exploration history or interesting facts, please let me know!\"\n", "        }\n", "    ],\n", "    [\n", "        {\n", "            \"from\": \"system\",\n", "            \"value\": \"You are a function calling AI model. You are provided with function signatures within <tools> </tools> XML tags. You may call one or more functions to assist with the user query. If available tools are not relevant in assisting with user query, just respond in natural conversational language. Don't make assumptions about what values to plug into functions. After calling & executing the functions, you will be provided with function results within <tool_response> </tool_response> XML tags.\\n <tools> \\n[{'type': 'function', 'function': {'name': 'search_wiki', 'description': 'Search the entity in WikiPedia and return the summary of the\\\\nrequired page, containing factual information about\\\\n    the given entity.', 'strict': True, 'parameters': {'properties': {'entity': {'type': 'string', 'description': 'The entity to be searched.'}}, 'required': ['entity'], 'type': 'object', 'additionalProperties': False}}}]\\n </tools>\\nFor each function call return a JSON object, with the following pydantic model json schema:{'title': 'FunctionCall', 'type': 'object', 'properties': {'name': {'title': 'Name', 'type': 'string'}, 'arguments': {'title': 'Arguments', 'type': 'object'}}, 'required': ['arguments', 'name']}\\nEach function call should be enclosed within <tool_call> </tool_call> XML tags.\\nExample:\\n<tool_call>\\n{'name': <function-name>, 'arguments': <args-dict>}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"human\",\n", "            \"value\": \"Can you look up the topic of climate change on Wikipedia and provide me with a summary that highlights its causes, effects, and possible solutions?\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"<tool_call>\\n{'name': 'search_wiki', 'arguments': {'entity': 'Climate change'}}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"tool\",\n", "            \"value\": \"<tool_response>\\n{'name': 'search_wiki', 'content': \\\"Present-day climate change includes both global warming\\u2014the ongoing increase in global average temperature\\u2014and its wider effects on Earth\\u2019s climate system. Climate change in a broader sense also includes previous long-term changes to Earth's climate. The current rise in global temperatures is driven by human activities, especially fossil fuel burning since the Industrial Revolution. Fossil fuel use, deforestation, and some agricultural and industrial practices release greenhouse gases. These gases absorb some of the heat that the Earth radiates after it warms from sunlight, warming the lower atmosphere.\\\"}\\n</tool_response>\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"<tool_call>\\n{'name': 'search_wiki', 'arguments': {'entity': 'Climate change causes'}}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"tool\",\n", "            \"value\": \"<tool_response>\\n{'name': 'search_wiki', 'content': 'There is no page in Wikipedia corresponding to entity Climate change causes, please specify another word to describe the entity to be searched.'}\\n</tool_response>\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"<tool_call>\\n{'name': 'search_wiki', 'arguments': {'entity': 'Effects of climate change'}}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"tool\",\n", "            \"value\": \"<tool_response>\\n{'name': 'search_wiki', 'content': \\\"Effects of climate change are well documented and growing for  Earth's natural environment and human societies. Changes to the climate system include an overall warming trend, changes to precipitation patterns, and more extreme weather. As the climate changes it impacts the natural environment with effects such as more intense forest fires, thawing permafrost, and desertification. These changes impact ecosystems and societies, and can become irreversible once tipping points are crossed.  Climate activists are engaged in a range of activities around the world that seek to ameliorate these issues or prevent them from happening.\\\"}\\n</tool_response>\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"<tool_call>\\n{'name': 'search_wiki', 'arguments': {'entity': 'Climate change solutions'}}\\n</tool_call>\"\n", "        },\n", "        {\n", "            \"from\": \"tool\",\n", "            \"value\": \"<tool_response>\\n{'name': 'search_wiki', 'content': 'There is no page in Wikipedia corresponding to entity Climate change solutions, please specify another word to describe the entity to be searched.'}\\n</tool_response>\"\n", "        },\n", "        {\n", "            \"from\": \"gpt\",\n", "            \"value\": \"### Summary of Climate Change\\n\\n**Causes:**\\nClimate change primarily refers to the ongoing increase in global average temperature and its broader effects on Earth's climate system. The current rise in temperatures is largely driven by human activities, particularly the burning of fossil fuels since the Industrial Revolution. Other contributing factors include deforestation and certain agricultural and industrial practices, which release greenhouse gases. These gases trap heat in the atmosphere, leading to further warming.\\n\\n**Effects:**\\nThe effects of climate change are well-documented and increasingly evident across Earth's natural environment and human societies. Key impacts include:\\n- An overall warming trend.\\n- Altered precipitation patterns.\\n- More extreme weather events.\\n- Increased intensity of forest fires.\\n- Thawing permafrost.\\n- Desertification.\\n\\nThese changes can disrupt ecosystems and human societies, potentially leading to irreversible consequences if critical tipping points are crossed.\\n\\n**Possible Solutions:**\\nWhile specific solutions were not detailed in the search results, general approaches to address climate change typically include transitioning to renewable energy sources, enhancing energy efficiency, reforestation, and implementing sustainable agricultural practices. Climate activists are actively engaged in various initiatives worldwide to mitigate these issues and prevent further damage.\"\n", "        }\n", "    ]\n", "]\n"]}], "source": ["# Load and display data to ensure correctness\n", "with open(output_file, \"r\") as f:\n", "    data = json.load(f)\n", "\n", "print(\"Sample data:\", json.dumps(data[\"generated_queries\"][:100], indent=4))  # Display sample queries\n", "print(\"\\nSample tool call data:\", json.dumps(data[\"tool_call_data\"][:100], indent=4))  # Display sample tool call data\n"]}, {"cell_type": "markdown", "metadata": {"id": "U_kaSObAJTa6"}, "source": ["## Summary\n", "In this tutorial, you learned how to generate user queries and structure tool call data by leveraging multiple tools with the camel library. This setup enables scalable and flexible data generation for various toolkits and tasks."]}, {"cell_type": "markdown", "metadata": {"id": "35iTz35suM8K"}, "source": ["<div class=\"align-center\">\n", "  <a href=\"https://www.camel-ai.org/\"><img src=\"https://i.postimg.cc/KzQ5rfBC/button.png\"width=\"150\"></a>\n", "  <a href=\"https://discord.camel-ai.org\"><img src=\"https://i.postimg.cc/L4wPdG9N/join-2.png\"  width=\"150\"></a></a>\n", "  \n", "⭐ <i>Star us on [*Github*](https://github.com/camel-ai/camel), join our [*Discord*](https://discord.camel-ai.org) or follow our [*X*](https://x.com/camelaiorg)\n", "</div>"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}