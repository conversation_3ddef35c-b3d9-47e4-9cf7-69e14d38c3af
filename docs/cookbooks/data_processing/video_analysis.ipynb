{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {"id": "ssX_map8c6mx"}, "source": ["# Video Analysis"]}, {"cell_type": "markdown", "metadata": {"id": "9S0pePjZkwUz"}, "source": ["You can also check this cookbook in colab [here](https://colab.research.google.com/drive/1XfF4BOSzo_rj9J58aZYanMUk5vixn-eR?usp=sharing)\n", "\n", "<div class=\"align-center\">\n", "  <a href=\"https://www.camel-ai.org/\"><img src=\"https://i.postimg.cc/KzQ5rfBC/button.png\"width=\"150\"></a>\n", "  <a href=\"https://discord.camel-ai.org\"><img src=\"https://i.postimg.cc/L4wPdG9N/join-2.png\"  width=\"150\"></a></a>\n", "  \n", "⭐ <i>Star us on [*Github*](https://github.com/camel-ai/camel), join our [*Discord*](https://discord.camel-ai.org) or follow our [*X*](https://x.com/camelaiorg)\n", "</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This notebook demonstrates how to set up and leverage CAMEL's ability to do video analysis.\n", "\n", "In this notebook, you'll explore:\n", "\n", "* **CAMEL**: A powerful multi-agent framework that enables Retrieval-Augmented Generation and multi-agent role-playing scenarios, allowing for sophisticated AI-driven tasks.\n", "* **Video Analysis**: How to use CAMEL to read and generate descriptions of uploaded videos."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📦 Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "id": "AkvGo5umOwxM"}, "outputs": [], "source": ["%pip install \"camel-ai[all]==0.2.16\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔑 Setting Up API Keys"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_-8_5_srcpC_", "outputId": "c3fa2b4c-4f5e-4cb5-e300-404e7e55bdc2"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter your API key: ··········\n"]}], "source": ["import os\n", "from getpass import getpass\n", "\n", "# Prompt for the API key securely\n", "openai_api_key = getpass('Enter your API key: ')\n", "os.environ[\"OPENAI_API_KEY\"] = openai_api_key"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Alternatively, if running on Colab, you could save your API keys and tokens as **Colab Secrets**, and use them across notebooks.\n", "\n", "To do so, **comment out** the above **manual** API key prompt code block(s), and **uncomment** the following codeblock.\n", "\n", "⚠️ Don't forget granting access to the API key you would be using to the current notebook."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# from google.colab import userdata\n", "\n", "# os.environ[\"OPENAI_API_KEY\"] = userdata.get(\"OPENAI_API_KEY\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Set up an agent for video analysis task"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "J97N3GtSexwu"}, "outputs": [], "source": ["from camel.agents import ChatAgent\n", "from camel.configs.openai_config import ChatGPTConfig\n", "from camel.messages import BaseMessage\n", "from camel.prompts.prompt_templates import PromptTemplateGenerator\n", "from camel.types import ModelType, ModelPlatformType\n", "from camel.types.enums import RoleType, TaskType\n", "from camel.models import ModelFactory\n", "\n", "# Define system message\n", "sys_msg_prompt = PromptTemplateGenerator().get_prompt_from_key(\n", "    TaskType.VIDEO_DESCRIPTION, RoleType.ASSISTANT\n", ")\n", "sys_msg = BaseMessage.make_assistant_message(\n", "    role_name=\"Assistant\",\n", "    content=sys_msg_prompt,\n", ")\n", "\n", "# Set model\n", "model=ModelFactory.create(\n", "    model_platform=ModelPlatformType.OPENAI,\n", "    model_type=ModelType.GPT_4O,\n", "    model_config_dict=ChatGPTConfig().as_dict(),\n", ")\n", "\n", "# Set agent\n", "camel_agent = ChatAgent(\n", "    sys_msg,\n", "    model=model\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Providing video and set user message"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "cQIJBZh_YXRY", "outputId": "0766500d-171b-4b15-e98c-89821836d0c6"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\"Step into the fascinating world of scientific discovery with our latest video! Watch as a dedicated researcher meticulously works in a high-tech laboratory, surrounded by intricate equipment and cutting-edge technology. This behind-the-scenes footage offers a glimpse into the meticulous process of experimentation and innovation. Join us on this journey of exploration and witness the passion and precision that drive scientific breakthroughs. Don't miss out on this captivating look at the heart of scientific research!\"\n"]}], "source": ["# Provide your video path\n", "video_cctv = \"/content/CCTV.mov\"\n", "with open(video_cctv, \"rb\") as video_cctv:\n", "    video_bytes_cctv = video_cctv.read()\n", "\n", "# Set user message\n", "user_msg_cctv = BaseMessage.make_user_message(\n", "    role_name=\"User\",\n", "    content=\"These are frames from a video that I want to upload. Generate a\"\n", "    \"compelling description that I can upload along with the video.\",\n", "    video_bytes=video_bytes_cctv,\n", ")\n", "\n", "# Get response information\n", "response_cctv = camel_agent.step(user_msg_cctv)\n", "print(response_cctv.msgs[0].content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "q7NIRGNT3bWx", "outputId": "84941af1-45b9-42bf-cc71-8da1fc9a620d"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\"Embark on a breathtaking journey through lush, green landscapes and rugged mountain trails. This video captures the serene beauty of nature, with winding paths leading you through picturesque scenery under a vibrant blue sky. Perfect for nature lovers and adventure seekers alike, this visual escape will transport you to a tranquil world far from the hustle and bustle of everyday life. Join us as we explore the untouched beauty of this stunning trail.\"\n"]}], "source": ["# Provide your video path\n", "video_help = \"/content/help.mov\"\n", "with open(video_help, \"rb\") as video_help:\n", "    video_bytes_help = video_help.read()\n", "\n", "# Set user message\n", "user_msg_help = BaseMessage.make_user_message(\n", "    role_name=\"User\",\n", "    content=\"These are frames from a video that I want to upload. Generate a\"\n", "    \"compelling description that I can upload along with the video.\",\n", "    video_bytes=video_bytes_help,\n", ")\n", "\n", "# Get response information\n", "response_help = camel_agent.step(user_msg_help)\n", "print(response_help.msgs[0].content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "RdwfgT_S3uZ5", "outputId": "3c69323b-ad66-41c4-d700-232400535841"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\"Join us for a candid glimpse into a moment of relaxation and reflection in the kitchen. Watch as our protagonist unwinds after a long day, savoring a drink and enjoying the simple pleasures of life. This video captures the essence of taking a break and finding comfort in familiar surroundings. Don't miss this relatable and heartwarming scene!\"\n"]}], "source": ["# Provide your video path\n", "video_content_mode = \"/content/content mod.mov\"\n", "with open(video_content_mode, \"rb\") as video_content_mode:\n", "    video_bytes_content_mode = video_content_mode.read()\n", "\n", "# Set user message\n", "user_msg_content_mode = BaseMessage.make_user_message(\n", "    role_name=\"User\",\n", "    content=\"These are frames from a video that I want to upload. Generate a\"\n", "    \"compelling description that I can upload along with the video.\",\n", "    video_bytes=video_bytes_content_mode,\n", ")\n", "\n", "# Get response information\n", "response_content_mode = camel_agent.step(user_msg_content_mode)\n", "print(response_content_mode.msgs[0].content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🌟 Highlights"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This notebook has guided you through setting up an agent and analyzing videos using CAMEL. \n", "\n", "Now, you know how to generate description for uploaded videos."]}, {"cell_type": "markdown", "metadata": {}, "source": ["That's everything: Got questions about 🐫 CAMEL-AI? Join us on [Discord](https://discord.camel-ai.org)! Whether you want to share feedback, explore the latest in multi-agent systems, get support, or connect with others on exciting projects, we’d love to have you in the community! 🤝\n", "\n", "Check out some of our other work:\n", "\n", "1. 🐫 Creating Your First CAMEL Agent [free Colab](https://docs.camel-ai.org/cookbooks/create_your_first_agent.html)\n", "\n", "2.  Graph RAG Cookbook [free Colab](https://colab.research.google.com/drive/1uZKQSuu0qW6ukkuSv9TukLB9bVaS1H0U?usp=sharing)\n", "\n", "3. 🧑‍⚖️ Create A Hackathon Judge Committee with Workforce [free Colab](https://colab.research.google.com/drive/*********************************?usp=sharing)\n", "\n", "4. 🔥 3 ways to ingest data from websites with Firecrawl & CAMEL [free Colab](https://colab.research.google.com/drive/1lOmM3VmgR1hLwDKdeLGFve_75RFW0R9I?usp=sharing)\n", "\n", "5. 🦥 Agentic SFT Data Generation with CAMEL and Mistral Models, Fine-Tuned with Unsloth [free Colab](https://colab.research.google.com/drive/1lYgArBw7ARVPSpdwgKLYnp_NEXiNDOd-?usp=sharingg)\n", "\n", "Thanks from everyone at 🐫 CAMEL-AI\n", "\n", "\n", "<div class=\"align-center\">\n", "  <a href=\"https://www.camel-ai.org/\"><img src=\"https://i.postimg.cc/KzQ5rfBC/button.png\"width=\"150\"></a>\n", "  <a href=\"https://discord.camel-ai.org\"><img src=\"https://i.postimg.cc/L4wPdG9N/join-2.png\"  width=\"150\"></a></a>\n", "  \n", "⭐ <i>Star us on [*Github*](https://github.com/camel-ai/camel), join our [*Discord*](https://discord.camel-ai.org) or follow our [*X*](https://x.com/camelaiorg)\n", "</div>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}