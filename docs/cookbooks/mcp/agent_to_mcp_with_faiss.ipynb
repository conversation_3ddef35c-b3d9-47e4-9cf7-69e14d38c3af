{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["# 🐫 **CAMEL Agent to MCP Server Conversion via .to_mcp() for FAISS RAG**\n"], "metadata": {"id": "fNv8EK3G2zDk"}}, {"cell_type": "markdown", "source": ["<div class=\"align-center\">\n", "  <a href=\"https://www.camel-ai.org/\"><img src=\"https://i.postimg.cc/KzQ5rfBC/button.png\"width=\"150\"></a>\n", "  <a href=\"https://discord.camel-ai.org\"><img src=\"https://i.postimg.cc/L4wPdG9N/join-2.png\"  width=\"150\"></a></a>\n", "  \n", "⭐ <i>Star us on [*Github*](https://github.com/camel-ai/camel), join our [*Discord*](https://discord.camel-ai.org) or follow our [*X*](https://x.com/camelaiorg)\n", "</div>"], "metadata": {"id": "u9C6kQIp3y_C"}}, {"cell_type": "markdown", "source": [], "metadata": {"id": "FmREQ-bS2YGC"}}, {"cell_type": "markdown", "source": ["## Overview\n", "This cookbook demonstrates how to convert a CAMEL RAG agent into an MCP server using the .to_mcp() method, allowing other MCP clients to use your agent as a tool.\n", "## What You'll Learn\n", "- How to create a self-contained RAG agent with embedded FAISS tools\n", "- How to convert any CAMEL agent to an MCP server using `.to_mcp()`\n", "- How to test the converted MCP server with a client\n", "\n", "\n", "## 🎯 Key Concept: The .to_mcp() Method\n", "The `.to_mcp()` method converts any CAMEL agent into an MCP server, making it available as a tool for other MCP clients.\n", "\n", "\n", "\n", "\n"], "metadata": {"id": "HM_n50_G33-R"}}, {"cell_type": "code", "source": ["# Simple example from CAMEL documentation\n", "from camel.agents import ChatAgent\n", "\n", "agent = ChatAgent(model=\"gpt-4o-mini\")\n", "mcp_server = agent.to_mcp(name=\"demo\", description=\"A demo agent\")\n", "mcp_server.run(transport=\"stdio\")"], "metadata": {"id": "WKbAedpuT7ld"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["# File 1: RAG Agent Configuration (faiss_rag_agent_config.py)\n", "\n", "## Purpose: This file creates a self-contained RAG agent with embedded FAISS vector search tools.\n", "\n", "## What it does:\n", "\n", "Defines three core FAISS tools: create_faiss_index, query_faiss, and get_index_stats\n", "\n", "- Implements simple text chunking and vector embeddings using numpy\n", "- Creates a ChatAgent with Gemini model and embedded tools\n", "- Provides a complete RAG system without external dependencies\n", "- Self-contained: No external MCP server dependencies\n", "- FAISS integration: Uses Facebook AI Similarity Search for vector operations\n", "- Simple embeddings: Uses deterministic random embeddings for demonstration\n", "- Tool composition: Wraps functions as FunctionTool objects for CAMEL\n"], "metadata": {"id": "p3rs66Il4aXz"}}, {"cell_type": "code", "source": ["import logging\n", "import os\n", "from pathlib import Path\n", "from typing import List, Dict, Any\n", "import json\n", "\n", "from camel.agents import ChatAgent\n", "from camel.messages import BaseMessage\n", "from camel.models import ModelFactory\n", "from camel.types import ModelPlatformType, RoleType\n", "from camel.toolkits import FunctionTool\n", "from camel.storages import FaissStorage\n", "from camel.storages.vectordb_storages.base import VectorRecord, VectorDBQuery\n", "from camel.types.enums import VectorDistance\n", "import numpy as np\n", "\n", "# Prevent logging since MCP needs to use stdout\n", "root_logger = logging.getLogger()\n", "root_logger.handlers = []\n", "root_logger.addHandler(logging.NullHandler())\n", "\n", "# Global storage instance\n", "faiss_storage = None\n", "\n", "def initialize_faiss_storage():\n", "    \"\"\"Initialize CAMEL's FaissStorage with professional settings\"\"\"\n", "    global faiss_storage\n", "\n", "    storage_path = Path.cwd() / \"rag_storage\"\n", "    storage_path.mkdir(exist_ok=True)\n", "\n", "    faiss_storage = FaissStorage(\n", "        vector_dim=1536,  # OpenAI embedding dimension\n", "        index_type='HNSW',  # Fast with high recall\n", "        collection_name='rag_documents',\n", "        storage_path=str(storage_path),\n", "        distance=VectorDistance.COSINE,\n", "        m=16,  # HNSW connections per node\n", "    )\n", "\n", "    # Load existing index if available\n", "    try:\n", "        faiss_storage.load()\n", "    except:\n", "        pass  # No existing index to load\n", "\n", "    return faiss_storage\n", "\n", "\n", "def create_rag_agent():\n", "    \"\"\"Create a self-contained RAG agent with CAMEL's FaissStorage\"\"\"\n", "\n", "    # Initialize storage\n", "    initialize_faiss_storage()\n", "\n", "    # Create Gemini model\n", "    model = ModelFactory.create(\n", "        model_platform=ModelPlatformType.GEMINI,\n", "        model_type=\"gemini-2.5-flash-preview-04-17\",\n", "        api_key=os.getenv(\"GOOGLE_API_KEY\"),\n", "        model_config_dict={\n", "            \"temperature\": 0.2,\n", "        }\n", "    )\n", "\n", "    # No tools needed since .to_mcp() doesn't expose them\n", "    tools = []\n", "\n", "    print(f\"DEBUG: Created {len(tools)} tools:\")\n", "    for i, tool in enumerate(tools):\n", "        print(f\"  Tool {i+1}: {tool.func.__name__}\")\n", "\n", "    # Enhanced system message that tells the agent it HAS these tools\n", "    system_content = (\n", "        \"You are a professional RAG assistant with access to CAMEL's FaissStorage for vector database operations. \"\n", "        \"You have the following tools available:\\n\"\n", "        \"1. create_faiss_index(documents, chunk_size) - Create a persistent FAISS index\\n\"\n", "        \"2. query_faiss(query, top_k) - Search for semantically similar content\\n\"\n", "        \"3. get_index_stats() - Get detailed statistics about the vector storage\\n\"\n", "        \"\\nYou MUST use these tools when users ask you to create indexes or search for information. \"\n", "        \"Do not claim you cannot perform these operations - you have the tools to do them!\"\n", "    )\n", "\n", "    system_message = BaseMessage(\n", "        role_name=\"Professional RAG Assistant\",\n", "        role_type=RoleType.ASSISTANT,\n", "        meta_dict={\"task\": \"Production FAISS RAG Operations\"},\n", "        content=system_content\n", "    )\n", "\n", "    # Create the agent (conversational interface only)\n", "    rag_agent = ChatAgent(\n", "        system_message=system_message,\n", "        model=model,\n", "        tools=tools  # This should attach the tools\n", "    )\n", "\n", "    # Verify tools are attached - Fixed debug code\n", "    print(f\"DEBUG: Agent created\")\n", "    if hasattr(rag_agent, '_internal_tools'):\n", "        print(f\"DEBUG: Agent has {len(rag_agent._internal_tools)} internal tools\")\n", "        # Fix: _internal_tools contains tool names (strings), not objects\n", "        for i, tool_name in enumerate(rag_agent._internal_tools):\n", "            print(f\"  Internal Tool {i+1}: {tool_name}\")\n", "    else:\n", "        print(\"DEBUG: Agent has no _internal_tools attribute\")\n", "\n", "    if hasattr(rag_agent, 'tool_dict'):\n", "        print(f\"DEBUG: Agent tool_dict: {list(rag_agent.tool_dict.keys())}\")\n", "        # Also show the actual tool objects\n", "        for tool_name, tool_obj in rag_agent.tool_dict.items():\n", "            print(f\"  {tool_name}: {tool_obj.func.__name__}\")\n", "\n", "    return rag_agent\n", "\n"], "metadata": {"id": "kGjDLuW04en8"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["# File 2: MCP Server Conversion (faiss_rag_agent_mcp_server.py)\n", "## Purpose: This is the main file that demonstrates the agent-to-MCP conversion.\n", "\n", "## What it does:\n", "- Imports the RAG agent from the configuration file\n", "- Uses the .to_mcp() method to convert the agent into an MCP server\n", "- Runs the server with stdio transport for local communication\n", "\n", "## Critical Points:\n", "- Must be synchronous (no async/await) - let .to_mcp() handle event loops\n", "- Requires GOOGLE_API_KEY environment variable\n"], "metadata": {"id": "oAL2f5cM6cur"}}, {"cell_type": "code", "source": ["import os\n", "import json\n", "import numpy as np\n", "from typing import List\n", "from pathlib import Path\n", "\n", "from faiss_rag_agent_config import (\n", "    create_rag_agent,\n", "    initialize_faiss_storage,\n", "    faiss_storage  # Import the global storage\n", ")\n", "from camel.storages.vectordb_storages.base import VectorRecord, VectorDBQuery\n", "\n", "def main():\n", "    \"\"\"Initialize and run the RAG agent as an MCP server with hybrid approach\"\"\"\n", "\n", "    # Ensure API key is set\n", "    if not os.getenv(\"GOOGLE_API_KEY\"):\n", "        print(\"Error: GOOGLE_API_KEY environment variable not set\")\n", "        return\n", "\n", "    print(\"DEBUG: Creating RAG agent...\")\n", "    # Create the self-contained RAG agent\n", "    rag_agent = create_rag_agent()\n", "\n", "    # Ensure storage is initialized at the module level\n", "    print(\"DEBUG: Ensuring FAISS storage is initialized...\")\n", "    storage_instance = initialize_faiss_storage()\n", "\n", "    print(\"DEBUG: Converting agent to MCP server...\")\n", "\n", "    try:\n", "        # Step 1: Convert Chat<PERSON><PERSON> to MCP server (for conversational capabilities)\n", "        mcp_server = rag_agent.to_mcp(\n", "            name=\"RAG-Agent-MCP\",\n", "            description=\"A self-contained RAG assistant with FAISS vector search capabilities\"\n", "        )\n", "        print(\"DEBUG: .to_mcp() conversion successful!\")\n", "\n", "        # Step 2: Add custom FAISS tools to the same MCP server instance\n", "        print(\"DEBUG: Adding custom FAISS tools to MCP server...\")\n", "\n", "        @mcp_server.tool()\n", "        async def create_faiss_index(documents: List[str], chunk_size: int = 1000) -> str:\n", "            \"\"\"Create a FAISS index from document texts using CAMEL's storage.\"\"\"\n", "            # Import here to ensure we get the latest global state\n", "            from faiss_rag_agent_config import faiss_storage, initialize_faiss_storage\n", "\n", "            try:\n", "                print(f\"DEBUG: create_faiss_index called with {len(documents)} documents\")\n", "\n", "                # Ensure storage is available\n", "                current_storage = faiss_storage\n", "                if current_storage is None:\n", "                    print(\"DEBUG: Storage is None, initializing...\")\n", "                    current_storage = initialize_faiss_storage()\n", "\n", "                print(f\"DEBUG: Storage status before clear: {current_storage.status().vector_count} vectors\")\n", "                current_storage.clear()\n", "                print(\"DEBUG: Storage cleared successfully\")\n", "\n", "                all_records = []\n", "                chunk_id = 0\n", "\n", "                for doc_idx, doc_text in enumerate(documents):\n", "                    words = doc_text.split()\n", "\n", "                    for i in range(0, len(words), max(1, chunk_size // 10)):\n", "                        chunk_words = words[i:i + max(1, chunk_size // 10)]\n", "                        chunk_text = ' '.join(chunk_words)\n", "\n", "                        if chunk_text.strip():\n", "                            # Create simple embedding\n", "                            np.random.seed(hash(chunk_text) % (2**32))\n", "                            embedding = np.random.normal(0, 1, 1536).astype(np.float32)\n", "\n", "                            record = VectorRecord(\n", "                                id=f\"chunk_{chunk_id}\",\n", "                                vector=embedding,\n", "                                payload={\n", "                                    \"text\": chunk_text,\n", "                                    \"source_id\": doc_idx,\n", "                                    \"chunk_id\": chunk_id,\n", "                                    \"metadata\": {\n", "                                        \"document_index\": doc_idx,\n", "                                        \"chunk_index\": chunk_id\n", "                                    }\n", "                                }\n", "                            )\n", "\n", "                            all_records.append(record)\n", "                            chunk_id += 1\n", "\n", "                if all_records:\n", "                    print(f\"DEBUG: Adding {len(all_records)} records to storage\")\n", "                    current_storage.add(all_records)\n", "\n", "                    # Verify the addition\n", "                    final_status = current_storage.status()\n", "                    print(f\"DEBUG: Final storage status: {final_status.vector_count} vectors\")\n", "\n", "                    return json.dumps({\n", "                        \"status\": \"success\",\n", "                        \"message\": f\"Indexed {len(all_records)} chunks from {len(documents)} documents\",\n", "                        \"total_chunks\": len(all_records),\n", "                        \"final_vector_count\": final_status.vector_count\n", "                    })\n", "                else:\n", "                    return json.dumps({\"error\": \"No text chunks were created\"})\n", "\n", "            except Exception as e:\n", "                import traceback\n", "                error_details = traceback.format_exc()\n", "                print(f\"DEBUG: Error in create_faiss_index: {error_details}\")\n", "                return json.dumps({\"error\": f\"Error creating FAISS index: {str(e)}\", \"details\": error_details})\n", "\n", "        @mcp_server.tool()\n", "        async def query_faiss(query: str, top_k: int = 3) -> str:\n", "            \"\"\"Query the FAISS index using CAMEL's storage.\"\"\"\n", "            # Import here to ensure we get the latest global state\n", "            from faiss_rag_agent_config import faiss_storage, initialize_faiss_storage\n", "\n", "            try:\n", "                print(f\"DEBUG: query_faiss called with query: '{query}', top_k: {top_k}\")\n", "\n", "                # Ensure storage is available\n", "                current_storage = faiss_storage\n", "                if current_storage is None:\n", "                    print(\"DEBUG: Storage is None, initializing...\")\n", "                    current_storage = initialize_faiss_storage()\n", "\n", "                status = current_storage.status()\n", "                print(f\"DEBUG: Storage has {status.vector_count} vectors\")\n", "\n", "                if status.vector_count == 0:\n", "                    return json.dumps({\"error\": \"No documents have been indexed yet\"})\n", "\n", "                # Create query embedding\n", "                np.random.seed(hash(query) % (2**32))\n", "                query_embedding = np.random.normal(0, 1, 1536).astype(np.float32)\n", "\n", "                db_query = VectorDBQuery(\n", "                    query_vector=query_embedding,\n", "                    top_k=min(top_k, status.vector_count)\n", "                )\n", "\n", "                results = current_storage.query(db_query)\n", "\n", "                formatted_results = []\n", "                for result in results:\n", "                    formatted_results.append({\n", "                        \"id\": result.record.id,\n", "                        \"text\": result.record.payload.get(\"text\", \"\"),\n", "                        \"metadata\": result.record.payload.get(\"metadata\", {}),\n", "                        \"score\": float(result.similarity)\n", "                    })\n", "\n", "                return json.dumps({\n", "                    \"results\": formatted_results,\n", "                    \"query_info\": {\n", "                        \"total_results\": len(formatted_results),\n", "                        \"search_type\": \"semantic_similarity\"\n", "                    }\n", "                })\n", "\n", "            except Exception as e:\n", "                import traceback\n", "                error_details = traceback.format_exc()\n", "                print(f\"DEBUG: Error in query_faiss: {error_details}\")\n", "                return json.dumps({\"error\": f\"Error querying FAISS index: {str(e)}\", \"details\": error_details})\n", "\n", "        @mcp_server.tool()\n", "        async def get_index_stats() -> str:\n", "            \"\"\"Get statistics about the FAISS storage.\"\"\"\n", "            # Import here to ensure we get the latest global state\n", "            from faiss_rag_agent_config import faiss_storage, initialize_faiss_storage\n", "\n", "            try:\n", "                print(\"DEBUG: get_index_stats called\")\n", "\n", "                # Ensure storage is available\n", "                current_storage = faiss_storage\n", "                if current_storage is None:\n", "                    print(\"DEBUG: Storage is None, initializing...\")\n", "                    current_storage = initialize_faiss_storage()\n", "\n", "                status = current_storage.status()\n", "\n", "                stats = {\n", "                    \"vector_count\": status.vector_count,\n", "                    \"vector_dimension\": 1536,\n", "                    \"index_type\": \"HNSW\",\n", "                    \"distance_metric\": \"cosine\",\n", "                    \"collection_name\": current_storage.collection_name,\n", "                    \"persistent_storage\": current_storage.storage_path is not None,\n", "                    \"storage_path\": str(current_storage.storage_path) if current_storage.storage_path else \"in-memory\"\n", "                }\n", "\n", "                return json.dumps(stats, indent=2)\n", "\n", "            except Exception as e:\n", "                import traceback\n", "                error_details = traceback.format_exc()\n", "                print(f\"DEBUG: Error in get_index_stats: {error_details}\")\n", "                return json.dumps({\"error\": f\"Error getting index stats: {str(e)}\", \"details\": error_details})\n", "\n", "        print(\"DEBUG: Custom FAISS tools added successfully!\")\n", "        print(\"DEBUG: Starting hybrid MCP server...\")\n", "\n", "        # Run the MCP server with both conversational and tool capabilities\n", "        mcp_server.run(transport=\"stdio\")\n", "\n", "    except Exception as e:\n", "        import traceback\n", "        error_details = traceback.format_exc()\n", "        print(f\"DEBUG: Hybrid MCP setup failed: {e}\")\n", "        print(f\"DEBUG: Error details: {error_details}\")\n", "        return\n", "\n", "if __name__ == \"__main__\":\n", "    main()"], "metadata": {"id": "RD4dhcOb4qWo"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["# File 3: Test Client (test_faiss_rag_mcp_client.py)\n", "## Purpose: Tests the converted MCP server by connecting to it as a client.\n", "\n", "## What it does:\n", "\n", "- Creates an MCP configuration that points to your server script\n", "- Connects to the MCP server using MCPToolkit\n", "- Creates a client agent that can use the server's tools\n", "- Provides an interactive interface to test RAG functionality\n", "\n"], "metadata": {"id": "u0a_VY1A67Kc"}}, {"cell_type": "code", "source": ["import asyncio\n", "import json\n", "import os\n", "import sys\n", "from getpass import getpass\n", "from pathlib import Path\n", "\n", "from camel.agents import ChatAgent\n", "from camel.logger import get_logger\n", "from camel.messages import BaseMessage\n", "from camel.models import ModelFactory\n", "from camel.toolkits import MCPToolkit\n", "from camel.types import ModelPlatformType, RoleType\n", "\n", "logger = get_logger(__name__)\n", "\n", "async def main():\n", "    \"\"\"Test the self-contained RAG MCP server\"\"\"\n", "\n", "    # Configuration for connecting to our RAG MCP server\n", "    rag_mcp_config = {\n", "        \"mcpServers\": {\n", "            \"rag_agent_server\": {\n", "                \"type\": \"script\",\n", "                \"command\": \"python\",\n", "                \"args\": [\"faiss_rag_agent_mcp_server.py\"],\n", "                \"transport\": \"stdio\",\n", "                \"env\": {\n", "                    \"GOOGLE_API_KEY\": os.getenv(\"GOOGLE_API_KEY\") or getpass('Enter Gemini API key for server: ')\n", "                }\n", "            }\n", "        }\n", "    }\n", "\n", "    # Write config file\n", "    config_path = Path.cwd() / \"rag_mcp_client_config.json\"\n", "    with open(config_path, 'w') as f:\n", "        json.dump(rag_mcp_config, f, indent=2)\n", "\n", "    # Get API key for client model\n", "    # gemini_api_key = getpass('Enter your Gemini API key for client: ')\n", "    # if not gemini_api_key:\n", "        # logger.error(\"API key is required to proceed.\")\n", "        # return\n", "    # os.environ[\"GOOGLE_API_KEY\"] = gemini_api_key\n", "    gemini_api_key = os.getenv(\"GOOGLE_API_KEY\") or getpass('Enter Gemini API key for client: ')\n", "\n", "    # Connect to the RAG MCP server\n", "    logger.info(\"Connecting to self-contained RAG MCP server...\")\n", "    mcp_toolkit = MCPToolkit(config_path=str(config_path))\n", "\n", "    await mcp_toolkit.connect()\n", "    tools = mcp_toolkit.get_tools()\n", "\n", "    # Debugging:-------------------\n", "    # Add debug before the existing debug section:\n", "    print(f\"\\n=== DEBUG: MCP Client received {len(tools)} tools ===\")\n", "    if tools:\n", "        for i, tool in enumerate(tools):\n", "            print(f\"Tool {i+1}:\")\n", "            print(f\"  Name: {getattr(tool, 'name', 'Unknown')}\")\n", "            if hasattr(tool, 'func'):\n", "                print(f\"  Function: {tool.func.__name__}\")\n", "                print(f\"  Doc: {tool.func.__doc__[:100] if tool.func.__doc__ else 'No doc'}...\")\n", "            else:\n", "                print(f\"  Type: {type(tool)}\")\n", "                print(f\"  Attributes: {[attr for attr in dir(tool) if not attr.startswith('_')]}\")\n", "    else:\n", "        print(\"  No tools received from MCP server!\")\n", "    print(\"=\" * 50)\n", "    # Debugging--------------------\n", "\n", "    if not tools:\n", "        logger.error(\"No tools found from the RAG MCP server\")\n", "        return\n", "\n", "    logger.info(f\"Found {len(tools)} tools from RAG MCP server\")\n", "\n", "    try:\n", "        # Create a client model to interact with the RAG MCP server\n", "        model = ModelFactory.create(\n", "            model_platform=ModelPlatformType.GEMINI,\n", "            model_type=\"gemini-2.5-flash-preview-04-17\",\n", "            api_key=gemini_api_key,\n", "            model_config_dict={\n", "                \"temperature\": 0.2,\n", "                # \"max_output_tokens\": 1024,\n", "            }\n", "        )\n", "\n", "        system_message = BaseMessage(\n", "            role_name=\"RAG Client\",\n", "            role_type=RoleType.ASSISTANT,\n", "            meta_dict={\"task\": \"RAG MCP Client\"},\n", "            content=\"You are a client that uses a self-contained RAG MCP server for document search and retrieval tasks.\"\n", "        )\n", "\n", "        # Create client agent with RAG MCP tools\n", "        client_agent = ChatAgent(\n", "            system_message=system_message,\n", "            model=model,\n", "            tools=tools\n", "        )\n", "        client_agent.reset()\n", "\n", "        # Debugging --------------------------\n", "\n", "        # Add direct tool testing:\n", "        print(\"\\n=== DEBUG: Testing direct tool access ===\")\n", "        if tools:\n", "            try:\n", "                # Try to call get_index_stats directly\n", "                stats_tool = None\n", "                for tool in tools:\n", "                    if hasattr(tool, 'func') and 'stats' in tool.func.__name__:\n", "                        stats_tool = tool\n", "                        break\n", "\n", "                if stats_tool:\n", "                    print(\"Found stats tool, testing direct call...\")\n", "                    result = stats_tool.func()\n", "                    print(f\"Direct tool call result: {result}\")\n", "                else:\n", "                    print(\"Could not find stats tool\")\n", "            except Exception as e:\n", "                print(f\"Direct tool call failed: {e}\")\n", "        else:\n", "            print(\"No tools to test directly\")\n", "        print(\"=\" * 50)\n", "# DEBUGGING ----------------------------------\n", "\n", "        print(\"\\n=== Self-Contained RAG Agent as MCP Server Test ===\\n\")\n", "        print(\"Testing CAMEL RAG agent (with embedded FAISS) exported as MCP server using .to_mcp()...\")\n", "        print(\"Type 'exit' to quit\\n\")\n", "\n", "        # Test the RAG MCP server\n", "        test_message = \"Hello! Please create an index with documents about AI, machine learning, and FAISS, then search for information about vector databases.\"\n", "        print(f\"Test Query: {test_message}\\n\")\n", "\n", "        response = await client_agent.astep(test_message)\n", "        if response and response.msgs:\n", "            print(f\"RAG MCP Response: {response.msgs[0].content}\\n\")\n", "\n", "        # Interactive loop\n", "        while True:\n", "            user_input = input(\"You: \")\n", "            if user_input.lower() == 'exit':\n", "                break\n", "\n", "            response = await client_agent.astep(user_input)\n", "\n", "            if response and response.msgs:\n", "                agent_reply = response.msgs[0].content\n", "                print(f\"RAG MCP Agent: {agent_reply}\\n\")\n", "            else:\n", "                print(\"No response received.\\n\")\n", "\n", "    except Exception as e:\n", "        logger.error(f\"An error occurred: {str(e)}\")\n", "        print(f\"\\nError: {str(e)}\")\n", "    finally:\n", "        try:\n", "            await mcp_toolkit.disconnect()\n", "        except Exception as cleanup_error:\n", "            logger.warning(f\"Error during cleanup: {cleanup_error}\")\n", "\n", "if __name__ == \"__main__\":\n", "    if sys.platform == \"win32\" and sys.version_info >= (3, 8):\n", "        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())\n", "    asyncio.run(main())"], "metadata": {"id": "P9ZZ8Pwn41ck"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["![250609_04h20m30s_screenshot.png](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAB4AAAAI5CAIAAADPPbCLAAAAA3NCSVQICAjb4U/gAAAgAElEQVR4nOzdd3xT1fsH8CdJs9Mm3XsvWihtWS0to0zZBUFAEZkFQUEUxQkKKKCCqOhXBFkiIihimbKn7L3b0kIHpXskbZqmSfP74/oLocX2piQM/bxf/JGc++Sc5557kj+eXs7lePq0JQAAAAAAAAAAAAAAS+M+7gQAAAAAAAAAAAAA4N8JBWgAAAAAAAAAAAAAsAoUoAEAAAAAAAAAAADAKlCABgAAAAAAAAAAAACrQAEaAAAAAAAAAAAAAKwCBWgAAAAAAAAAAAAAsAoUoAEAAAAAAAAAAADAKlCABgAAAAAAAAAAAACrQAEaAAAAAAAAAAAAAKwCBWgAAAAAAAAAAAAAsAoUoAEAAAAAAAAAAADAKlCABgAAAAAAAAAAAACrQAEaAAAAAAAAAAAAAKwCBWgAAAAAAAAAAAAAsAoUoAEAAAAAAAAAAADAKlCAfooNHpq0fM2RmXNXPO5EnlAPOT+xcd2WrTkycPAYy2YFAAAAAAAAAADw34EC9FOsZXQHvkAYGBTh4Oj0uHN5Ej3k/Hj7BgsEQk+vQIsnBgAAAAAAAAAA8B9h87gTaCJvH//3PlopFkuZt3q9vrAge/uWVUcO7qwf3H/gyP6DksrLij+dO7GoqKB+wLNDx8d37G8nd6yt1effzdyyefmZU0eallijY1nQpfNHPTwCsrJSS4qLmtyJSCT6/Ovk9NSLXy6cYcHcHvtYxGJ+WOXD4Tx8JnM//cnbJ6ROo16vW7Vs9tHDu0wbG1g/zi5ucxesF/3/mje6evnE5/OmmrYMGT6xfXwfhb2zTldTWpJ/9NCWbck/1fkUm5iGjZ3wTvv4vpt/+27H1p+NjR9+vNLV3Xfxp1PSUq+ZBj83fGLb2J72Dq48Hk+jUWdmXPv0kynGo3MWrPXxDWVeGwyGyory82cPrvh+nln5AAAAAAAAAADAE+hpvQPa0ytALJbW1upVylKVslSn07q5+40a936HTs/UD+7Re4RAIHJ28Rw8bFL9oy+MnDpg0ARHJ3edrobD4fj4NRs78aOAwNCmJdbwWJa1aePypFEd584c9zCd2Ns72traO7l4WiqrJ2QsYjE/jywfR0c3IqqoKGeWK/NPWV6iUpbViWxg/VSpK5XKEpWytKqqgogMhlqVslSpLFEpS0zDXhg5tV/iOEcnd41Grdfr3Nz9Bg97tc5GImxiGuXtE8IXCDsmJBpbYuO6+Qe2kEhsA4JamEa+9uZnfRPHubh663Q1KmUph8MNDW/j5x9kDHBycieiClWZSlmqrlTJbBUdEwa+Nv1Ts/IBAAAAAAAAAIAn0NN6BzTjZuqFebP/rtONHP16t2ee79l7RJ1bSuM69LCzc2BeBwa3rNODs4tb526D9Xp98qalWzavEYlEU6d/Ft4itu+A0UsWv2tuPg2P9WSS2cqJyIbH/5eNxcajzKeyUvlqUo+GYxpePxUVqhnTBhNRaFjEu7NW5GSnz3x7RP1O4jsP0Ot1mzYs2bF1PRH17D1k6AvT2nfo+8emVWbFNI7DISI3d79WbeLOnTlGRJ26JP7/kXu3jffp/3x064QabfWOras3//b3ftyubh75ebmmnVVWKl+d0JN53apNXNLkjyNbdfLxDcjKzDAjJQAAAAAAAAAAeMI83QXo2lqD8fXa1Ys7JAyU2zvXiWndtisRnTqxKyS0lbOLV0BgaEZ6ivFoz97DhULxpQtHtmxeQ0QajWbZ/z6at/BXRb1+2Gh4LCLi8XgjRk2Lbp1ga+dQU1NdkJ+9fcuqU8cPmhUTG9dt3MSP+AIhEel02uNHt6/4fn79ZBrtZ/6iDe4e/sxrNw+/1etPMa91uprVy+eY1vF9fANGjHrTxy+My+XeuHYmODQq4+blhfOnMUd79xv+7HOv/LJukZ2dY0K3wbZ29lXqymtXTiz95kO9Xm/uWA0bOfr1+M6J33wx/crls8bGcRPfbdOux/szhjJbbbCZH7Py6dV3WM/eIxT2ztWaqrTUC0uXzFSrK1kmbGQwGBqNaXT9MPQ6HREZDLX1D/n4BkildtmZKUxlmYh27/xNrnCSSu3MimGPw+EkdBt87swxB0enoJCo+gEdEwYS0dY/lm/Z/KOxsU71me6fn3NnjmXdTgkNax0aFo0CNAAAAAAAAADAU+1p3YKjPju5nMezYWpzpgKCWuj1+i2bfrh+9RSHw+mY0N/0KLPz7PkzB40tZaUl77353IKPJzchh4bHIqJpby3s2mOYvYOrwWAQCiW+fmFJkz5uH9/drJgqdYVarVJXKjUatY2NIK7jAJnMtn4yjfZTWalUVyqrNWrmLdMn86+6usoY5uHp/cbbS0LD2ojFUqFQHBndUSKxNd2J2MMzgC8Q9uo7asCzE+QKJy6XJ5XZtY3tOXbCu+aO1Si5wkkkkjQLb23a6OUTIpbIRCIx+/lhn09gUMTQF6Y5OLpxuTyxRNYyqsPEV2ezT9gsja6fRpUUF9bW6iX3l5J/Xb909Q+fmRXDXo22OqRZa5nMtm//kQKBSFutMT3q7u7p5u5XVHjHtPrMnqbK7EI/AAAAAAAAAAA8UdjeAd0utvPwF6fzbOpuVpCXe2v+nMmWjWGPucuViHx8A0aPf9/Ghp+TnWYa0DKyrb2Da+btGzk5mQf3/x4b3yc4NNo0QCaTE9HFC8dNG8tK79tUl6VGx3JycmkeEavVVq9bveDQge3OLm5Jk2eHhEbHxvU6/tde9jEXL5x8bVIfIuLxeAu/TrZ3cHFz97qZdt3csT6elcRM3ZwFv+TmpL/31vMPPK+xE2Yq7J0L8rO3/vGDQCAc+vw0oUhSvzLo7OKZcv3Mvt0blMqy3v1eiozuGB4RazzKcqxGKZWlRCSzVRCRRCJl7kQWCsV6vS73Tjb7+WGfj4Oj262Mq7u2/5Sfl92j9/C4Dv2ahbdtQuY8no0x4QdqdP2wUVGhupV+NTC45fR3Fq/4fu4DVzKbGPZOndgV32nAoCHjI1t1qqwov5l2KTK6472TiorjcDg52Tcb7YdD93btaBvTyT+gebVGff7sX+wzeTJ/owAAAAAAAAAA/uPYFqBltgq5wpnH49VpNy1EWiqGvcCgCOPmCURUoSr7feP/TANiO/QmossX/yKilOuX8+7edvfwd3ZxKyzIYwL4AqHBYGC2bnh26Pgu3Z7jcLlEdCf75vw55j1FsNGxOFwuh8PVVlcx9e7CgrxvFs8Ym/T+X0e2GTthE2Ok1+tVqlJ7B5f6h9j3U6OtJqLaB+3nQEQx7bsGBkdWqMoWLZjCbJsQFBLVPr5PnRtdiSgj/YqxPJeWMmPJst12do5mjcVGWWkBEUmldh6e3h99si4t9cLn86YKhWJNlbp+cAPzwz6f/LzM2e///XS+Zd/OaRbWxsHR7Z82x2iAWCz934oDpi2H9m9atfzec/YaXT8srVo2d/JrCyIi4z/94ve01Av792xkNmg2N4als6f3t4np0e2Z54no/JkD3Pu/2sxWNnUek/hAUpndku931RpquRyuzFbO4XAP7vutokLFPpMn8zcKAAAAAAAAAOA/jm0Bev+e5P17kh9NDHu1tXqVqszWVsHl8nKy075dPOPu3TumAUHBkTXa6j+3/cy8vX71lLuHf0K3gb+uX1q/N5lMIbNVMM9Pa6Bq+U8aHauwIC8n+6a3T/DcBetTbpy9cvHYkUM7v1w4w7QTNjFsWKqf2PheHA7n+F87jJv2lhTnEZFOV1MnMj3tkvG1Xq9Xlpe4uduKRCKNpm6p+mEUFd4lIonULjaup0AoCgxuyePxhEKxRmOt4mDmrRumb8vLihwc3ezk9k3oSqUsvVfsNhjKy4tNj5q1VhuQk5M5850Xhwyb2Da2R4uWcc0jYs+fPfT1orfNjWGpRqvNSLsU1iKGiI4cSu7c9VnTo1wul4hYbH9NRGRrZ09EBoNBqSw5dXzXujVfmpXJk/kbBQAAAAAAAADwH/d0P4TwZuqFebMnvTByas8+L+pqaupUnwMCQ51dvDgczryFG5kKmEAgIqJm4W2J/i7qVVdXcTgc/4CQWxmpP65c+OPKhSKR6Mvv/jQ3EzZjEdGybz8YPf79gKCINu26t2nXfdiLb5w7vX/5dx+bdsUmhg2L9OPu7kdEB/ZuMrbI5Y5EVFurrxOpv78kzTwij8utexvpQ8rOuklEYonMLyDcYDCIRJJOCX0EAlFpaaFlBzLS33+mtQYDETF/pTBLZaVyysRn/ukoy/XDkl6v3/Dz/zb8/L+OnXv1TRzXqk2XSVPnfPf1LHNjWNq3Z6NfQPOc7LRzZ47VKUCrVGVEZGuraLSTqqrK6a/2a8LTHQEAAAAAAAAA4En2dO8BXVtrIKKf134d1TrBLyC8T/8Xdmz92Xi0Y8IAplBoJ79vLwhv72DjnbllpYXePiFBIRG3MlKZoxqNplZf90mGjWIzFhFlZ92aO2t8SGiL2PhefgHhvn7N4jsN4PFsln7zkfEjbGLYYNkPU1Hl8R68EkRiqU6nNW6vTERu7r5EROZXYBsdi43srFt6vU4sltraKtJvXvLzD4+IjOcLhFVqM/ZqsGA+7BkavA2Y5fox15FDf54+efDzr5Ijozs9TEzDzpw6cuZU1wceunr51JDh5OEV0Ggner3uIavPT+ZvFAAAAAAAAADAfxzb0ptUJpcrnOqX6kz3RbVUTBNsS/5hTNKHz/QdeXBfsrGMFRIaTURLvnjj7Omjxsjp7yyOiIzv1nPw9i3riCj1xrmIyPjWbbvt+XPTA3tmic1Yrm4eSZNnHz6w+fCBHakpV4gooWu/0UmzTJ81xyaGDfb9FBXmGwy1CnuXBz4iT6vV8HhODo5OzDbZoWER/oEtiEjAF5qVD5uxWKpSVzg5e9rY2Jw/e1Amk7doGcfhcNSVyseVj0WwWT9sxMZ1G/Bs0safv7xw7gTTotFoNBq1zFZhLGSzibGUWxmpBfnZLq4+XXskWntfiyf8NwoAAAAAAAAA4L+JbQH6wN4tB/ZueTQxTXD4wI4OnfqHNGv90rgZS5d8SESubh5uHv5lpYWmFT0iunzxr4jI+BYRsUxRb8fWn3v0eiGkWfRzwyf++sv3RDRk+ESxxLa0tID96CzHCgtvFRQc6ebmq6upOXZ0D18g8A9sTvdvZ8EmxhSzx64NX1CnnX0/er2+uCjPydnj9RlfrFr+senNzkRUWlLg4ur90pgZ6378okXLdgOencAU5uT2zuznh+VYLKnVKpmtgogunDvi4ODq5u5HRBUV5Q8M/qf5sWA+D4/l+jHi2dgQEYfDrd+Vu4efh2dA0uSPd25dvS35J4lE+vzIqc4unmWlhcbKMpsYCzp8YPOQ4VOfe/41kUjK/AcFmcw2ILDZpYunLTvQE/4bBQAAAAAAAADw3/R07wFt6pefvnxn1g9t2nULCPw5Iz0loetAHo+XnZVaJ+zwgW1Dhk/18Q9j3ur1+u3JK4e9+EbfxHHdn3mew+EKhCKdrubQ/t/ZD81yrIP7t3XtOczHN3TCK5+MSZrF5dnweDyDwXDqxG7jR9jEtIhoPX7yHKFQTEQikZSI3nh7iV6vMxgM507v/2HpJyz7MTp6aEvi4InBodEff/ZrlVpVU6Pdv2fDls0/EtGBvb+FNGsV1TohqnUCE3zm1N6AwAgvr0CFvUNZaQn7WWp0LJYqK8rJ1btCVXbtynkv78A2MT2ISKm8lwmb+bFgPg+P5fqRyWxnzl0pEsv4fAEReXkHfrV0JxkMVy8fX/a/uUzM5t9WBIdGhreIHTJ8auLgl7lcLo9nU1ur3/Pnva1p2MRY0LbknwKDW0a3Thj6wrTEZyfodDqRWMLl8ubOfCkjPcUaIwIAAAAAAAAAwJPjATdRPhUyb91QVypz72QYWzLSU44d2arT/b19s1JZUl1dder4rjof1Gg0N66dqdFW83h/Px9v185f169dWJCfzeXZEIeTlZnyw3ezdu/8jX0y7Mea91HSqeO7lOXFXB6vpqY6Nyf9l58W/br+vqfMNRojFImlUrlYLBOLZczGwQKBSCyWSSS2UpmcfT9Gf2xa+fvGb/LzMvV6nURqy+cLamq0zKETx/bt2LpKpSzV6/WlJQV/blvzzeL3zpzaYyBSKByYmOysVG215k5Oummfebm3y8uL6+9r0cBYLF08f7RGW33h3CEiOrBvS25OemWF8vyZQ+bOT6P53M64Vl1dlXv/ed29k1FVVZF5O82snEtK8osLc//pKMv1I5ZI5XInudxRIrElIg6HK5c7yhVOcsV9d6N/9snUrZuXF+RnE1FtbW3e3ds/r/mszj3UbGIaVViQo65U3s3NMm3Mv5tVXV2Vm5Nh2vjVwhnbk1cU5OfwbPgisaSyUnnl0rHM2zeNASUl+UUF9z1BFAAAAAAAAAAA/h04nj5tH3cOAAAAAAAAAAAAAPAv9LTeAQ0AAAAAAAAAAAAATzgUoAEAAAAAAAAAAADAKlCABgAAAAAAAAAAAACrQAEaAAAAAAAAAAAAAKwCBWgAAAAAAAAAAAAAsAoUoAEAAAAAAAAAAADAKlCABgAAAAAAAAAAAACrQAEaAAAAAAAAAAAAAKwCBWgAAAAAAAAAAAAAsAoUoAEAAAAAAAAAAADAKlCABgAAAAAAAAAAAACrQAEaAAAAAAAAAAAAAKwCBWgAAAAAAAAAAAAAsAoUoAEAAAAAAAAAAADAKlCABgAAAAAAAAAAAACrQAEaAAAAAAAAAAAAAKwCBWgAAAAAAAAAAAAAsAoUoAEAAAAAAAAAAADAKlCABgAAAAAAAAAAAACrQAEaAAAAAAAAAAAAAKwCBWgAAAAAAAAAAAAAsAoUoAEAAAAAAAAAAADAKlCABgAAAAAAAAAAAACrQAEaAAAAAAAAAAAAAKwCBWgAAAAAAAAAAAAAsAoUoAEAAAAAAAAAAADAKlCABgAAAAAAAAAAAACrQAEaAAAAAAAAAAAAAKwCBWgAAAAAAAAAAAAAsAoUoAEAAAAAAAAAAADAKlCABgAAAAAAAAAAAACrQAEaAAAAAAAAAAAAAKwCBWgAAAAAAAAAAAAAsAoUoAEAAAAAAAAAAADAKlCABgAAAAAAAAAAAACr+E8UoCUS6TfLdr8y7ZNHMNbgoUnL1xyZOXfFQ/bzKHN+0lhqDh9SbFy3ZWuODBw85vGmAeZ6QtYPAAAAAAAAAADQf6QA7eTsKrNVuLr5PIKxWkZ34AuEgUERDo5OD9PPo8z5SWOpOXxI3r7BAoHQ0yvwMeYATfCErB8AAAAAAAAAACAiG5Zxb733dfOI2Prter1uxdIPjx3dY9GsnmKXzh/18AjIykotKS56YIBIJPr86+T01ItfLpxh7WQe5VgW9MjmkFU/HM7DDAEWxPK6N7p+AAAAAAAAAADgkWFbgFapSpXKEg5xbGz4YonMYKitUJUTkU5fo1arrJnhU2bTxuWbNi5vIMDe3tHW1t7JxfMRJPMox7KgRzaHT+n8/GexvF6Nrh8AAAAAAAAAAHhk2Bagly75kHkREtrivY9W5mSnz3x7hNWysrCC/FxleUle7u3HnQgRkcxWTkQ2PH7DYRbJmeVYTx1Lnde/dX7+rXC9AAAAAAAAAACeOhxPn7ZmfSAoOOyDOWuyMlNmvTOyzqGkSTNbte26+NMpqSlXjI1jJ7zbrv0zn86dcCsjlYhc3Tw+/PjHSxeOcjic5hHtxRLbClXpmVN71676ok5vHTv36tXvJRdXH4OhtiA/e8eW1dbb6KPhsfr0f2HQc5P379mwfu0SpoUvEMye96NEYjttcl+mJTau27iJH/EFQiLS6bTHj25f8f38OqPMX7TB3cO//ug6Xc3q5XOOHt5lwTNiP9bAwWM6dE5U2DvrdDV3sm9uXP9VyvXL5g5nJ5ePmzgrODRKKBSXluT/ue3HfoPG52SlLZw/jQnoO2BE/0FJq5fPPXFsn/FT099Z7OEZMH1KIvP2Uc4hm36ee/7lvgPGnj65Jz3tUs/eIxT2ztWaqrTUC0uXzFSrK00/Zam1aieXjxz9VkhYa5lMUaPV3Mq4unbVp7l3sk1jGr5evfsNf/a5V35Zt8jOzjGh22BbO/sqdeW1KyeWfvOhXq9nYqa8Pj+sRbvPP5nEfCUZI8e80aFz4reL37x08TT785qzYG2tXv/ZJ6+Me/mDsObtBAJRSXHe9uSVhw5sN8bweLwRo6ZFt06wtXOoqakuyM/evmXVqeMH65y7j2/AiFFv+viFcbncG9fOBIdGZdy8bFw/bK4Xm/VjqTkEAAAAAAAAAACWLPkQQj5fIBZLE7o/a9oYGd1RKBQZDLXMW/+AMInULja+T0xcb5mtgsfjyRVO3XoOHzHqNdNPde85aNT4Dzy9gnQ6LRF5eQePmfBhTPuuFsyW/VgqVamNDb9L96HhLaKZlnET3/fwDKipqTbGVKkr1GqVulKp0ahtbARxHQfIZLZ1BqqsVKorldUaNfOWiWf+VVdXWfakWI41ZPjExMEvOzl7EJFQKA4MbvnKtM9d3TzMHW7qG59HRneUSGx5PBsnZ88Xx7yrUDi7ufsaAzw8A0QiibdvsOmnXFy9HJ3cJRIp8/ZRziH7fgKDIoa+MM3B0Y3L5YklspZRHSa+Ots0wFJr1cnJZdbcNW1je8rljlwuVySWhjVvN+GVuaYxjV4vD88AvkDYq++oAc9OkCucuFyeVGbXNrbn2AnvGmNs+HyJxLZrj8GmPbdq00UgEFVV3SusszkvJyd3dw+/d2ctbd22m1gsIyIXV+9hL77h5ORijJn21sKuPYbZO7gaDAahUOLrF5Y06eP28d1N+/Hw9H7j7SWhYW3EYqlQKGbWkkgsNQawuV5s1o+l5hAAAAAAAAAAAFhiuwUHGyeP72rX/pngkGhjS2xcN7nCKT8v8/atm3WCszJTdmxZrdVWde0xtEXLuJj2vdat+Yo5xBcIBg6ZRMRJ3vT95t9WENH4l9/v0Dmx38CxJ4/vt2DCLMc6cnBn67Zdo1p1fnH02++9OTymfde2MT10uppf139l7OfihZOvTepDRDweb+HXyfYOLm7uXjfTrpuO9fGsJCLy8Q2Ys+CX3Jz099563rLnYu5YEom0W89hBkPtru0/bfp1uauL+4RX5vr4NRs+4rWvFr3NfqzIqJigkCi1WpW86fu/Du9sH99jwLMTbe3szc35Uc4h+34cHN1uZVzdtf2n/LzsHr2Hx3Xo1yz83n8asOBaHTvxAydnD2V5yfbkFUcO7QgIbNat59DCwjvGAPbXy9nFM+X6mX27NyiVZb37vRQZ3THc5AmiJ4/tiozuFBrWxtjSLrazvYNr3t3baanXzD0voUji5RNy8fyRDeu+rKhQvjtrmbuHf1Sr+L27NxORk5NL84hYrbZ63eoFhw5sd3ZxS5o8OyQ0Ojau1/G/9t479wkzFfbOBfnZW//4QSAQDn1+mlAk0ZhUw9lcLzbrx1JzCAAAAAAAAAAALFnyDuizp4+WFOc7u3ga7xRuF9uDiG6mXqwTqVSWLJjz8olj+86dObZw/rTi4jw7uWNQcBhztFefYTJbxaULR5nKFxH9sPSTosJcD88AvkBgwYTZj7Xs24+Ki/M8PANemfbJ0Bde4/Fsjh3ZeurEofod6vV6laq0gRFrtNVEVPv/t4RbVcNjdek+UCyWZd66/su6b2u02pyczNU/fKLX64NCoswaJap1RyI6fnT7rh0bKypUe3b9nrxp6cOk/cjmkE0/+XmZs98fc+LYvlsZqcu+nVNSnCcUigMCQ5mjllqrCnuH4NBWBoPhl58W7dr5q1pdeeXy2a8Wvf3zj18bY9hfr4z0K/PnTD514tCNaxe/XjRDrVbZ2Tkajx47uqestNDZxSu8+d8fjI3rTUTXr97bfMOs8zp6KHnxZ9Nz72Qry8vPnNxLRMw+GETE4XI5HK62uuriheNEVFiQ983iGRfOHjx6eIvx4zHtuwYGR1aoyhYtmHLk4M59u/84d/YgEWmrNXUGYnndG1g/lppDAAAAAAAAAABgyZJ3QBNRWur5mPa9OnTqd+3KeSIKDG5JRH8d2V4nLP9ulukuunm5tx0d3Ty8/JnbFb18gonIyzvovQ/v1TElUlsez8bPL8h4k6ZFsBxLra7c8NOiCa/MaxvTg4hysm+uXLbAgmk8Fh6e/kSUnXVvI+CM9BSlstje3kUkEmk0dct//0Rh70JEKTfOGVsOHdw+YrQZ91A/yTJv3TB9W15W5ODoZif/+/5uS63VsPBWfL6grLSwgc2j2V+v9LRLxtd6vV5ZXuLmbmsak3L9TExc744JideuXiCioJDI2lr9/j2/GT/F/ry02uofVy00vt20cXlOdrrxLunCgryc7JvePsFzF6xPuXH2ysVjRw7t/HLhDNPzio3vxeFwjv+1Iz8vl2kpKc4jIp2uptF5M5cF5xAAAAAAAAAAANiwcAH6+NEdMe17BYdGE1FM+65yhVNRYS5TjDal1+tM32o0lUTE4/KYt2KxlIhcXL1dXL1Nw6qrq5TKMssmzH6sUycO9U286esXRkQn/tph2TQeC6FIQkSVFUrTRmabXXcPH9Mn1DXMxoZPROVlxcaWGq3WYDBYLNHHSl9733Pnag0GIuJwOMxbS61Vhb0TEakrlQ3EsL9e+vtLt8wO7Nz//34R0aEDf7Rr3yukWSsi6pjQ207umJ2Zkp11yxjA/ry0Wk2NVmvaUmePjmXffjB6/PsBQRFt2nVv0677sBffOHd6//LvPjYGuLv7EdGBvZuMLXK5IxHV1lr+iX8WnEMAAAAAAAAAAGDDwgXoC+dOFBfddXL2DG8e1a59DyK6mVZ3/w0i4nI5pm+lUjsiKi0tZN5Wa6qIaMfWVRt//s6y6dXHfqx27RM8vYKY17HxfbYl/9S0EZkKJo9n4ZlvwljMHrtSmZ1po1AkMRgMd3Oz2I/CbIwgV9zboIAvEBBx7o8ykMnfGBhNLudZag4fvgSdtscAACAASURBVB9LrdUKVRkRmT52rz5LXS8iunblfEF+tqubT1SrWOam/iuXT5gGWPA7mJ11a+6s8SGhLWLje/kFhPv6NYvvNIDHs1n6zUdMgEgs1em0uXeyjR/5+/GVnDpLyALXy4JzCAAAAAAAAAAAbFhyD2hGWsp5DofToXN/Zv+Nk8f+rB/j4urD4/1dfOQLBJ7eQTU12ssX/96C9k5OOhH5+DazeG71sRxLIpEOH/GGjQ1/z5/r8nJve3kHjZ3wTtNGLCrMNxhqFfYuEklD1UaLaHis3DsZROTlHWxs8Q8IsbNzVClLzdpnoLQkn4iCTXbR7dotkcu9b2lptRoicnbxMrYEh4TbO7iyH8WUpebw4fux1Fq9fvWcTlejsHeOjIr5pxhLXS/GtSsniahzl0GBQS1rarR7/txgetRS5+Xq5vHBnOWduvRJTbny48qFcz4Yu3blPCJi/pMEQ6vV8Hh8B0cn5m1oWIR/YAsiEvCFdXp7+Otl2TkEAAAAAAAAAIBGWb4AffTQViKKje+jUDiXluSfP3u8fozC3nn6O4udXdxc3Tymv/2lra195u3rev3f/+P+4P5ktVrVPCL25SmzZTJbIgpvHvXBnOULvtho8WxZjjXx1TkOjm6Zt6+vW/PV+rWLdDptXMf+bWM6PbBPpvZqw3/wM+j0en1xUZ5YLH19xhcent4PjLGUhsfav2ezWq3yCwh//sVX+QKBt4//6KQPeDzezdQLZo1y/uxhg8EQ36n/M32GikSihK79+g0aXycmNyeDiFpExvUfONI/ICTx2dGvvr6Q2bvjgR7NHD58P5Zaq0VFBbfSr3C5vBfHvBPRsg0ROTg6TZoye/o7i40xlrpejH27Nur1+ug2XaQyu6zbN0qKi6xxXmHhrYKCI4c+Py2uQw8i4gsE/oHN6f7tNUpLCjgczktjZji7uHXpPuDlKfOZe5zl9s51emN/vf5p/Vh2DgEAAAAAAAAAoFGW3wjiyuWzhQU5zL2uN00e5FVHeIvYz75MJiIOh6PVVm/7Y4XxUFlpyc6tqwcOmRQb17ttTE+tVsPsSJv+z701GZuxOnfp2zKqQ3V11U+rPiWiixdOnvhrZ4fOiUNfmHb65GEmpkVE6/GT5wiFYiISiaRE9MbbS/R6ncFgOHd6/w9LPzEd9OihLYmDJwaHRn/82a9ValVNjXb/ng1bNv9o8bNreCyNRrP3z5/7D0p6pu9L3Xu9wFT9lOXF63/60qwhrlw+m3L9bLPwNs+PfHP4i9M59XZOIKL9e5P7DRwvVzgNHjZl8LApRKRSlVZVVYpNNp14LHP4kP1YcK2uXfXp9He/dXbxnP7u/6qqKoVCEZfLKynONwZY6noxcnIyc7LTfP2aEdHF80esdF4H92/r2nOYj2/ohFc+GZM0i8uz4fF4BoPh1IndxpgDe38LadYqqnVCVOsEpuXMqb0BgRFeXoEKe4ey0hLTDhu4XmzWj2XnEAAAAAAAAAAAGsWzk3ua9QGdvqZTQmLmrWunTuz/pxhfvyAfv2ZElPz793dybpse8vIOaBPTPev2jZupFxUOzjyeTVFh7qYNS04eP2AalnrjUmlJrpOTu1giEwhE5WWFJ4/9+e2X71nj0XaNjhURGRvSLHrf7g2HD/797MGL54+1aZcgEkn/3P4z0+LtGxjXoZ9QKObzBUwFlsez4fMFfL6wuDjv5LE9piPeuH6+Vl/t6OQmEkmFInGtXn/j2pmbqVcsfmqNjnX92nlDrdbZxUskktRoq2/furby+9mmD6Nj6cypfV7e/nJ7Jy6XV1x09+C+30JCo9Vq1e6df2/sYDAYsm5f9fQKFIokHA4nKzNlxXcfubv72PAF2/5Yw8Q8ljlsoB+pVBIZ3fHsqX2pN+5VXUNCI5xdvX7/9fuqKjXTYqm1Wl5eevHsQWcXD5lMLhZLq6oqr185ufSb9ysrVMaYRq+Xs4traLPWRw9vycq8aWxsEdFWIrXbunlVnRH1uqrwFjEFeVnLl86t1dd94h+b84pp372iovzQ/uQGzuv40Z0urh62dvZCkbimRluQl7V9y8otm9cYA3KybwkENu7ufjY2gvKyokP7flu5bIGjk7Ovf9iZk3vKyu4rQDdwvViuH8vOIQAAAAAAAAAANIzj6dPW4p126T5g1LgPykoLp03uW+dQbFy3l6fMv3HtzIK5ky0+Ljw5Vv18srgo982pgx53IgAAAAAAAAAAAPDYWH4PaCIKb96OiK5fO22NzgEAAAAAAAAAAADgqWD5AnTXHolRrROqq6u2/4H/rg4AAAAAAAAAAADw32XJhxC+OPr1qFadHRzdOBzOru3rc3IyLdg5AAAAAAAAAAAAADxdLFmAdnXzsXdwKS66e/jA71v/WPvAmDs5GVVVFXl3b1twXHgCFRfdLSrMfdxZAAAAAAAAAAAAwONklYcQAgAAAAAAAAAAAABY5SGEAAAAAAAAAAAAAAAoQAMAAAAAAAAAAACAVaAADQAAAAAAAAAAAABWgQI0AAAAAAAAAAAAAFgFCtAAAAAAAAAAAAAAYBUoQAMAAAAAAAAAAACAVaAADQAAAAAAAAAAAABWgQI0AAAAAAAAAAAAAFgFCtAAAAAAAAAAAAAAYBUoQAMAAAAAAAAAAACAVaAADQAAAAAAAAAAAABWgQI0AAAAAAAAAAAAAFgFCtAAAAAAAAAAAAAAYBUoQAMAAAAAAAAAAACAVaAADQAAAAAAAAAAAABWgQI0AAAAAAAAAAAAAFgFCtAAAAAAAAAAAAAAYBUoQAMAAAAAAAAAAACAVaAADQAAAAAAAAAAAABWgQI0AAAAAAAAAAAAAFgFCtAAAAAAAAAAAAAAYBUoQAMAAAAAAAAAAACAVaAADQAAAAAAAAAAAABWgQI0AAAAAAAAAAAAAFgFCtAAAAAAAAAAAAAAYBUoQAMAAAAAAAAAAACAVaAADQAAAAAAAAAAAABWgQI0AAAAAAAAAAAAAFgFCtAAAAAAAAAAAAAAYBUoQAMAAAAAAAAAAACAVaAA/YQaPDRp+ZojM+eueLz9TH9n8er1p1avP7VszZGXxr5p1bEs3s+j9JA5x8Z1W7bmyMDBYyyb1WNn1vqxOB/fgO9W7h817q1HPC4AAAAAAAAAABihAP2EahndgS8QBgZFODg6PcZ+1GpVZaWyWqMWCIQJ3QYHBIZabyyL9/MoPWTO3r7BAoHQ0yvQ4ok9XmatH4vz8Q0Wi2Vubj6PclAAAAAAAAAAADBlY1b0kOET28f3Udg763Q1pSX5Rw9t2Zb8k5UyE4lEn3+dnJ568cuFM6w0hAU5u7jNXbBeJJbWab96+cTn86Y2ocNL5496eARkZaWWFBc9MIDl/DTaT8O++3oWEfF4vLmfrvPwDPD1D81IT7HSWBbv51GyzPXicKyV32Ni1vqBh/R0/WYCAAAAAAAAwH+EGQXoF0ZO7dnnRSKqrFRyOBw3d7/Bw161seH/sWmVNTKzt3e0tbV3cvG0RucWV6WuVCpLamq0Nny+WCwzGGorVOUGMqiUJU3rcNPG5Zs2Lm8ggOX8NNoPG3q9vrjorodnAKfBCqlFxrJgP4+Spa7XvxLL9QMP6b+8xgAAAAAAAADgiWVGATq+8wC9Xrdpw5IdW9cTUc/eQ4a+MK19h75WKkDLbOVEZMPjW6Nzi6uoUM2YNpiIQsMi3p21Iic7febbI6w64tM1P4DrBdaGNQYAAAAAAAAATyC2BWgf3wCp1C47M4WpPhPR7p2/yRVOUqmdMSZp0sxWbbsu/nRKasoVY+PYCe+2a//Mp3Mn3MpIZVp4PN6IUdOiWyfY2jnU1FQX5Gdv37Lq1PGDxo/MX7TB3cOfee3m4bd6/SnmtU5Xs3r5nKOHdxkjO3bu1avfSy6uPgZDbUF+9o4tq48d3WM8OmrcW9GtE86fPRjfaYBKWbJ31/p+ieNt+Py/Dm/9ceXCOic4YNBLfRPHqZSln8+bnJ+Xy3Ja6tPrdERkMNTWP9S73/Bnn3vll3WL7OwcE7oNtrWzr1JXXrtyYuk3H+r1eiYmNq7buIkf8QVCItLptMePbl/x/fw6/bCZHzb9MHx8A0aMetPHL4zL5d64diY4NCrj5uWF86exP2U2Y/Xp/8KQ4VO53Lp7jt++de2j90az74fNHBKRq5vHmKT3/QNbcLm8osI7f277ccjwqVevnFi65EP25zVy9OvxnRO/+WL6lctnjY3jJr7bpl2P92cMZbbasNT1MurVd1jP3iMU9s7Vmqq01AtLl8xUqyvZ50wsvl+Mhq87m++Oq5vHhx//eOnCUQ6H0zyivVhiW6EqPXNq79pVX5iVMKPh7zJLXl6+L417188/3ECGjJuXr1899cCwgYPHdOicyGwldCf75sb1X6Vcv1wnxk4uHzn6rZCw1jKZokaruZVxde2qT3PvZDNH+w4Y0X9Q0urlc08c22f8yPR3Fnt4Bkyfksi8Zf/70/C5s1nzLNcYy7UBAAAAAAAAAGBBbB9CWFJcWFurl5iUm4no1/VLV//wmfEtny8Qi6UJ3Z81jYmM7igUikwLstPeWti1xzB7B1eDwSAUSnz9wpImfdw+vrsxoLJSqa5UVmvUzFu1WqWuVDL/qqurjGHdew4aNf4DT68gnU5LRF7ewWMmfBjTvqsxwM3NR2Hv3KFzIo/Hc3RyHzzsVT5fIBSKO3UZpLB3qHOCMXG9hEKxk7NHp4T+LOfEXB6eAXyBsFffUQOenSBXOHG5PKnMrm1sz7ET3jXGVKkrmPPVaNQ2NoK4jgNkMts6/bCZHzb9EJGHp/cbby8JDWsjFkuFQnFkdEeJxLb+TtYNYzOWSlWqUpaolKXMvwpVGdPu6xcmEonY98NmDolo6vSFzcLbCoViPl/g7uE/ZsKHtnb27u5+Zp2XXOEkEkmahbc2bfTyCRFLZCKRmH3OLNczEQUGRQx9YZqDoxuXyxNLZC2jOkx8dbZZOROL7xexuO5svjv+AWESqV1sfJ+YuN4yWwWPx5MrnLr1HD5i1Gvm5tzod5kNHo83ZfqikGatBEKRUCgOa97u2aGv1g8bMnxi4uCXnZw9iEgoFAcGt3xl2ueubh6mMU5OLrPmrmkb21Mud+RyuSKxNKx5uwmvzDUGeHgGiEQSb99g00+5uHo5OrlLJH9PI8vfn0bPnc2aZ7nG2KwNAAAAAAAAAADLYnsHdEWF6lb61cDgltPfWbzi+7llpQ/Y2vjk8V3t2j8THBJtbImN6yZXOOXnZd6+dZNpcXJyaR4Rq9VWr1u94NCB7c4ubkmTZ4eERsfG9Tr+114m5uNZSUTk4xswZ8EvuTnp7731fP2x+ALBwCGTiDjJm77f/NsKIhr/8vsdOif2Gzj25PH9ppG7dqzNzkybNHWBSln2+iv9Pv5snZd3cFBw8zOnjpiG5edle3oF1dbqb9+6znJOmsbZxTPl+pl9uzcolWW9+70UGd0xPCLWePTihZOvTepDRDweb+HXyfYOLm7uXjfT7kuJzfyw6YeIxk6YqbB3LsjP3vrHDwKBcOjz04QiiabKvFtu2Yx15ODOIwd3Gt8q7B3mL/xNLJFduXRMo9GYlTM1Noc9ew/x9ArSVmt2bltz8vjuNu269O43WiyRmXVSRKRUlhKRzFZBRBKJlLkTWSgU6/U6452wlrpeDAdHt1sZV3dt/yk/L7tH7+FxHfo1C29rVs5svl/E+rqz/O5kZabs2LJaq63q2mNoi5ZxMe17rVvzFfuc2X+XG5Y4eIyrm49WW33syNbsrNTwFjGt2nThcO77G5tEIu3Wc5jBULtr+0+bfl3u6uI+4ZW5Pn7Nho947atFb9+bn4kfODl7KMtLtievOHJoR0Bgs249hxYW3mGfjFHDc8j+3Bte82zWGMu1AQAAAAAAAABgWWbsAb1q2dzJry2IiIz/9Ivf01Iv7N+z8dyZY6YBZ08fLSnOd3bxDG8Rfe3KeSJqF9uDiG6mXjTGcLhcDoerra66eOE4ERUW5H2zeMbYpPf/OrKtznA12moiqn3QXhZE1KvPMJmt4tyZA0zVhoh+WPpJs/C2Hp4BfIGgRqs1Rubl3r6be5uIVKpSIqpQlRORjY2gTodfL3q7X+LIgvys0ycPs5+TJshIvzJ/zmTmdVrKjCXLdtvZOdYP0+v1KlWpvYPLP/XT8Pyw6SemfdfA4MgKVdmiBVOYXUeCQqLax/fRVmvYn45ZORtNe3ORWCLLzkz58vM3m9BPw3MY3rwdEZ05tYdZG1s2/ygUivsmjjP3dMpKC4hIKrXz8PT+6JN1aakXPp83VSgUa6rUTciZzfXKz8uc/f4Y5vWyb+c0C2vj4OgWEBiakZ7CMmc23y/2153Nd0epLFkw52WmOn/uzLFF32xxdHQLCg574F8OHoj9d7lhYeFtiWjf7l82rPuWiPbt/uP1GYsiozuaxnTpPlAslt1Kv/LLum+JKCcnc/UPn7w/e3VQSJQxRmHvEBzaymAw/PLTImYrjCuXz5puw2KWhueQ/bmz+d1oeI2x/+0FAAAAAAAAALAgMwrQOTmZM995cciwiW1je7RoGdc8Ivb82UNfm9w2SERpqedj2vfq0KkfU4AODG5JRH8d2W4MKCzIy8m+6e0TPHfB+pQbZ69cPHbk0M4vF84wN28vn2Ai8vIOeu/DpcZGidSWx7Px8wtKS71mbodEtC15bRM+Za70tEvG13q9Xlle4uZuKxKJjDcCPzKx8b04HM7xv3YY97wuKc4jIp2uxqrjTnvzM7+A5sXFeV8tnG66cTN7Dc+hwsGFiC6e/8sYc/XyySYUoIsK7xKRRGoXG9dTIBQFBrfk8XhCoVijMe8OcfYyb90wfVteVuTg6GYnt2ffA5vvl2Wve/7dLNNdqvNybzs6unl4+bMvQFvquyxXOBsMhn27fjW2XLpwpE4B2sPTn4iys1KNLRnpKUplsb29i3H9hIW34vMFZaWFTdiE2lzsz/3hfzcs9dsLAAAAAAAAAGAWtntAM/R6/Yaf//fm1EErls7Kz8tu1abLpKlzTAOOH91BRMGh0UQU076rXOFUVJjLFKONln37QXraJZmtok277qOTZn2zfE/SpA/MzVsslhKRi6t3SLNWxn8SiW11dZVSWWZub4+S/v4yH7M7NpfLe/SZMHsiH9i7ydgilzsSUW1tU4rCLI0c/XpU6wR1pXLZt+8XFRU0rZOG51DAFxJR7p1bxgCttroJo2Rn3SQisUTmFxBuMBhEIkmnhD4CgUitrmha2o3S3z/ztQYDEXE4HLM6afT7ZdnrrtfrTN8y1XmeOevZUt9lPl+gq9GaLqpqTVWdGKFIQkSVFUrTRmbrZHcPH+atwt6JiNSVSrI+9udukd8Ni/z2AgAAAAAAAACYxYw7oE0dOfTn6ZMHP/8qOTK6k2n7hXMniovuOjl7hjePate+BxHdTLtY57PZWbfmzhofEtoiNr6XX0C4r1+z+E4DeDybpd98ZBrGVN94vAdnyJSWdmxdtfHn75p2Ck+7hueHDZFYqtNpjdsZE5Gbuy8R0YMqnkzBi19v6xKz9O43vEuPoTU12nVrPku5fvlhumqATq8jImcX9+ysv2vQAqGoCf1kZ93S63VisdTWVpF+85Kff3hEZDxfIKxSq5rQ28NfL5Ya/X6Zdd0bxeXe9ymp1I6ISksL64Q1sH4s9V3W6bQ2fIFMZltR8fcF4nDr/oGN2edaKrvvYapCkcRgMNzNzWLeMk/IbOxRnAaqV2dvwp+RLPs71ugaY/nbCwAAAAAAAABgQWzvgI6N6zZv4S9Rre499kqj0Wg0aqFQLBLdV91LSznP4XA6dO7P7L9x8tifpkdd3Tw+mLO8U5c+qSlXfly5cM4HY9eunEf/f9O0qaLCfIOhVmHvIpE8oBJ0JyediHx8m7HMv1E8Hm/QkHFxHXpYqkNra3h+2NBqNTwe38HRiXkbGhbhH9iC/v8O4rrB1RoicnLxaGq+1C6286AhkzkcztbNy/46srvJ/TSK2bs5qtW9P420btulaV1VqSucnD2dnD0ybl4uKrzTomUch8Np2r2xD3+92GDz/TLrujfKxdWHx/u78MoXCDy9g2pqtJcvnq4T1sD6sdR3WVlewuFw4jv1Nrb4+tXtM/dOBhF5eQcbW/wDQuzsHFXKUuNeFtevntPpahT2zpFRMf80llarISJnFy9jS3BIuL2Dq7k5W/Z3rOE1xv6394EGDBrVqUufOo0x7bsOHprEF9z3dwUPT+9hL0wOCW3R1PMAAAAAAAAAgH8VtgVodw8/D8+ApMkf90t8kYgkEum4ie86u3iWlxXV2YT06KGtRBQb30ehcC4tyT9/9rjp0bDwVkHBkUOfn8aUevkCgX9gc3rQf//X6/XFRXlisfT1GV94eHrXOXpwf7JarWoeEfvylNkymS0RhTeP+mDO8gVfbGR/8qZenTYvcfDE8ZPmtI3p1Hj0P+PZ2BARh2Pe3iYPxOVyiciG/+CbjhueHzb9lJYUcDicl8bMcHZx69J9wMtT5jP3Tsrtnet3kpt7i4iiW3dxcnIRiUTtYjubNZaPb8BLY98XCEWXzh/dsvnHBrJtuB82Lp0/SkQxcb2fHTo+KDjsuedf7pgwsAn9EJFareLzBRwO98K5IzlZacyd1BUV5U3Imf31ehhsvl9mXfdGKeydp7+z2NnFzdXNY/rbX9ra2mfevl5/a+8G1o+lvsu3M64S0TN9XgxvHkVEHRN6x3fsXydm/57NarXKLyD8+Rdf5QsE3j7+o5M+4PF4N1MvGGOKigpupV/hcnkvjnknomUbInJwdJo0Zfb0dxbfO52cDCJqERnXf+BI/4CQxGdHv/r6QhsbvlkJW/DcGQ2vMfa/vfVNfOXDZ4e+Mibpw85d+hobo1u3n/DKJ/0HJb06bb5p8LS3vuzdf/TU6V/UKUwDAAAAAAAAwH8T2w0BNv+2Ijg0MrxF7JDhUxMHv8zlcnk8m9pa/Z4/f64TeeXy2cKCHObewJsmD85iHNy/rWvPYT6+oRNe+WRM0iwuz4bH4xkMhlMnHnBL7NFDWxIHTwwOjf74s1+r1KqaGu3+PRuY8mVZacnOrasHDpkUG9e7bUxPrVbD7KaaXm9EllzcvImIy+X5+YedPnnY3I/LZLYz564UiWV8voCIvLwDv1q6kwyGq5ePL/vfXPb9tIhoPX7yHKFQTEQikZSI3nh7iV6vMxgM507v/2HpJ6bBDcwPm34O7P0tpFmrqNYJUa0TmA7PnNobEBjh5RWosHcoKy0xHWvvrl+79Rju5OyxYPHmWr1eIBSVl49jttFgM1bXHkNktgoiCmsR89XSncZuy0oLPnx3VBPOvQF7d2/ukJDo5x8+YNCEAYMmsJ/8+iorysnVu0JVdu3KeS/vwDYxPYhIqbw3M5a6XpbC5vtl1nVnI7xF7GdfJhMRh8PRaqu3/bGifkwD68dS3+WtyWti4no7OLrN+GBZtUbNbPdch0aj2fvnz/0HJT3T96XuvV5gKu/K8uL1P31pGrZ21afT3/3W2cVz+rv/q6qqFApFXC6vpDjfGLB/b3K/gePlCqfBw6YMHjaFiFSq0qqqSnEjG3fUZfHfsQbWmFm/vXUwm7RwOBxf/zA68PdDZf0DmzM3v7u4epkGOzi6EZHMVuHm5mncBgcAAAAAAAAA/rPMuFH3s0+mbt28vCA/m4hqa2vz7t7+ec1n27esqx+Zcv0s8+L0yT31j877KOnU8V3K8mIuj1dTU52bk/7LT4t+Xb+0fuQfm1b+vvGb/LxMvV4nkdry+YKaGq3x6NY/1q7+YW7W7Rs1NdUCgai0JP/gvt/mzZ5oDMjPz65SV2TeTi0qzFeWl+TdvU1Ed3MzqtQVmbdu1Bnr+NGd1dVVRYV3Du5PZj8nRmKJVC53kssdJRJbIuJwuHK5o1zhJFfcu6s0OytVW61h/tO9UV7u7fLyYrW6knkrFImlUrlYLBOLZczT5wQCkVgsk0hspTI5+/lh08+JY/t2bF2lUpbq9frSkoI/t635ZvF7Z07tMRApFA51xlKWl/+4cl5+XiaHwzUYam9cO30z9Rr7sa5dOclsvysQCOVyR+M/hcLZuIEDm37YzCERfb3ozauXT1Rr1Hq9rrjo7rnT+9lcxPounj9ao62+cO4QER3YtyU3J72yQnn+zCFjgKWu1+2Ma9XVVbn3n9fdOxlVVRWZt9PMyrnR7xeb687+u5N1+8bZU/uqqipqa2sL8nPWrV5w4dyJ+lk1sH6IxXeZjbLSkhXff5idlVpToyUOJz3t0jeLp6vVqrt3M03Dfv91xeZf/1dUmGswGKo16ptpF5d8Mb2wIO++k8rMWDB7/MXzhytUZUKhqKqq8uK5w59+fC8fvV7//Tfv3c64VlVVqdPV3L51bcmi6bfTr5SWFhjXIcs5bPTcWa55RsO/mex/e+s4dmSbpqqytCT/8IE/jI0H9/1RkJ+trdacPLbLNPjcmf06nfb6tdOoPgMAAAAAAAAAEXE8fdpavNMu3QeMGvdBWWnhtMl9G48GsLJm4ZHvzFx+O+PaR++Pfty5/HvExnV7ecr8G9fOLJg7+XHnAgAAAAAAAAAATygLbFVcX3jzdkR0/VrdB5EBPAJ+/kFdug8wbXF2dicijabu7aIAAAAAAAAAAABgVWz3gGava4/EqNYJ1dVV2/9YZfHOARo1eNgrLVrGBQS2WP3Dp3q93snJ5Zm+I4noVvrVx50aAAAAAAAAAADAf4slC9Avjn49qlVnB0c3Doeza/v6nJzMxj8DYGnnTu9vFt62Y8LANu26q9UqWzt7gUCUn5f1+28/PO7UAAAAAAAAAAAA/lssWYB2dfOxd3ApLrp7+MDvW/9Ya8GeAdg7sG9reXlx3wFjPb0C7R1cNVUVN66dXv3D/BqttvEPA2t3cjKqqiqYZ+sBAAAAAAAAAAA8kFUeQggAAAAAAAAAAAAAYJWHEAIAAAAAAAAArRXgUwAAIABJREFUAAAAoAANAAAAAAAAAAAAAFaBAjQAAAAAAAAAAAAAWIUZDyEUKuwinxvOFwqNLZWlxRfW/WyFrB5A7uftFBScvnf/v2wsMJdjs6CQrs9wuX//7cRAVJKZcWPr9sebFQAAAAAAAAAAANRnRgFa7u0ptXc0bdFptZbO5x8Fd+lh6+x69/IldX7Rv2ksMJeNSCyUyDjcezfvKzx9HmM+8F82eGhSr74vZWWlzp057nHnAhYz8dWPolp1FoulRFRbq1+7ct6BfVsfd1JPgenvLI6IjCcirbb66KHkH1cubFrMo/Sk5QMAAAAAAPCvZEYBmohDRMWZ6Zc2bLRSNv9E7u8jc3ZRl5U8gorwoxwLmiD/wuX8C5eZ11wbXsfX3uA83oTgP6xldAe+QBgYFOHg6FRSjF+Mf4OXxr7ZPr6PwWCoUJXpa/V6na68vPhxJ/V0UKtVlZVKG56NUCRJ6Db46KGtGekpTYh5lJ60fAAAAAAAAP6VzCpAExHV6vUNB7i3jvJpHSOykxv0+vK83JTdOzXFZfXDZB6uPjHt5W6efLGYw+Fe+P2X8ltZ/9Snd6u2HOLkp15vwli+HePcm7cUSu1q9bqKwvy0g/sq7uQ1kP8Dx5K6OQd0SpC7edoIhbrq6rK7OWl7d1eXKo0BYif71iNG8YVi00/V6vXXd28tuFw37UaxnEOGT4c4/5j4O5fP39y919yBiIjD47Z87jmFp0/m2ZO3Dx5+QACX45fQ2S0kTCCV1ep1VeVlWWdOFly+1oSxGj0vc8eq1ekNBkMTMpHauw19c71AKK3TnpVyYufyqczrNr1fjuw04vKxX09t/doY0PG59wIiuvy2eGRl6d+raPAbPzl5hNRNTK/bv2F2+rldxpbIbqPCYwbJ5C61el3R3bS/khcVZTVlDp8ocxas9fENZV4bDIbKivLzZw+u+H7e483qUbp0/qiHR0BWViqqz9YjEok+/zo5PfXilwtnPILhIqM7EtH25BW/bVjW5E4ecc6W8tzwiW1je9o7uPJ4PI1GnZlx7dNPprD/+HdfzyIiHo8399N1Hp4Bvv6h9Yu5bGLeeu/r5hGx9fvX63Urln547Oge887qoXP29vF/76OVzB3xRKTX6wsLsrdvWXXk4E4LZvK4PKVrFQAAAAAAGqVQ2PdLHCWoU6us1R89tPVm2nX2MUTE4XDaxnT2CwgTi2W1ep1KVXb18sm0VDPqWhZ+CKFLy+ahXZ+RKBz0NVoOl+vg7Rc1ZDiHV3cUt1aRrYaPdA0OE8psa3U1mgplTZX6n/q0kYodfPx02ursEyfNHSugW5eA9p3Fdvb6mmoOl6vw9Gk56Dm+TGLWWAI7WathLzr5BfFFIn1NDV8kcvYPbvX8SIGdzBgjdrDnC8W1ep26rMT4r6q8RKNUPmichrCcQ4bMw9WvXXsuz8bO1d3cgYiIK+BHvzDCwSeAy7Nx8PF7YEzYgAF+bdoLbeX6Gi2Hw7V1dgvv1d81KuL/2Dvv+KiK7YGf7b0k2d1seu+UBEgIPfTeRAUUFKk2fD7x2StY0Ifiz/ZUFFBEiiLSQXqR3gmB9Ep63U22797fHxc3myXcnU0umxDn+8kfm7tn7zlzZs7MnXPvnXFXF0q56NLlErOhSa+t1TfWmQyNAEAQNn1jnb6xVt9Ya5dRBsSxObyEvlOFMpX9oFwZzBfKfAKaM84SLzUAGJoa9I119j9dY62+qTm33mPo7L5jnpZ6+5tNegaTqQ7pPnbOCoFUQXu5PIxC4QcAjdp6raZO16QVS+SD0qb8a8lHHW2X59iyedWCxwfh9TfuKV5ePhKJl0IV4Bl1IpFUr2tsT/YZPG4zLfzrxY/HT56n8g2yWMxaTR2DwYyJ7xMaFunueaxWa011GQAwGHd9O4VaRqut02hqtZo6ve52/6zV1Gk1dRpNrU6nddee9tscEBguEIhsNitphsViUvuFPj7v9YGDR98LYzzM/dhWMRgMBoPBYDAYDApSmReXJ7BaLRpNreNfU6PGLRkAGDp8Urce/URimcVsAgbT20c9cMjEmFg38nVuPwFNTWjfAQwGs+jimdwDh7hSceLDM0XeiuD+/QqP/2WXkQYHxAwdzWAyKrIy8o4dMdQ2UJ8zODWVxeFWZN+wGoxu6WIL+YE9e9mslsxDe8svXWML+d2mPuAVEBI2aHDWnr3outhCPkEQNQW52YcP6KtqJUF+8WMnCeXeoQMGOp2nrqTo6qZNbnnsTlB8aCd21DgWmwsAcPep/t1g8biJMx6R+voZm7Q8kaTViTdbJFBGxpgN+vSdv9fnFQFA5KgRQYnJAd0T7etg0FUuGnW5xKTXblw+DQBUod2nPvtDTVnulk8fdRZiAABw+aK+E549vP4tirMZdZof3x5JIdBz0EwAuHho7bndX4u81GPnfurjF9ln9ILjv37YvnJ0PE1NmmcXjiI/9+rTf8HT7/XsNTg4JLyoMK9jDcN0GcQSGQCwWRxP6BJLmCy2xWzk8/kGg6Ht5/GgzbQwbuLMpN5pZpNx9461W3/7gTzoq/avKC/1vDHffPE2+SE6pttr76wuKc598+U7+mePk5N1+YN3nyI/z57z7+GjZ44a++iJY/uof9X5ue/aKgaDwWAwGAwGg3GLivKivbtc5CqpZfgCQXBojNGoP3Lw91slRQDQb8CIuITk6NjEzJuo+To6E9CSIH+Rl09TXU3ugUMAYNI05p061n38A6roOMfkacTgoUwWq+jS2dz9B1FOq4qKJQii+PxZd3X5duvGYnMrsm+UX7oGABadIWPnjn7zn5T7B7qlS1defeLzz+z/aovLsg7tS3xgpsRXfccJ2rIchLvlshM2LE2iUpPp47udMCCld8SANKOu6dKGn02axhZfJfeW+vo11lTe2Lcr+ZEnWv25SKVkMlma2ioyIwwARadPByUms7hct3ShlMstXXRhtVoAgCBsFDLhCWkXlEGaquK7CVAvAxKVMkEoVVTdunlu99cA0FRXfnzrx5Of+jY4pn9bre5EOJb94vmTRQWZMXG9Y+KS7AnopcvX2azWj99/Zt6Tb8QlpHC5/Nqa8l3bVh89vMvxPFOmPTFwyGS5l9JiMd8qztm84f8ybzj3YlKZbPac/0TH9RaL5WaTIT/v+ro1H5XealEvg4aMGTPhMZVvMEHYKiuKd29f6/TCPovFevTx55N6p0mk3mazsbKieNf2NWdPHXFLJrX/8HmL3uFweQBgsZhOndj1w7fO9xLGTpjxwEPPbFz/iVTqkzZ8mkTqpdc1ZaSf/ubLt60OCxn5qv2fWPB6WEQ3JpNVXXVr786fHpzx3PX00/YcHCKIfnbpH6lMNm/RW1ExiTyeoK62Yu/OnyZMnV9SlL3iw+dJgfGTHp04dcHaVctOn2zuwJe8stI/IHzJ4slu6XLp5w8/2eTnH0Z+VvuHrt1wu2e2WMxrVy11N/dH3cbmP/n6gMGTyPtwXC7vmzW31yMiCOLAvl/W//h/iFpQbF7w1Ju9koet/GhxVma6/YdzF76a0m/0R8sW5udlAYCv2v/t9366evkEg8FI6N5PIJQ0auvOnz2wbs2nThpd+hmFQWlTAGDHH6u2b/3JfvDO7HNwSPijj78YHBrHZDJvZpyPiknMy7lmbxu0Y7NZgbJ/Ruk3UGTQjGnu69atXTkwbYrMS+kk80+OLwwGg8FgMBgMxi0cl3SwWi2ahppLF44XF+U7iYWFR/dMGiCT+xAE0VBfc+XSiYL8HEeBSVMfJwjbnl0bBw4eExgYwWJzmhobLl04mptze229tOETAgIjd2//qa6u+ZX3lNS02Pjef+7ZWF52i0ZdKIhE4ikPzi8quAkMRlBwFJfLNxh0eTnpZ08fscsMGDQqLCJhz86fa6qr7Af7DRgRGd1j5x9rHQsCAN26907qk2bQN+3e8XNTU4tc3G1Qlq6llPH2VjKZrIb6KjL7DABXL5+OS0hmc9zI19G5BId3WDgAaMpvVx5XKg7vNxgAhHIvuwxbyJf6+us19ajZ5x4JAqlcW1muLW4xE0bRJZDLAaDJobZMDVqDVsOXytzSdScNBcUEQTBZLKfjLHZ7nyFCKReJOEAdlNjHZrXkHKPypF9CDxaHK5R5qbs7PxhfdvlK/tkTlzdtsJktd/t5U2WVzWqR+gaEDx/Kk0u9IkO7TXkAAOpKWlmwm0IXSrnc0uUxGhsq2Vx+3/FuLITqhF9YEgCUF1y1H6nIu9zUUCmSKvlibxpM7HwY9E32zwqFn59/6KtvfdM7ebhAIAYAlW/Q9FkvKBTNC5s8OGPR5GlPKpT+AMDjCSKiejzz/H991f6O51QoVG8t+zE5dZRM5sNkMvkCUVxCysJnljnKjBg19fH5bwQERlosJgAIDIp6YuHbffsNc5R5/j8rho2c7uXtSxAEjycMCY1b8NR7/QaMcEtGr2vU6bS6Jo3BoGOzuf0HTRKLnW8C+QeEc7i8MeMfn/TAQplcwWSyRGJpcuqouQtfdRR7bsmK2PhkHk/A4XD9/MOeWPi2ROrl5xeK7m10P6P457kX/tszaZBQKGGx2AplwKwnXpXLlWq/EMdy8fnCoJAox1+pfAN9FH5CYfOi6rTURVOTRtekMRpuL9BE+pz8Mxr1bvnHZRurr69uaKjWauvIfzWa2oaGmoaGmoaG6vo6Nxb4RrGZw+EKBKK0EQ84/rBn0iAej29PtoaFxwlF0tQB4/r2HyuWyFkslkyuGD5qxqOP/8vxVyh+domfX4DaL7S66pZj9vlO/AOCXnj5i5i4PgKBiMcTkO2EL3BeSd9joPQbKDJtQCqTsVhsq6XF0PlPji8MBoPBYDAYDMZdho+a2q1HP7FYDkBwOFyFMmDYyAdDQiMcZaJjEgYPneztoyaf4vJR+A0ZNiU4JNxRRiL1knspx098JDyiG4fHZzAYUplP6oCxAsHt1XdZTDaPJ4hN6OX4q9DwOBabYzYZ6dWFgo9CxeMJomKSoqIT+XwRk8kSCiXdevTrkzLYLsNisblcflx8C5tDwmLZbO6dT0BGRvfgcLgSqVdUTOsLYqDkKqllamurrFaLQhnQt99QiUQaFBw6bOQDAFBR5ka+js4noAUyGQA0VlcBAM9LmvjQTKHcm7DZmCy2QOGlr64DAGVsDJPFMhv0PWfOkKjULA7X2Kgty7haeKyV9SUAIKB7IgCU3bjqdBxFF7lfIk/SIjHE5vGYbA6DySBsznV2N113okyIYzAYxsbmxSjJBiAPCB760qs2q8Wk12nKS/OOH9VX1d71LK2BUi6S2FFjWWxO8eXzjZVVdz0dQFNtjUSpJmw2TXmZ01fmRl3BkeMAwBHdNVQsTfqyG9cCuiWF9E4N6Z0KAARBVGTfyNnXyhN2FLpQyuWWLo9x9fjGlFELQ2L7ewfG1Ja0flOLyWJz+CKzoanVb8UyFQBU32rxW21dmVju6x/ZO+9yR5au/TCgeeWW5L6Dw8ITjAbdpQstwpnHFwYGR1+5dHzT+s8aGzWvvvWdn39YYq8BB/7cCgBCoWj4qOkEYdu36+ctv67yVfktfGZZcGjsjEf/9X+fvGw/ydxFbyiU/pqG2l3bfjh+dHd4ROzwUQ9XVTXfq+RwuVMefAqAsW3Lt+QaAvOffH3gkMkTpsw9c+oQKaNQqBK6p5pMxvVrlx89vEupUi94+t3omKTU/mNO/XUAXebK5TP/emocALBYrBWfb/PyVqn9Ah2X57ejVAVk3jh/8M9NGk392AmP9UwaFO+wu9qosQ8GBEaajIY9O388c+rPPilDx06YIxCK7zwPCtR+RvFPz8S+kdGJOp1225Zv/zq2p9+AkZMeWCSROt/9cglddfHeWwsAIDgkfOnyjaUlua/9Z2bbPIPSxn7b+O1vG78FgG/XHjWbjM8tGtM2XSg2nzm1L6Xf6KjoJPuR1P7DZXJFRXmh0w12ACgqzNy9fa3JpB828uFuPfr37TfG/jg2ip9R6JHYn8FglBQ7q3Zi7sI35V7KyoriHX98z+XyHp75PI8vdLzb5ElQ6hSxb0GEfOMBAIJDwufMf53N5pQUZzt8+8+NLwwGg8FgMBgMxl0EAmFQcJTFYv7r2I7cnEyhUDRk2EQ//7CIqO6FBbmkDJPJ7JMyHADOnz149fJZAOg/cGRsfJ+k3oOd1vzkcHjeCr/CgptnTx00mozjJ83y8lIFBYdlZV4HgNyc9JCwOP+AMLt8UHCoWCyvr6uqqammV5dbVFeVXr543GKxJHRLDgqJjorucf7s7Tdxc3PSI6N7qv1D7cLBIeFCoaS+vqq+vs7pPA31Nd4+aoKwVVc55+JIfNXBcxe+arVaDAZddVXpxXNHnZ6hdilj0Ovzcq5FxSQldE8ld4wngCjIv+HW27d0JqBZHC4AmPU6gcKr54MzBFJ5TWEek83yCgjhiEVkkpEnlQGAVOUHAGajgbDZBFJ5eOpgBoNZcPS40wmFKh+ZX4BR11h69mIbdNWXFIf0TvWNjtNWlFdev8GXS8MHp3EFIgBgcrlOK0pT6HKCJ5eG9x9CAFF2vTlVrS0t01SUcfh8BpPJ4vL4Yik/UioPCLqw/keXi1y7Wy4ACB+eJlGqm2qrcw8cECh9KE54Y9v2huQSfV1tfW4BuhmOZO3ey+bxfaPiyH8JwqataL1NU+hCLBe6Lo9hMmjzrh+JThrTd8zTe77/V6syXL5o7nuHHY+kn9ry15bbe/GxuXwA0GlaPERpMjYBAF/sduqhsyESS7/4dp+NsDEZTLFExmAwjxz8rbHReaOwE0e3/fDtB+Tn82cOTJy6wJ7NGTpiikAgzs9N37j+KwAoKSlc+/37r7+7NjI60f5zuZd3VEwvgiA2/vwJ2cGlX7uQfu2Co4ox46aLJfKL5w/bV7D9/pv3Y+OT/QPCOVyu2WQCAAaTyWAwTUb9lcunAKCqsvzLlS/NXfD6X8d32s+DImPHarVqtXVe3qo7vyLJy03/cOnT5OfszJe++O5PqbQ5YOMTUgDg/Nn9pM3bt/7E4wnGT277loYUfkbxT2LvQQBw6sSufbs3A8D+fb8TBDHriVdb0UQJXXVBQt6UtlGuk0MNShuzQxAE9aI6KFDbfOHcidqaCqUqIL5bUkb6JQBISR0JADlZV5wkNZra5Uuf1OmaAODi+ZOffLndx0cdGRVH3u1A8TMKci8lAGg1VLdL+/YbFhHVs1Fb/8nyxeTSHJHRif0GjDMZ275SdntAqVO36t0lEZHd7YtUAECjtv73zV/b//0nxxcGg8FgMBgMBuMuDAaDwWBYLKaSkkIA0OmaDu7fOnDQmKzM5jlRfEIiXyDKz71OZoQB4OSJ/QGB4V7eKiaTabO1uILNzLhgT4bm5aT3Th7GYt1OeBbk5zQ1aWQyH6VKXVVZDgCRUd0B4FZJLu260NHrG3fv/MViNgNA6a2ih2c+KZZ4efsoamuqAeBWSVFjY71U6qXyVVdWlANARGQC3OWJ48MHt1dWlmgaakuKC5y+qqwsq6ku4/L4TAaTw+WJRFKRSOqrDtq57UdNQwO6DAAcP7qXw+WHhv2dr7PZyI3c0aEzAU1O2jl8QeJDj/Al0ur87Gu//pb4SItnajh8PgCYDfqbB/dUX88EgPBhaSF9+vkl9LgzAR2ckspgsqpys9qmqzYzp/5WkTwgOHb42NjhY+3HbVaLU/aZWpcjPLk0cfojfIm0IiujKv2m/bilSX/hx7X2f72jw2OGj+FLZKH9B97cuauVE90FlHKJA9WBPfvYrNbMA3vvfI77TkrPuUipUxPQt48yPKoqN6sqN4vD5wclJUcMGGo2GMovOudKKHShlMtdXR7jzM4vQ+MHBUYlqyOS7iajb6yzvztPEIReW9P83d2TWdRrT98vkE/wEQSh0dSePbVv/Y+fOQmYTMaf1qyw/7tl86qS4lz7k3rkfcjioubQy8vN1GhqvLxU9o3g4uJ7cTjc+roqittrgcFRABAYFPna29/YDwpFEhaLHRoamZ2VAQBVleUlxTlBwVHLlm/IvHkh/crJ40f3fLbiJcfzoMigk5vdfJvKarVqGmrVfhJ7ueTeKgC4cqn5gfHr1860OQFN7WcU/8i9VACQebM5io8e2fXoHLefFaWrLugCpY15mOysS337jRk4eAKZgI6I6gEAfx13HiwqyorI7DNJeWmBj4/aPzCMTECj+BkFJpMJrpYFSx0whsFgnPprt31h6NqacgCwWMyIWugFpU7prXebzarV1kskciaTVVKc/dXKl8ocVov7J8cXBoPBYDAYDAbjLjpdU011uY/C74GHFpTdyi8uysnLzTx0YJujjLePGgC8Fb5jJ8ywH+TyBEwmy8vL2/7wMgBYLGbHPVSuXDpTV1vl+ORy6a28qOjEmNjEqsq9AKD2CyEI242M5itzGnUh0lBfTWafSerrqsUSL7ncp/ZvXeWlhZHRPaNiepIJaF91MACRndX6mg3Xr7WeizPo9dt+X2v/NzgkvN+AMSKxLLHXwGN/7xeFIgMA3Xv0CQqOKi7MKirM4vH4cQnJvfoMNRoNNzNQ83V0JqCtJiMAhPcfwmJzKnMzr2/5HQBYXB4AmBtvT6FtFgsAVObcJLPPAJB36IhvTAJPLGGwmIS1OR/HYDEV4ZE2q7Xo9Km26QKAyxs3RIwY7h0UwuJwDVpNxc3r0WkjzXc8sUWtyw6ZfRbKvGqLCzL+2EYhWZuVVyA5GTt8rOCOtZupQSlX7MhxLDanNONKQ0ExAJBLUZN7Z9EOTy6NGJBWV1KYvmULeaQ6Ozvl8fnBvVLcSgqjlIsuXbSja6jMvXIgLmVyn1ELWxUw6jQ/vTP6bj83GXUAIJQqHA9yeSLyh7Ra2gHo9U1Lnp3gmCO7E5PJ4PQwpuP6ADy+EACaGlu4glyW1M8/mNyQTe6lAABdE5W7BAIRAKh8g1S+QS1OZdRrNPX2f7/76o05818Pj+zeJ2VEn5QR02e9cPHcoVX/e8/xJygyiFhbpufIWw5M5u3l47kcHgCU3mreZsFkcr43hg61n1H8w2ZzAKChvvn2idlkasPjwDTWBS2gtDEPc+rE7r79xkTFJAFA337DZHJFdVUpmYx2hNwl1Y7B0AQArL/bD6KfXaLV1gOARCKnkCHXJT98YIv9iEzmA3/vE+h5UOqU3nrPybr8wbtPPTL7uVHjZlnMZsfsM/yz4wuDwWAwGAwGg2kDhw9uHTRkvK86ODyye3hk934DDfm51/86/qddgHydVyZTyGQt0ilms8npaRKLxeT0kLJTRjjzxqWo6J5+AaEAEB4RLRBKaqpLHR/vpVEXIuSiwXZMZiP8/WwQSXbW1cjonn5+IUCuvyGSajW1ZDK6zRQV5onFJ1MHjJXefSXAVmUkEmlSn7TyssL9+25PCQsLsqdMm5/QLaVjEtDGxkYAYLE5FdkZGVtv52f5EqnNarGvsWDW6wHAYmyRZLEYDTyJhMFgOM7DAvsmc/jC2qI8Y10rWScUXQBAWG2OKwirk3owmKzGqgqns1HrInHIPudf3bTJhS8ACLJFujm3dFkuocpHovQFAP/4nv7xPe0/lPr6p/3n5Zzjh0pOn3NLIzXqHt1ZbE5NfvOLCYaaen19ndDLvd3zUOqLLl33gtM7Pg/rNtQ/LEnf5LzaDvz9fPfd0DfWAoAiMDbr7A77QYmXHwBUFLhecLyTY7VaqLPPLiHXkBWJpY4HeXwhQRBlpbffLmnU1gMA9XZnRoMeAHbvWLP5l/9RiBUX5S97a350TLfUAWNCw+NDQmMHDJ7EYrG/+fIdt2RowWK1AIBS5Wff6pfL49Orwg6Kf8iX8WXy5kVCOFwugNPNLQIccqAkzJb/0lgXAGAjCABowztNdlDaGL24tPnyxdM11WUKZUB8QmJKv5EAkJPdyrDNZLZwvkgkBYC6utvr/iP62SXXr519cAb4B4ZTyPAFIovFVHqr2H7k9tZ5rd37JG+0cNhUGyKjyFCAUqdu1btLe2w2AgB+Wfd5Yu+00PD4cRMf2b3jF/u3/+T4wmAwGAwGg8Fg2oCmoWHX9l98FMqIqASVKkChDIiJ681kMo8f3UsKWMwmALh04eilCyfbqauyoryhvkYmV/j5B4ZHJABAcVGLLXBo1IWI01OkPJ4AAJqampczLSst0WrrpDJvpUodHpkAAOXu7Ph3N8jsOXUW606ZqJjubDbnVnFzvq6hoV6jqZPK3MjXMV2LIFOdmw0AhkaNPcOoiI/hCkS6+uaVJeuLiwBA6utnP8LicflSmcWgt1lapP/Vsd0A4NYV5yfC0HU5wRYJwvoNAoDKbOet5Kh1gUP2uaYw9+qmTSgLXyjCowCAwp5WcVkuXWVNTWGusVFrbLr9Z9I3AYDNajU2ak1NzqlABpMR2C/FOybSLTPscAVC+PtRZTtMDqfVpAOFLpT6ckuXhzHptdmX9jKYTKGEasXtVinNvQAA6pAe9iPqiCSRTNXUUNVU166bV12D0lt5ABAYFGU/EhYeLZX6aDV19juNN65ftFjMci9lz8S+dzsPuX5TcEgshS5ftf8bS1cNHjouKzP9p9Urlr4xd93qDwCAfBAVXYYu6usqASCxV/Net72Th9KuhQTFP3W1FQAQ5bBC7rDhkx3vwQKAyWQAAKUq0H4kKjrey9vXXV3ofq6uqiAIm9xLJRRS3YGgAKWN0QuKzdmZlxgMxsAhE8n1N86c3HunjMo3mMW6nXzkcLkBQZFms+naldt3GVH8jEJ+XlZlRbHKN3jYyMl3kzGZDCwWx9vn9uMAMXHdwyK6wd9P8TsLGw0AoFD5UyhFkaEApU7dqnd0e3Zu+54giNHjZztW7j85vjAYDAaDwWAwGHcRicTjJj4SHhFTU1119tSRndvWHz+yHQAct92rra0EAIXS724ncQty0/XY+F6+6mCr1ZJxvcWaFfTqAgAGg9EjMSU07K65OKlcYc9BM5lMHx+11Woub/meZXnZP/rLAAAgAElEQVRpAQAjOranWh0MALk56W3T5UhQcBQAaCh3ALpThi8QgsPG7CRsNsetxRjoTEA3lpQ31dXwxdLIkcMBgO8tixw8DAAqs5rXStYU3TJoG+QBwSGD+gMAi89LmDyFzeXVt/SyPDxErFDq6muqb7T+kiyKLkeU3WL7zJ7DF0sbym85refgUpc9+1ydn3110+a7ZZ+Fvgqxv6/Y31ceERo9bowiPJIgiKoc917yRSnX1U2bT3795cmvbv9d2vwLAGiryk/97+vKa86LfsZNmhg1aHiPSdPkEaFuWUKiq6sFAP9uPcQBavJI6JCBQplc39DK+90UulDK5ZYuuiAf+2IwXAfC2V1fks8yu0vOhb06bY3CPzplwmIAEHv7D5zyHwaDcSv3fBvO1vU4tH+rTqcNDY+fOetZDpcbFBw2Z8EbLBYrJ+uyXaa6ujI/N53JZM164pXuPfoAgLeP4qnF7y55ZaVd5sihbTqdNqF76pOL3xWLJQAQn5D4xtJVyz/dbJeJi+8VGdXz4ZnP9x84EgA4XG5YRAK0XEYARcYRMn/E5rTlQc6rl04AQN/+Yx94eH5kVNxDM58clDalDedBAcU/ly4cIwhiwOCJo8c9zOfz04ZNmDB1vtN5SkvyAKBbz/4Tp8wOC4+e/MCcZ/+9glxbwC1d6H62Wq011eUCgejfL33qHxAE7oPSxugFxeYTR3cAQOqAcXK5sq624tKFVhaAknspl7yyUqlS+6r9l7z8mUTiVVhww/r3u1oofkbk2OGtDAbjoZn/GjfxEfKIWCzp0TPZLlBXW8lgMB574iWlSj10xKQnF39Idp4yL+WdZystzQeApN5DFQoVn89PSR3SNhkKUOrUrXpHt+fY4d3ZmRdlMp/H5jUvqfxPji8MBoPBYDAYDMZd1H6Bar+Q1P6jybQpk8lUqgLAvpYAAABkZV4zGvVBwVGDhozlcLkAoFSpx0185IGHnK+iUci4fsFms4aFJ/D4wuqqWwa93vFbenUBQNrwiX1Shg8dMS0wKLRVAZFIOnLMNKFQJBKJR415kC8QVVWWOj2YnHXzCgARHZMoFEkbG+sdX0hF1+Xto1AqfZVK38Cg0IFDxgQGRxJAFBVkuSWjqa8FgKjoHkrV7Xxd7+SBEqmcfFsdEZpfuiw4fTxu9MSgpBS/hB5MFofJYuk1dUUnW8yriy6ejRo8IrzfkOBefZlsNpPFNhv0eUcPO8oE9k4GYFRk3minrrChQ/ziurN5PBaHCwCNNZXp27Y6ncelrvDBQ4QyLwCQqgP6P/Os/bjVbL68eYOxXgMAoUMGhvUd5PTDqrys2sxccBOUcqEj9PIBAAaDKVX71ecWOH7lFRkaM2Isk8ViMFkAIFH6kqVrrKq8uvn2PLb0wqXAxD5CuXefR+ZYTAYmi81icwjCVnThjFu6UMrllq52whVIpj63mssXs9hcAPDxi5j99h4giKLMU0c3LWv1JxaT4eb5nUlpj4GrtxWcIGzWK8d+SR37TFLa7ITUB1hsLovN0Wlrzuz6ipay3O8YDIYDe3+ZOHXB6PGPjRjzCJnV0jTUbPi5xWaG69Z8tOTVr5SqgCWvfq3XN/F4fCaTVVvTvJxOfV3tnh1rpzz4VGr/scl9R5lMBnKlVMdtAI8c2jls1PTgkJiFz7z/xIK3mCw2i8UiCOLs6T/dkunWvff8p5eS78jw+SIAeOHlL6xWC0EQF88d+v6b9xHLfuDPrQPTJoeGxU+aunDS1NZXGKcLFP+kX7uQeeNCbHyfmbNfnDFrSas3Mw8d2DZhynyZXDFt+uJp0xcDgFZbp9c3CRwWSKGrLuycOLp98rRFUTFJ7338q16nNZtNh/Zv2r71J8SyI7YxenFpc/q1C1WVJeSzrjnZd12NJ75b6sefbQMABoNhMhl3/vGD/SsUPyOyc9vPEVE9knqnPfzI85MfWGixWPgCIZPJWvbmY3m5mQBw+MBv0bG9EnunJfZOI39y/uyB8IjugYERci/v+roWN+cO7Pt1+MgZCqX/8pVbbVYrl8dvaJiXeeOauzIUoNSpW/Xulj0bf/7slbe+75MyPDziF9I//+T4wmAwGAwGg8Fg3CU352ZC91KF0n/YyIesFjODyWQyWQBEbk7zFbjRYLhy8VifviOiYhIjorpbLGYulw8AFeWFbdCo1Whqa8oVygAAKCxwflSUXl3w95Y5DAZTofQrKS5oVSYwKGr6rEgAYADDYjFfunDMSaCiokzTUCuV+QBAxd3X36DQ1Tt5YM8k51xlcVFWYUGuWzIZ1y/FJvSRSr0nTJljNhpYf+fr0q+5ka9jSWUBiKIiX6UqKlZXV1OZcddcbVNltb6xXqxQcgRCwmatLy3O2LnN3NTixoL2VqmFMAnlXhyB0GazaipKb+zd2VRWaRfgiIXRw0bYLJbrf2wlrHfd4AhFl7pHd5lfoNViNjTU37p28eaOnRZ9ixdvUXRxJSLv4FAGk8nicNhcnv2PyWZV5WSaNI0AwJEIJb5+TBaLwWACECZdY0Xm9Zs7d4Lb2wshlcsRm9Xq16OnpqK0OrO1p605TK/AYIO2IfvgfpupxX5o0gA/dXx3Do/P4nAAgMFgkuWyWS2ll/5ejYQgKrNuciRCrkDI5vEBiKba6pzjhysutzYtv7supHK5petvQvr1t5nNJefde5qYL/FOHPoYXyhjc3gAwGAwODwhhyfUNdblXNxDyiiC4nz8Ii8eXKPX3N4zqjTnQmj3NAYwLvy5ymy8vdpJZK/RBl3DzdN/UKirKLhqNOm8VCF8oZSwWStLbhzetLShIp/iJ/cF/QaObtTWHzlEVfa+/UY0NjYcPUS1aeeNjEuEzaRUBfL5QrPJWJCfsfrbd+3LIpM0NNRduXBEqfIXi2UCgUivb7qRfuabL19vamxeIynr5tW62lKFwk8gFHO5/Ib6qjMn93712WuONwxOndij8vWXSL14fIHZbKosL9q1ffX2rT866nIpExQS0X/gBB5PwOFwySwSi8XmcLgcDq+mpvzMyf0AoFT5xsT2PnFse1Fh89pS3bonC0XSHVvX2I9cvXwiMChCJvNmMBh1tRU3r5/zCwhrqK8+cpDKq23zM4p/zp89GBgUJvNSMJmsmuqyIwd/i45J0um0f+65vfA9QRBFBdcDAiN4fCGDwSgqzPzhf+/4+QWzOdydf/zoli6UuiC5eeOSzWr0Uaj5fBGPL7BZrTczzudktf7+UaugtDGS1AGjmhrrj1B6EgUUm0NCI4NDYwFg2+/f3iopcPwqMCi8T98RRQU3c7KuyL2VLBa7uqp0y6YvzpxqcbMWxc+InDm5n8Nhyr1UIrGUw+E2NWmyMy/t272RPFVJcT6Xy/bzC2WzuQ311UcP/rb6u+U+CmVIWNz5M/vrWy42ZTQaa6pLgkOiRCKZzWrJybq884+fnExCkbFYzYPTJhfmZ5w9fQjuAKVO0eudwh6rxdR/4LgbGeeuXLq9HlxdXY2PQukfGPHXsZ11dTXoddFV4wuDwWAwGAwGg3GXnOx0sVgsEIjYHK7Vammor7l04Wj61QuOMpWVZZqGKrFEzuXx2WxOU5MmJ+vKkYM7HGXCwmMMBl1WpuuncEwmfWBQREN9zYlje+6cMdGri8Vm+qqDm5oaTv+132xukR+Tyb3CI7tVV5WWlxeIRFImk6nV1J09/WdRYSvzFLm3t0LpDwAXzh3W3GV5AApdQqFQofBjsVgMJhMIQq9vzM+9fvTQTndlCIIoyLspFAn5fCGXxycIoqG++sK5w5k3UR8hAgBGQHCyaykAAFB1j08YO7kqPzv919/QFbSB8OFDQ3qnVmRlZPzR3hRAp9KFuRcMWfIfU1PjqW/atQcXBtNJiI3v+cqbqwryMt55fU5H23KbNb+cqakuffG5qR1tSBdk6IhJj897o76u6vmnxzt9ldp/+JOLP7yZcX75sqc7xDaMZ8DxhcFgMBgMBoPB/KMIDgkfMXp6aUnu3t2u106MjIobPHRKU5Nm0/r7/h16t5fg8AkOG/DsYvJzU13t5fXr6TYJCJvNYjQUnaN/4YWO1YWhBe+YyJihIxnkxlwMBpNF8zIyGIzHCA2LDIuIP3xgu/2IUukHAAaD826imC5JfEIKANzIONfRhmAwGAwGg8FgMBgMptMREBgOf+9Dfr/jRvKusarSYjayOTyuUEwesZhM98Km/MNH8w8fvRdn7lhdGFrgCAQ8sZTBbN42UFdf14H2YDBtZtr0Z7r16B8e0W3t9x9ZrVaFQjV6/GwAyM+93tGmYe45w0ZOTuydZjTqd/2xxrU0BoPBYDAYDAaDwWD+SURFx4eGx1ssJvtigPc1biSgdeXVx1d+eu9MwWBQqLh8jXpVaAzmfuHiuUOx8cmD0qb0SRmh02klUi8ul19RXvT7b993tGmYe8isOf9O7DXE20fNYDD27dpQUtLGfS0wGAwGg8FgMBgMBtP1SO47JDQsViyRMxiMyxdPajWajraIBvDyBRgMBtMxHD64o6GhZvykuQGBEV7evgZ9482Mc2u//9B8b14uaRs11WXVVaUdbUWXwlcd7OWtqqkuO3b49x1/rGtV5lZJnl7fWF5W4FHLMB4HxxcGg8FgMBgMBvOPor6+xmQy1NdXU8jI5D4isUyrrb95/Vz6tYses+2e4sYmhBgMBoPBYDAYDAaDwWAwGAwGg8Ggw3QtgsFgMBgMBoPBYDAYDAaDwWAwGIz74AQ0BoPBYDAYDAaDwWAwGAwGg8Fg7gk4AY3BYDAYDAaDwWAwGAwGg8FgMJh7Ak5AYzAYDAaDwWAwGAwGg8FgMBgM5p7A7mgDMF2Q4JDwV9/+/vRfe3784b/3TsuiZ99J7DVEIBABgM1mXbf6g8MHd9w7dfcX0x5eMGb8Y0VFWcvenNeBZix5ZWX3ngMAwGQynji67afVKzrQmH8UnonBLkkniZ1OC/ZPq6T2Hz530Tu7t6/+Y8saWk7YxfxMu386CV21XCh01fG9q5ara9C2axsP1+mKz7eWlxWu+PD5e6qlM+PJ8auLjZW4/wFa67RL5go6YZvvkn7uYnSqvgU/AY2hn+CQKIFArFYH3zsVj819sd+AcXy+sFFb39BQU19X3dBQc+/U3Xf0SBrI4fIiIrt7+yg60AydTtvUpDEadFwuL234tPCImA405h+FB2Kwq9JJYqfTgv3TKkEhUVwuLyAwgq4TdjE/0+6fTkJXLRcKXXV876rl6hq07drGw3Xq7aP2VYfcUxWdHE+OX11srMT9D9BXp101V9DZ2nxX9XMXo1P1LW4/AT1xyuyJUxc01Nd8tGxRdXXlvbAJHT6f/9/Pt+VmXflsxUv3TktQcNhr76wm7+oAgNVqraos3rV9zfEje+4UdumfBx6eP2DQRKnMx2azVpQVbt+66vzZ4/fIcs/4p0PomTQIAHZt++G3Td91oBlutQ2X0FhfVy+d8PcPLyrKqq2pbuep2sP/Pn8LAFgs1rKP1vsHhIeExeTlZrbnhJ2q/6GLzhannuyjPAminztJ7KCgVKmXLd/A/7v/sXP92un/fvCc45Gu17eg49H4YjDoOlPX9DNN/ulsfSaN9X4fQfv43knoquX6J+P5Or0fu4T79Drhvhsrqbl/+59O2H46Sa4Anft0nnLf+ZkuOt21KCWdqm9xOwE9cuyjXC5fqQqYNv2pb796917YhI6Xl49E4qVQBdxTLQGB4QKByGazNjVqAIDL46v9Qh+f9zphs504ts9JmNo/j8x+btS4WQCg1zexWKzg0Ni5i96prXn6HrUAz/inQxCJpHpdY4f3dG61DZfQWF9bNq/asnlV+89DC1artaa6zD8gnNHuS/JO1f/QRaeKUw/3UZ4E0c+dKnao0euaNJpas9nE5nAEAjFB2Bq1DQQQWk2tk2RX7VtQ6FTxhQ72cyfRhaGGxvG9U9FVy/VPBtcpNffpdcJ9N1aicD+21U7YfjpJrgCd+3Sect/5mS7ux2vRTtK3uLcER/+BI6VSb/JzRFSPe2CPe4glMgBgszge0JWTdXnxotGLF41eNGfIwX0b2GzuqLGPOslQ+0epUg8ZPs1qtf6++aun5g59btGojPTTQqFk/KQ598hmT/rHk4jFEiaLTRA2Pp/f0bYAoLUNFLpqfdFFZ+t/6KLz1Lvn+yhP0nn8TBeNjdqXnp+2eNHoz/77LwAoKc5dvGj0c4vGfPPlO06SXa/s6PyTy+5JPOlnXKcYDAZDL7hfxbSHztZ+OluuAIXO5kMU7kc/08X9WF+dBPeegO6dPAwAzp7eFx3TS6kKDI9wfnjbV+3/xILXwyK6MZms6qpbe3f+9OCM566nn/7mi7cdxQYNGTNmwmMq32CCsFVWFO/evvbkif32b8dOmPHAQ89sXP+JVOqTNnyaROql1zVlpJ/+5su3rVYrKfPhJ5v8/MPIz2r/0LUbzpKfLRbz2lVLnR4+nTT1sfGT52k1df/94OmK8lK3imzHZiPsn9etXTkwbYrMS+mWf0aNncHjCa5ePr59648AYDAYvvv6nQ9W/Cq/4zwuYbFYjz7+fFLvNInU22w2VlYU79q+5uypI3YBdP8Eh4Q/+viLwaFxTCbzZsb5qJjEvJxr9q0zUOoCAAIDQx6b92poWDwBRF7OtRvXz7bBZhTmP/n6gMGTyJs2XC7vmzXHyOMEQRzY98v6H//PLjll2hMDh0yWeyktFvOt4pzNG/4v88Y1x1MtXb7OZrV+/P4z8558Iy4hhcvl19aU79q2+ujhXW6ZBGhtg7rNI9aXS5tT+w+ft+gdDpcHABaL6dSJXT98+2GrNlPbs/jfH8Z1S/nv+0/l52XZD85+4oWBQyZ/tfLFq1fO2Q9Stx96oY6v8ZMenTh1wdpVy06fPGg/uOSVlf4B4UsWT7YfcdlHPT7vP0m90y5dODJg8CStpvbAvg0TJs9nczh/HdvhtGC/J/sxal2AFoMuQe+j2l92xHIhxil1O0TxM3rsUPct6GWnEavFAgAEYbvzK7fGSgoQ/UNLP0/S/r6FxvgCAKlMNnvOf6LjeovFcrPJkJ93fd2aj0pvFTuJjRk/fdTYR+VeSqNBn511+Zsv3tTpmsivUNoGop+lMtm8RW9FxSTyeIK62oq9O3+aMHV+SVG23T+I/SFi2alxq41R+Iek/bGMDl39WPvLha4LpR26rFMa4xQFl7GM0s/TMr/wZLkQ69RludBj2aWfPeZD9Osol9fqiNc2tNQ7jXHBZLJHjX1w5JhHvX18mxobzp7+8+e1K+3fLnjqzV7Jw1Z+tDgrM91+cO7CV1P6jf5o2ULHC2+XIF4jdb05CIouumIQEVrqAh3PzONoz8lQQFedejJXgN7Xdb15CqKfEeOC2mZ0P6NAHTvjJj4y9aGnD+3ftGHdF+QRDpf77gc/CYWS558eTx6hcX7h0j/o9eWxnEz7x0r3EtDhkd2sVuv2Ld+Pnzyn38Dxg9ImOiWgn1uyIiAwkvzs5x/2xMK3AcDPL9RRZsSoqTNmL2GzuXp9I4vFDgyKemLh21ar9cypQ6SAf0A4h8sbM/5xhdKfbNYisTQ5dZTJZFj1v/dImaYmja5Jw2KxeXwhAOh0WiAIALBYzEaj3snsvv3H8HgCnlIwOG3irxu/davIrSKVyVgsNjnnR/dPcEgMAFw6f8R+pL6u9rUXH2pqanTXgOf/s4LcyNJsNvF4wpDQuAVPvcdivnPqrwOkAKJ//AOCXnj5C3t2iVzEx3FFUZS6YLFYi5d84vv3liBxCSlxCSltsBmF+vrqhoZqFostkXgBgEZTSxAEABCErb6ueSGkB2csGj9pLoPBsFjMPJ4gIqrHM8//9/235zreflAo/Fgs9qtvfRMUEkMQhNVqUfkGTZ/1wvVr59qzuHCrbcNlm0esL5c263WNOp2WYzYyWWw+X9h/0KRN679sbNQ6GenSHjaHIxRKho2c5ji29eozlMvl6/XNk2qX7YdeqOPLPyCczxcGhUQ5TtJUvoE+Cj+hUGTPBbjso9TqYLmXcuCQyUwm00fhN236szarjcvjDx46dfvW1fV1txc38GQ/5lIXYgy6BLGPoqXsKOcBtDh12Q5R/IwYOy77FsSyewy3xkoKEP1DSz8PNPUtdMUXACgUqlfe+k6h9AcAgiD4AlFcQsrCZ5a989ocR40Rkd17Jw9jMlkAIBCKeyQOXPTsuys/fvHvQrluG4h+fu6F/0ZGJ962TRkw64lXAcBiNjk4EKk/RCk7XX526R+gKZYRoasfo6VciLpQ2iFKndIVpyigxDJKP0/L/MKT5UKsU5flQoxlFD97zIeI11Eux1PEaxu66p3GuPD28X3ksdurgkplPiNGzyQIYv2Pn5FHOByuQCBKG/GAYwK6Z9IgHo/f6o1kClBip0vOQVB00RWDnqwLRDw2j6M9J0MBXXXqyVwBYl/XJecpiH5GiQuXNiP6GQWXsaPV1rHZnKEjHr5y6URG+iUAmLfodf+A8KrKEvtJaJ1fuPAPYn15MifT/rHSjQR0j57JXt6+hQU3S0oKjxz6PXXAuKiYJEeBUWMfDAiMNBkNe3b+eObUn31Sho6dMEcgFDvKcLjcKQ8+BcDYtuXbrb/9AADzn3x94JDJE6bMdep8laqAzBvnD/65SaOpHzvhsZ5Jg+K7p9q/fe+tBQAQHBK+dPnG0pLc1/4zk8LyivLigMBIm81akH8DvbxOkHecSKVz5r/OZnNKirMdBVz6RyyWAcCVy6ccD7oVMyQKhSqhe6rJZFy/dvnRw7uUKvWCp9+NjklK7T/GXvGI/pm78E25l7KyonjHH99zubyHZz7P4wsN+iYnMeq6mDztCV91sMlkPHl8R3FRVny3vr36DGUwWqzugmIzCr9t/Pa3jd8CwLdrj5pNxucWjblTRigUDR81nSBs+3b9vOXXVb4qv4XPLAsOjZ3x6L/+75OXHSV5fGFgcPSVS8c3rf+ssVHz6lvf+fmHJfYacODPregmgau2gdLm0dsztc1XLp/511PjAIDFYq34fJuXt0rtF5iT3aLZo9hz5uS+nkmDY+L62H+VkjrEy9u3vKwgOyvDfhCx/dCCy/hCAaWPItm3e11xYfZTzy3Xaur//cyE9z5eHxgUFRmVQG7H58l+DEUXSgyigNJH0VV29PO4jFOX7RDFzyixg963UJfdk7g1VlKA4h+6+nmgqW+hK74AYO6iNxRKf01D7a5tPxw/ujs8Inb4qIerqm45nc3bR52fd33frp8ryotHjp3Rf+CE2PhkJxnqtoHi556JfSOjE3U67bYt3/51bE+/ASMnPbBIIvVyyznoZXcJehtz6R9aYhkFuvoxusqFqMtlO0QpF41xigJi2an7eXrnF54sF3Wdol+TtN8ez/uQ+joKZTxFubahy2ba40KjqT12eKtWU9cnZURUTOLAwZM2b/jabDIBwJlT+1L6jY6Kbr6ITe0/XCZXVJQXFuTnuKuIOna66hwERReJx2LQpX/oaquen8fRmJOhgK469XyugLqvgy46T0HxM4oP0W126WeXoMTO8SN7eicPS+w1ZNacl197cUbffsOS+460WMy/bmh+dp7G+YVL/5C4vj70VE6GlrHSjQR06sCxAHDtyl8AkHnjWnlZgZ9/mFKlrqosJwXiE1IA4PzZ/aSXt2/9iccTjJ88z/EkY8ZNF0vkF88fJmUA4Ptv3o+NT/YPCOdwueTATJKXm/7h0qfJz9mZL33x3Z9SqY+TSWaTEQBsrm4Xf/7JyxMmz66sKDp35hh6eZ2IiOxuf8AeABq19b9v/tpRwKV/OFweQRDkjqUPPDx/6PCHGEwmANwqzvlw6VPoljCYTAaDaTLqyTxRVWX5lytfmrvg9b+O73SSpPZP337DIqJ6NmrrP1m+mLzFFBmd2G/AOJPR4CRJXRdx8ckAcPDPjZvWfwUAB//8498vfULeeGmDzYgQBEHeZ7uToSOmCATi/Nz0jeu/AoCSksK137//+rtr7c+LOXLi6LYfvv2A/Hz+zIGJUxfYs8noULcN9DaP2J5RbLZarVptnZe36s6fo9hz8sT+hx95XqkKjE9IzLh+GQBS+48FgBvXm1/aQm8/tOAyvlBA6aNIyksLykoLAECrrQOARm0DALDZXPJbT/ZjKLpQYhAFlD6KrrKjnwco2zx6O0SML4rYQe9bUOrdkyCWHQUK/9DVz9Pbt7Q/vuRe3lExvQiC2PjzJ+Src+nXLqRfu3Dn2SrKC999/Qny83dfLY2N6+Pto3ZaLAixbVD4ObH3IAA4dWLXvt2bAWD/vt8JgiAfgnYLt2LQJShtjNo/tMcyBfT24XSVi1oXSjtEKRft12MUuBXLFP08vfMLT5aLuk7Rr0nab4/nfUh9HYUynqJc29BlM71xYbNZ165aevH8SQDYt3vz8k9/VfuFDBw85vCB7QBw4dyJ2poKpSogvlsS+YRdSupIAMjJutIGXUAZO111DoKii8QzMWiHlrqgxvPzOBpzMii0s07teCxXQN3Xdfl5CoWf7VD4EN1maj+jgBiD3331zrKPN/gHhD/z/PvhEd1YLPaxw7+fPX3U6Wztn1+g+IeEur48mZOhZax0IwEdGdXTbDLu3fkL+e+N62f9/MPShk/5dcM35BG5twoArlz6y/6T69fOOHXigcFRABAYFPna29/YDwpFEhaLHRoa6XhTLjf7qv2z1WrVNNSq/SR8Pt9gaIsrd25b14ZfOWKzWbXaeolEzmSySoqzv1r5UllZi6efXPrHEbFYLpbIyQfpKXrYVqmqLC8pzgkKjlq2fEPmzQvpV04eP7rnsxUvuVui1AFjGAzGqb92219wqK0pBwCLxewkSV0XMrmSIIiD+361y1y9fNzpApEum1HwDwgDgOKi5kWv8nIzNZoaLy+VU/sxmYw/rWleOWjL5lUlxblteMqDum2gt3kU2m8zoj2ZN8737T92UNpk8sIlMrqnzWY9tP83+0/Q23WG+ycAACAASURBVA81Kf3SZs5+8c4l/MtK8z9490n7v27F191A6aNQ8GQ/hqILJQbd5W59FF1lRz8PdZunqx2igN630Dt+3S94fmxqPyjtMC6+F4fDra+rcrlQY2H+Tcd/G+qrvX3UUlmLZ5Pb3zbkXioAyLx50X7k6JFdj855+e6/aB16xyYUqP3T2eqdBKW+6CoXtS6UdohSLk9ej6GXnbqfp3d+4clyUdcpXdckKPZ0Nh+ijKco1zZ02UxvXDTUV5PZZ5L83HS1XwhZZJLsrEt9+40ZOHgCmYAmN9b+67jbm9CAq9jpqnMQdDwTgySeqQvPz+M62zVt++3xZK4Az1OofYhuc/tBjB2drmnTz58sfOaD5L4jAaCkOGf1d8vvkS5Aa2PU9eXJNkbLWImagA6PiFGqAhkMxgcrNpP3OLhcPgDExicD3HYrl8MDgNJb+fZfmUxGp/MIBCIAUPkGqXyDHI8bjXqNpt7xiLWly8hVscgl9jqEnKzLH7z71COznxs1bpbFbHbKPqP4x2jUMxiMsPDo/Lysn1av+Gn1Cj6f/9n/9rbBmO++emPO/NfDI7v3SRnRJ2XE9FkvXDx3yN0FRsn1rQ4f2GI/IpP5AIDN5rwCPXVdcDhci9nkuG6y0dDKkk+02IwCuRxPU6PG8aDRoAMAP/9gx90YTCaD0w2otr2nSd020Ns8Cu23GdGeo4f/SOk3Jjq2FwAMShsrlfkUF2YWFzVHN3r7oUYkkkql3iyWc1+k0zUva4USXyig9FEoeLIfQ9GFGIMuQemj6Co7+nmo2zxd7RAF9L6ls41fHsPDY1P7QWmHci8FAOiaNHf+3AlrSwttBAEA5F2cZpl2tw02mwMADfU19iNmk8nl4yd3Qu/YhAK1fzpbvd+2GaG+6CoXtS6UdohYLo9dj6GXnbqfp3d+0X7oqlO6rklQ7OlsPkQZT1GubWi0mca4sFpb7ARDPq9HXriSnDqxu2+/MeRScn37DZPJFdVVpWQy2l2oY6erzkHQ8UwM/v1bT9SF5+dxne2atv32eDJXgOcp1D5Et7n9oMfg2dNHx0/OCQmNA4DTf+2+p7pQ2hh1fXmyjQEdYyVqAnpQ2u1tLqWyFs/nBwVF2bPvFqsFAJQqP3vvxuXxnc5DXjrs3rFm8y//Q7fybpBX+Xemru4FNhsBAL+s+zyxd1poePy4iY/s3vGL/VsU/9TXVQUFR0dGd7fHksFgsFmddzJEobgof9lb86NjuqUOGBMaHh8SGjtg8CQWi/3Nl++0sJnSP3yByGIxOW6ervYLAQBoOVt2icViYnO4YrHEvhA++dp+22xuP+R6NyKx1PEgjy8kCKKstIheXSTUbQO9zXumPSPak5F+qbKi2FcdnNgrlbwBmH7ttKOAW+2H7Cg5rb0mc/jAdvKdRApQ4guAAABWy7HTaShF6aNQ8GQ/hqILMQZdgtJH0VV2us6D3g7bH1+e71vowmNjJS39PF1jE0n746tRWw/3cntVdyFf+pPJmztDDpcL4OQc1/1hZ7se82Qs01t2auhqzyjtELFc9F6PUYzvtF1nenx+AZ2mXCixjGJPh/iQApTxFOXaxi2bKeoUaI6LFi1BJJJBy8zm5Yuna6rLFMqA+ITElH4jASAnu43rb1DTVecgdEHXvAAFutpqh8zjXOLJnEz78eT1PJ6nUONJm9FjMKVfmn1v0tQB43Zu+/lOmfbPL+iCrpwMIu0fK1GTFNExSQDwxacvzJmZYv+7duUvLo8/fNQ0Uqa+rhIAEnsNtv+qd/JQp/PcKskFgOCQWES91FRXVRCETe6lEgqprshZLNbUB+f1HziSFqU7t31PEMTo8bMdlaL4J+vmRQDonTy8nQb4qv3fWLpq8NBxWZnpP61esfSNuetWfwAAd+7JRu0fk8nAYnG8fRTkvzFx3cMiusHfd4PR0TTUMhiMAYPH2o+EhDrXL7rN7af0Vh4ABAZF2Y+EhUdLpT5aTd29fleo1baB3uYR23M7QbcnI/0MAAwZOjUisofZbNq/d5Pjt261H3IRIoXKv202o8SXyWQAAKUq0P6rqOh4L29fx/Og9FEoeLIfQ9GFEoMooPRRdJWdrvOgt8P2xxftfQu9YxMFnulb6Orn6RqbSNofXzeuX7RYzHIvZc/Evm0wgHbqaisAIMphdbxhwyczW+ZlUPrDDrkeo8CTsUxv2amhqz2jtEOUctF+PUYxvtNVds/PL6DTlAslllHs6RAfUoAynqJc27hlM0Wd0hsXUqm3VCaz/+sfGA4AlRXFjjLZmZcYDMbAIRPJ9TfOnGzLG7Eu6apzELqga16AAl1ttUPmcS7xTPuhC0/mCu7reYoH8KTNiLEjFIpmPPoCm83Zv3d9eWlBYFDk3IWv3CnW/vkFXXgyJ0PLWImUgPZV+6v9w+rrqi6cO+F4nNwQrNvfmzBevXQCAPr2H/vAw/Mjo+IemvnkoLQpTqc6cmibTqdN6J765OJ3xWIJAMQnJL6xdNXyTzejG23HarXWVJcLBKJ/v/Spf0DQ3cSeff6DydMWzX9qaXLfwXeTQefY4d3ZmRdlMp/H5t1e7gTRP7t3/KJpqImOTXpoxiLyyIMzFgmEErPZvZd94uJ7RUb1fHjm82TagsPlhkUkQKuvzFD6p662ksFgPPbES0qVeuiISU8u/pC8hyPzUrplT0HedQAYPW5WfEIiAAxKGztg0MQ229x+Du3fqtNpQ8PjZ856lsPlBgWHzVnwBovFysm6TLsuJ+5sG+BOm0dsz4iQ+Qg2x/kGF7o9B/dttlqtSX2GisTSooKb5N50dtxqP6Wl+QCQ1HuoQqHi8/kpqUPQC4IYX6UleQDQrWf/iVNmh4VHT35gzrP/XkG+q24HpY9CwZP9GIoulBhEAaWPoqvsdJ0HvR2ix9fdYof2voWusYnFZgMAg3HXAd0zfQtd/TxdYxNJ++OruroyPzedyWTNeuKV7j36AIC3j+Kpxe8ueWVlG+xB525+vnThGEEQAwZPHD3uYT6fnzZswoSp851kUPrDDrkeo+BexPLdoLfs1NDVnlHaIUq5aL8eoxjf6Sq75+cXnadcKLGMYk+H+JAClPEU5drGLZsp6pTeuODy+P968VOFQgUAD818MiQ0zmwynjrxp6PMiaM7ACB1wDi5XFlXW3Hpwqk2KHJJV52DoOhCga55AQp0tdUOmce5xDPthy48mSu4r+cpHsCTNiPGzqJnl3r7qAsLbqz/8f82rPvEYjH1HzTxzpla++cXdOGxnAzQNFYiPeSfNmwKi8VyXB2c5NjhnQ/OeC44LI7898CfWwemTQ4Ni580deGkqQtbPVV9Xe2eHWunPPhUav+xyX1HmUwGcoUUx6W13eLE0e2Tpy2Kikl67+Nf9Tqt2Ww6tH/T9q0/Ocqo1EEAwGSyQsPizp051jZFjmz8+bNX3vq+T8rw8Ihf8nIzEf1jtVp3bVs9fdYL4yfPGzF6JoPB5PL4Fov56KHf3dJ+5NDOYaOmB4fELHzm/ScWvMVksVksFkEQZ0//eacwhX8OH/gtOrZXYu+0xN5ppPD5swfCI7oHBkbIvbzr62oR7dmx7ce+/cd6+6hfeuM7o0FHLuXTHpvbicFgOLD3l4lTF4we/9iIMY+QEahpqNnw82e067oTp7YBbrZ5lPZMQbfuvec/vZTHEwAAny8CgBde/sJqtRAEcfHcoe+/ed8te0pKCkuKs8nnTa5cOu70rVvt58C+X4ePnKFQ+i9fudVmtXJ5/IaGeZk3rqEUCjG+Dh3YNmHKfJlcMW364mnTFwOAVlun1zcJHF5YRumjUPBkP4aiCyUGUUDpo+gqO13ncasdUvgZJXZo71vaOTaJxZI3l63mC8QcDhcAAoMi/u+bPUAQ16+d+u7rZU7CHuhb6Orn6Rqb7LQzvgBg3ZqPlrz6lVIVsOTVr/X6Jh6Pz2Syamsq3LXEJSh+Tr92IfPGhdj4PjNnvzhj1hJGa2/YofSHHXI9RgFdsYwC7WWngMb27LIdopSL9usxivGdrrJ3yPyik5QLJZZR7OkQH1KAMp6iXNu4ZTNFndIeFxGR3T/6bJvNZiUH6IuXj9sXEiFJv3ahqrKEfLY95x54mKSrzkFQdKFA17wABbraaofM41DwQPuhC0/mCu7reYoH8KTNKLEzZOj4HokDjUb9z2s+AoArl8+c/mvPwCGTH37k+Ttnau2fX9CCx3IyQNNYifQEtEZTazTqz57a53TcYDDczDhvNhlZrNuLkX3+yYvXr502GnRWq6WmuuziuVaWad/xx7q13y8rKrhpNhu5XH5dbcWRg7998O4iu0BxUZbJaCAfXLdTXlrQ0FCj0zU5ne2PLat/3/xlRXmh1WoRiiQcDtdsNjnJnDqxx2jUV1fdOnJoG0p5nSjMv6lr0pAvCJDk5WaePL7DYrm9NCq6f/bt+XXDuhWVFcVMFhsYjKLCzO//99afe9ze1feDdxacPbVP01DDZLHMZmNpSe7Gnz/5dUMru7FR+Of0yYO7d6zRauqsVmtdbeXenT9+ufK182f3EwByuTcpg1IX9XW1P3z7dnFRltlsAgYjN/vqlyuX6HTasrLCttmMQnVVaU116d2+/f3XH7b++nV1VSlBEEaDLif7yhefLqmqLHc6Q1VlSdu023HZNkhctnk71O3Zpc08vkAkkgkEYoFATGYluFy+QCAWCiUicfPLgOj2HNj7i8GgKy3J3bNrg9NXKO3Hjqah4afVH1SUFzIYTIKw3cw4l4O83TNifFmt1m+/fK0gL0Ovb7JYzAX5GV98sqQgN72urtKx33DZR1VUFOt1jYUFWdVVFZqG2vKyAgAoK83T6xoL82+i+5DGfsylLsQYRAGlj6Kr7Cjt0GWbd6sdUvgZMXZc9i1u1Xs7xyaBUCSTKWQyH6FQAgAMBlMm85HJFTJ5K3e8UcZKChD9Q0s/71adotDO+AKAosK85e/Ov3LpWKO2nsfj6/VNVy4e++i9ZpmCvAyjUV/ast7LbuXp9Y2FBdnkvyhtA9HPn/33hcsXjuj1jTabtary1q7tq52KjNgfoo8FKFD4GcU/dMUyIrT0Y3SVC7HfcNkOUcoFdF+PUYzviHWKcj1Gy/zCk+VCrFOX5UKJZUQ/e8yHiNdRLsdTxGsbdJupr0XpiovamvLr1079vvmr+rpKJpOla9JcOHvwf5+/eadk5o0L5IdzZ/a7q4UEJXa65BwERRddMYgIvXVB3VY9No+jPSdDAY11SuKBXAFKX3dfz1NQoPYzSly4tBlxTEHBZexIpN4Wi/nQ/s3Zf0fcmlXLb5XkcFpbzqL98wuX/kGpL4/lZEjaP1YyAoKT3VLpFrHxPV95c1VBXsY7r8+5d1owGAymbeA+CoPBdAHW/HKmprr0xeemdrQhGIwn6KpjtyfL1VV9eB8xdMSkx+e9UV9X9fzT4zvaFsxtcFxgMBjMPQV1E0IUQsMih46Y5HhEqfQDAIOhLbdTMBgMhl5wH4XBYDAYzP1FVx27PVmururD+5r4hBQAuJFxrqMN+eeC4wKDwWA8DNIa0IhMm/5Mtx79wyO6rf3+I6vVqlCoRo+fDQD5uddp1ILBYDBtA/dRGAwGg8HcX3TVsduT5eqqPrx/GTZycmLvNKNRv+uPNR1tyz8XHBcYDAbjYehMQF88dyg2PnlQ2pQ+KSN0Oq1E6sXl8ivKi37/7XsatWAwGEzbwH0UBoPBYDD3F1117PZkubqqD+9HZs35d2KvId4+agaDsW/XhpISt7frwNAFjgsMBoPxMCypLICucxXkZ90qvqlQ+ktl3mKJ3GTU38g4+9VnrzS13PYXg8FgOgTcR2EwmC7JoCETqqtKTxzb3dGGYDD001XHbk+Wq6v68H5k5JjpwSExtTXle3f9+PuvONHZkeC4wGAwGA9zbzchxGAwGAwGg8FgMBgMBoPBYDAYzD8WOjchxGAwGAwGg8FgMBgMBoPBYDAYDMYOTkBjMBgMBoPBYDAYDAaDwWAwGAzmnoAT0BgMBoPBYDAYDAaDwWAwGAwGg7kn4AQ0BoPBYDAYDAaDwWAwGAwGg8Fg7gk4AY3BYDAYDAaDwWAwGAwGg8FgMJh7Ak5AYzAYDAaDwWAwGAwGg8FgMBgM5p6AE9AYDAaDwWAwGAwGg8FgMBgMBoO5J+AENAaDwWAwGAwGg8FgMBgMBoPBYO4JOAGNwWAwGAwGg8FgMBgMBoPBYDCYewLbLekp054YOGSy3EtpsZhvFeds3vB/mTeu2b8dP+nRiVMXrF217PTJg/aDS15Z6R8QvmTxZPuRpcvX2azWj99/Zt6Tb8QlpHC5/Nqa8l3bVh89vMtRl1Qmmz3nP9FxvcViudlkyM+7vm7NR6W3ih1lBg0ZM2bCYyrfYIKwVVYU796+9uSJ/Y4CLBbr0cf/n737jmvyeAMA/iQhk5Gw916CCxciLtxbtFpH1daNtmqtWlvrqKO12rpH696rjlrce0/cigtlI7JHgJBBkt8fr40hanKBl4D+nu/HP+TleN+79+6ee9/Lm3snNmgUYWllo1DIsjJTjx7aHHP9glGlRgghhBBCCCGEEEIIIVQBLCuhK2HSvgOievQeZW5upVKp2Gyuja1T/QYt794+V1JcRCWIaNvLx7dOZmbKk9jbmr/q1WekvYPbqeO7FAoFteXzAd9YCW0aNm5VK7iJmRlHrVZZWlkHBjW6ee2ERFJCpbGzc5g+Z5N/YAiPJ2AwGGw2x97B1de/7oWz/2r23L5j78HDfhSJ7OXyUiaTaW3jWK9By6yMpFdpiZo0k35YEhbehc+3UCqVbDbX2tohpGHrnKyUtNSEypw1hBBCCCGEEEIIIYQQQgaRLsEhEJi369hfrVadOLJ17Ig2M6f2S0l6ZmVlM2DQtxU4KpcncPMIeHDv8vTv+04a1/V1eqJAYBnSsLkmwfCoGXb2LuLCvN3b/vhmZNtF88feu30+7tldTQI2h9Or71gARvSBtWOHtx39VasrF6PZbE73XsM1aezsHGrXDZPLZZvXzRn1ZYsfvusV9/wem80JC+9cgTwjhBBCCCGEEEIIIYQQMgrpBHSb9r34fIvkxKd7dq5WyOVpaclbNvyqVCr9AkIqduArF6OX/j45/VWquLDw9s0zAMDmcKlfiaxt/AMbqtXqPTsWnzy+TyIpiX10Z/niH3ZtW6H5885d+1tYih7ev3Jw/0Zqy4Y1v+Zkp7u4+rA5HGoLg8lkMJhyWemD+9cBIDsrY9XSqffvXLhy6VDF8owQQgghhBBCCCGEEEKIHOka0C6u3gCQmhKn2ZIQ/*********************************************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)"], "metadata": {"id": "mX4Pv4Ha7YC3"}}, {"cell_type": "markdown", "source": ["![250611_18h33m50s_screenshot.png](data:image/png;base64,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)"], "metadata": {"id": "GwSDg3XHF1Ve"}}, {"cell_type": "markdown", "source": ["# Running the Example\n", "## Step 1: Set Up Environment\n", "```python\n", "export GOOGLE_API_KEY=\"your-gemini-api-key-here\"\n", "```\n", "## Step 2: Test the MCP Server (Optional)\n", "This starts the server and waits for connections. If it runs without errors, the conversion worked!\n", "```python\n", "python faiss_rag_agent_mcp_server.py\n", "```\n", "## Step 3: Run the Client Test\n", "```python\n", "python test_faiss_rag_mcp_client.py\n", "```\n"], "metadata": {"id": "TzY2LZq77nLb"}}, {"cell_type": "markdown", "source": ["## Key Takeaways\n", "\n", "### ✅ What We Accomplished\n", "\n", "1. **Self-Contained Agent**: Created a RAG agent with embedded FAISS tools\n", "2. **Agent → MCP Conversion**: Used `.to_mcp()` to convert the agent to an MCP server\n", "3. **Cross-Client Compatibility**: The MCP server works with any MCP client\n", "4. **Tool Composition**: Other agents can now use our RAG capabilities as tools\n"], "metadata": {"id": "k8ovcxfU5UBu"}}, {"cell_type": "markdown", "source": ["<div class=\"align-center\">\n", "  <a href=\"https://www.camel-ai.org/\"><img src=\"https://i.postimg.cc/KzQ5rfBC/button.png\"width=\"150\"></a>\n", "  <a href=\"https://discord.camel-ai.org\"><img src=\"https://i.postimg.cc/L4wPdG9N/join-2.png\"  width=\"150\"></a></a>\n", "  \n", "⭐ <i>Star us on [*Github*](https://github.com/camel-ai/camel), join our [*Discord*](https://discord.camel-ai.org) or follow our [*X*](https://x.com/camelaiorg)\n", "</div>"], "metadata": {"id": "LX-fx2OP8Z7Y"}}]}