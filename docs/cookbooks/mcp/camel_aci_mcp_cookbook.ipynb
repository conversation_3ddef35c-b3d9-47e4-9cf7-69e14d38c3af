{"cells": [{"cell_type": "markdown", "metadata": {"id": "ymsq1Lw0VEqT"}, "source": ["# CAMEL Cookbook: Pairing AI Agents with 600+ MCP Tools via ACI.dev"]}, {"cell_type": "markdown", "metadata": {"id": "7V3aV16AmY0K"}, "source": ["You can also check this cookbook in [Google Colab](https://drive.google.com/file/d/14Eznv3TZaT0Qnt6PvnMylk4i3yG6JCfz/view?usp=sharing)."]}, {"cell_type": "markdown", "metadata": {"id": "UvHRdXwflAz-"}, "source": ["<div class=\"align-center\">\n", "  <a href=\"https://www.camel-ai.org/\"><img src=\"https://i.postimg.cc/KzQ5rfBC/button.png\"width=\"150\"></a>\n", "  <a href=\"https://discord.camel-ai.org\"><img src=\"https://i.postimg.cc/L4wPdG9N/join-2.png\"  width=\"150\"></a></a>\n", "  \n", "⭐ <i>Star us on [*Github*](https://github.com/camel-ai/camel), join our [*Discord*](https://discord.camel-ai.org) or follow our [*X*](https://x.com/camelaiorg)\n", "</div>"]}, {"cell_type": "markdown", "metadata": {"id": "G5gE04UuPUWj"}, "source": ["This cookbook demonstrates how to supercharge your **CAMEL AI agents** by connecting them to 600+ MCP tools seamlessly through **ACI.dev**. We'll explore how to move beyond traditional tooling limitations and create powerful AI agents that can interact with multiple services like GitHub, Gmail, and more through a unified interface.\n", "\n", "**Key Learnings:**\n", "\n", "- Understanding the evolution from traditional tooling to MCP\n", "- How ACI.dev enhances vanilla MCP with better tool management\n", "- Setting up CAMEL AI agents with ACI's MCP server\n", "- Creating practical demos like GitHub repository management\n", "- Best practices for multi-app AI workflows\n", "\n", "This approach focuses on using **CAMEL with ACI.dev's enhanced MCP servers** to create more powerful and flexible AI agents."]}, {"cell_type": "markdown", "metadata": {"id": "0J0_iW-YVcq2"}, "source": ["## 📦 Installation"]}, {"cell_type": "markdown", "metadata": {"id": "7p-JjpyNVcCT"}, "source": ["First, install the required packages for this cookbook:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install \"camel-ai[all]==0.2.62\" python-dotenv rich uv"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> Note - This method uses uv, a fast Python installer and toolchain, to run the ACI.dev MCP server directly from the command line, as defined in our configuration script."]}, {"cell_type": "markdown", "metadata": {"id": "lfNvFbhD6o8B"}, "source": ["## 🔑 Setting Up API Keys"]}, {"cell_type": "markdown", "metadata": {"id": "jqV12oQfQTyl"}, "source": ["This cookbook uses multiple services that require API keys:\n", "\n", "1. **ACI.dev API Key**: Sign up at [ACI.dev](https://aci.dev) and get your API key from Project Settings\n", "2. **Google Gemini API Key**: Get your API key from [Google's API Console](https://console.developers.google.com/)\n", "3. **Linked Account Owner ID**: This is provided when you connect apps in ACI.dev\n", "\n", "The scripts will load these from environment variables, so you'll need to create a `.env` file."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🤖 Introduction\n", "\n", "LLMs have been in the AI landscape for some time now and so are the tools powering them.\n", "\n", "On their own, LLMs can crank out essays, spark creative ideas, or break down tricky concepts which in itself is pretty impressive.\n", "\n", "But let's be real: without the ability to connect to the world around them, *they're just fancy word machines*. What turns them into real problem-solvers, capable of grabbing fresh data or tackling tasks, is **tooling**."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔧 Traditional Tooling\n", "\n", "**Tooling** is essentially a set of directions that tells an LLM how to *kick off a specific action when you ask for it.*\n", "\n", "Imagine it as handing your AI a bunch of tasks to do, it wasn't built for, like pulling in the latest info or automating a process. The catch? **Historically, tooling has been a walled garden**. Every provider think OpenAI, Cursor, or others, has their own implementation of tooling, which creates a mismatch of setups that don't play nice together. It's a hassle for users and vendors alike."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🌐 MCP: The Better Tooling\n", "\n", "Which is what **MCP** solves. **MCP** is like a universal connector, a *straightforward protocol that lets any LLM, agent, or editor hook up with tools from any source.*\n", "\n", "It's built on a client-server setup: the **client** (your LLM or agent) talks to the server (where the tools live). When you need something beyond the LLM's cutoff knowledge, like up-to-date docs, it doesn't flounder. It pings the MCP server, grabs the right function's details, runs it, and delivers the answer in plain English.\n", "\n", "### MCP Architecture Example\n", "\n", "Here's a **practical example**:\n", "\n", "1. Imagine you're working in **<PERSON><PERSON><PERSON> (the client)** and need to implement a function using the latest React hooks from the React 18 documentation.\n", "2. You request, \"Please provide a useEffect setup for the current version.\"\n", "\n", "The challenge? The LLM powering *Cursor has a knowledge cutoff, so it's limited* to, say, React 17 and unaware of recent updates.\n", "\n", "With MCP, this isn't an issue. It connects to a search MCP server, retrieves the latest React documentation, and delivers the precise useEffect syntax directly from the source.\n", "\n", "It's like equipping your AI with a seamless connection to the most up-to-date resources, ensuring accuracy without any detours.\n", "\n", "*MCP's a game-changer, no question*. **But it's not perfect**. It often locks tools to single apps, requires hands-on setup for each one, and can't pick the best tool for the job on its own. **That's where ACI.dev steps in — to smooth out those rough edges and push things further.**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🚀 Outdoing Vanilla MCP\n", "\n", "### Why ACI.dev Takes MCP to the Next Level\n", "\n", "MCP lays a strong groundwork, but it's got some gaps. Let's break down where it stumbles and how ACI.dev steps up to fix it.\n", "\n", "With standard MCP:\n", "\n", "- **One server, one app**: You're stuck running separate servers for each tool — like one for GitHub, another for Gmail — which gets messy fast.\n", "- **Setup takes effort**: Every tool needs its own configuration, and dealing with OAuth for a bunch of them is a headache for a normal or enterprise user\n", "- **No smart tool picks**: MCP can't figure out the right tool for a task — you've got to spell it all out ahead of time in the prompt to let the LLM know what tool to use and execute.\n", "\n", "With these headaches in mind, ACI.dev built something better. Our platform ties AI to third-party software through tool-calling APIs, making integration and automation a breeze.\n", "\n", "It does this by introducing **two ways** to access MCP servers:\n", "\n", "- The **Apps MCP Server** and the **Unified MCP Server** to give your AI a cleaner way to tap into tools and data.\n", "\n", "This setup gives you access to 600+ MCP tools in the palm of your hand and make it easy for you to access any tool via both these methods."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### How ACI.dev Levels Up MCP\n", "\n", "- **All Your Apps, One Server** — ACI Apps MCP Server lets you set up tools like GitHub, Vercel, Cloudflare, and Gmail in one spot. It's a single hub for your AI's toolkit, keeping things simple.\n", "- **Tools That Find Themselves** - Forget predefining every tool. Unified MCP Server uses functions like ACI_SEARCH_FUNCTION and ACI_EXECUTE_FUNCTION to let your AI hunt down and run the perfect tool for the job.\n", "- **Smarter Context Handling** — MCP can bog down your LLM by stuffing its context with tools you don't need. ACI.dev keeps it lean, loading only what's necessary, when it's necessary, so your LLM has enough memory for actual token prediction.\n", "- **Smooth Cross-App Flows** — ACI.dev makes linking apps seamless without jumping between servers.\n", "- **Easy Setup, and Authentication** - Configuring tools individually can be time-consuming, but ACI simplifies the process by centralizing everything. Manage accounts, API keys, and settings in one hub. Just add apps from the ACI App Store, enable them in Project Settings, and link them with a single linked-account-owner-id. Done.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🛠️ Tutorial: Two Ways to Integrate CAMEL AI with ACI\n", "\n", "Alright, we've covered how MCP and ACI.dev make LLMs way more than just word generators. Now, let's get our hands dirty with practical demos using CAMEL AI. There are **two ways** to integrate CAMEL AI with ACI.dev:\n", "\n", "1. **MCP Server Approach** - Using CAMEL's MCPToolkit with ACI's MCP servers\n", "2. **Direct Toolkit Approach** - Using CAMEL's built-in ACIToolkit\n", "\n", "We'll explore both methods with hands-on examples. Let's dive in."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 1: Signing Up and Setting Up Your ACI.dev Project\n", "\n", "First things first, head to [ACI.dev](https://aci.dev) and sign up if you don't have an account. Once you're in, create a new project or pick one you've already got. This is your control hub for managing apps and snagging your API key.\n", "\n", "![aci](https://miro.medium.com/v2/resize:fit:1400/format:webp/1*3LoS4_biV27QxxQHKl3kcw.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 2: Adding Apps in the ACI App Store\n", "\n", "1. Zip over to the ACI App Store.\n", "2. Search for the GitHub app, hit \"Add,\" and follow the prompts to link your GitHub account. During the OAuth flow, you'll set a linked-account-owner-id (usually your email or a unique ID from ACI). Jot this down—you'll need it later.\n", "3. For these demos, <PERSON><PERSON><PERSON><PERSON> is our star player. Want to level up? You can add Brave Search or arXiv apps for extra firepower, but they're optional here.\n", "\n", "![log](https://miro.medium.com/v2/resize:fit:1400/format:webp/1*DvD7N7oRehBSxTahxZkebQ.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 3: Enabling Apps and Grabbing Your API Key\n", "\n", "1. Go to Project Settings and check the \"Allowed Apps\" section. Make sure GitHub (and any other apps you added) is toggled on. If it's not, flip that switch.\n", "2. Copy your API key from this page and keep it safe. It's the golden ticket for connecting CAMEL AI to ACI's services.\n", "\n", "![apps](https://miro.medium.com/v2/resize:fit:1400/format:webp/1*V22RnZyPGxbn15xteIrjZw.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 4: Environment Variables Setup\n", "\n", "Both methods use the same environment variables. Create a `.env` file in your project folder with these variables:\n", "\n", "```bash\n", "GEMINI_API_KEY=\"your_gemini_api_key_here\" \n", "ACI_API_KEY=\"your_aci_api_key_here\" \n", "LINKED_ACCOUNT_OWNER_ID=\"your_linked_account_owner_id_here\"\n", "```\n", "\n", "Replace:\n", "\n", "- `your_gemini_api_key_here` with your GEMINI API key for the Gemini model (get it from Google's API console)\n", "- `your_aci_api_key_here` with the API key from ACI.dev's Project Settings\n", "- `your_linked_account_owner_id_here` with the ID from the aci.dev platform"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔧 Method 1: Using MCP Server Approach\n", "\n", "This method uses CAMEL's MCPToolkit to connect to ACI's MCP servers. It's ideal when you want to leverage the full MCP ecosystem and have more control over server configurations.\n", "\n", "### Configuration Script\n", "\n", "Here's the `create_config.py` script to set up the MCP server connection:\n", "\n", "```python\n", "import os\n", "import json\n", "from dotenv import load_dotenv\n", "\n", "def create_config():\n", "    \"\"\"Create MCP config with proper environment variable substitution\"\"\"\n", "    load_dotenv() # load variables from the env\n", "    aci_api_key = os.getenv(\"ACI_API_KEY\")\n", "\n", "    if not aci_api_key:\n", "        raise ValueError(\"ACI_API_KEY environment variable is required\")\n", "    linked_account_owner_id = os.getenv(\"LINKED_ACCOUNT_OWNER_ID\")\n", "    if not linked_account_owner_id:\n", "        raise ValueError(\"LINKED_ACCOUNT_OWNER_ID environment variable is required\")\n", "    config = {\n", "        \"mcpServers\": {\n", "            \"aci_apps\": {\n", "                \"command\": \"uvx\",\n", "                \"args\": [\n", "                    \"aci-mcp\",\n", "                    \"apps-server\",\n", "                    \"--apps=GITHUB\",\n", "                    \"--linked-account-owner-id\",\n", "                    linked_account_owner_id,\n", "                ],\n", "                \"env\": {\"ACI_API_KEY\": aci_api_key},\n", "            }\n", "        }\n", "    }\n", "    with open(\"config.json\", \"w\") as f:\n", "        json.dump(config, f, indent=2)\n", "    print(\"✓ Config created successfully with API key\")\n", "    return config\n", "\n", "if __name__ == \"__main__\":\n", "    create_config()\n", "```\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Main CAMEL AI Agent <PERSON> (MCP Approach)\n", "\n", "Here's the `main.py` script to run the CAMEL AI agent:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#!/usr/bin/env python3\n", "import asyncio\n", "import os\n", "\n", "from dotenv import load_dotenv\n", "from rich import print as rprint\n", "\n", "from camel.agents import ChatAgent\n", "from camel.messages import BaseMessage\n", "from camel.models import ModelFactory\n", "from camel.toolkits import MCPToolkit\n", "from camel.types import ModelPlatformType, ModelType\n", "\n", "load_dotenv()\n", "\n", "async def main():\n", "    try:\n", "        from create_config import create_config  # creates config.json\n", "        rprint(\"[green]CAMEL AI Agent with MCP Toolkit[/green]\")\n", "        # Create config for MCP server\n", "        create_config()\n", "        # Connect to MCP server\n", "        rprint(\"Connecting to MCP server...\")\n", "        mcp_toolkit = MCPToolkit(config_path=\"config.json\")\n", "\n", "        await mcp_toolkit.connect()\n", "        tools = mcp_toolkit.get_tools() # connects and loads the tools in server\n", "        rprint(f\"Connected successfully. Found [cyan]{len(tools)}[/cyan] tools available\")\n", "        \n", "\n", "        # Set up Gemini model\n", "        model = ModelFactory.create(\n", "            model_platform=ModelPlatformType.GEMINI, # you can use other models here too\n", "            model_type=ModelType.GEMINI_2_5_PRO,\n", "            api_key=os.getenv(\"GEMINI_API_KEY\"),\n", "            model_config_dict={\"temperature\": 0.7, \"max_tokens\": 40000},\n", "        )\n", "        system_message = BaseMessage.make_assistant_message(\n", "            role_name=\"Assistant\",\n", "            content=\"You are a helpful assistant with access to GitHub tools via ACI's MCP server.\",\n", "        )\n", "\n", "        # Create CAMEL agent\n", "        agent = ChatAgent(\n", "            system_message=system_message,\n", "            model=model, # encapsulate your model tools and memory here\n", "            tools=tools\n", "        )\n", "        rprint(\"[green]Agent ready[/green]\")\n", "\n", "        # Get user query\n", "        user_query = input(\"\\nEnter your query: \")\n", "        user_message = BaseMessage.make_user_message(role_name=\"User\", content=user_query)\n", "        rprint(\"\\n[yellow]Processing...[/yellow]\")\n", "        response = await agent.astep(user_message) # ask agent the question ( async ) \n", "\n", "        # Show response\n", "        if response and hasattr(response, \"msgs\") and response.msgs:\n", "            rprint(f\"\\nFound [cyan]{len(response.msgs)}[/cyan] messages:\")\n", "            for i, msg in enumerate(response.msgs):\n", "                rprint(f\"Message {i+1}: {msg.content}\")\n", "        elif response:\n", "            rprint(f\"Response content: {response}\")\n", "        else:\n", "            rprint(\"[red]No response received[/red]\")\n", "        \n", "        # Disconnect from MCP\n", "        await mcp_toolkit.disconnect()\n", "        rprint(\"\\n[green]Done[/green]\")\n", "    except Exception as e:\n", "        rprint(f\"[red]Error: {e}[/red]\")\n", "        import traceback\n", "        rprint(f\"[dim]{traceback.format_exc()}[/dim]\")\n", "\n", "if __name__ == \"__main__\":\n", "    asyncio.run(main())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Step 5: Running the Demo Task (MCP Method)\n", "\n", "With everything set up, let's fire up the CAMEL AI agent and give it a job.\n", "\n", "##### Run the Script\n", "\n", "In your terminal, navigate to your project folder and run:\n", "\n", "```bash\n", "python main.py\n", "```\n", "\n", "This generates the config.json file, connects to the MCP server, and starts the agent. You'll see a prompt asking for your query.\n", "\n", "##### Enter the Query\n", "\n", "Type this into the prompt:\n", "\n", "```\n", "Create a new GitHub repository named 'my-ski-demo' with the description 'A demo repository for top US skiing locations' and push a README.md file with the content: '# Epic Ski Destinations\\nBest spots: Aspen, Vail, Park City.'\n", "```\n", "\n", "The agent will use the GitHub tool via the MCP server to create the repo and add the README.md file."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Method 2: Using Direct Toolkit Approach\n", "\n", "This method uses CAMEL's built-in ACIToolkit, which provides a more direct integration without needing MCP server configuration. It's simpler to set up and ideal for straightforward use cases.\n", "\n", "#### ACIToolkit Implementation\n", "\n", "Here's how to use the direct toolkit approach with the same environment setup:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from dotenv import load_dotenv\n", "from rich import print as rprint\n", "\n", "from camel.agents import ChatAgent\n", "from camel.models import ModelFactory\n", "from camel.toolkits import ACIToolkit\n", "from camel.types import ModelPlatformType, ModelType\n", "\n", "load_dotenv()\n", "\n", "def main():\n", "    rprint(\"[green]CAMEL AI with ACI Toolkit[/green]\")\n", "    \n", "    # get the linked account from env or use default\n", "    linked_account_owner_id = os.getenv(\"LINKED_ACCOUNT_OWNER_ID\")\n", "    if not linked_account_owner_id:\n", "        raise ValueError(\"LINKED_ACCOUNT_OWNER_ID environment variable is required\")\n", "    rprint(f\"Using account: [cyan]{linked_account_owner_id}[/cyan]\")\n", "    \n", "    # setup aci toolkit\n", "    aci_toolkit = ACIToolkit(linked_account_owner_id=linked_account_owner_id)\n", "    tools = aci_toolkit.get_tools()\n", "    rprint(f\"Loaded [cyan]{len(tools)}[/cyan] tools\")\n", "    \n", "    # setup gemini model\n", "    model = ModelFactory.create(\n", "            model_platform=ModelPlatformType.GEMINI, # you can use other models here too\n", "            model_type=ModelType.GEMINI_2_5_PRO,\n", "            api_key=os.getenv(\"GEMINI_API_KEY\"),\n", "            model_config_dict={\"temperature\": 0.7, \"max_tokens\": 40000},\n", "    )\n", "    \n", "    # create agent with tools\n", "    agent = ChatAgent(model=model, tools=tools)\n", "    rprint(\"[green]Agent ready[/green]\")\n", "    \n", "    # get user query\n", "    query = input(\"\\nEnter your query: \")\n", "    rprint(\"\\n[yellow]Processing...[/yellow]\")\n", "    \n", "    response = agent.step(query)\n", "    \n", "    # show raw response\n", "    rprint(f\"\\n[dim]{response.msg}[/dim]\")\n", "    rprint(f\"\\n[dim]Raw response type: {type(response)}[/dim]\")\n", "    rprint(f\"[dim]Response: {response}[/dim]\")\n", "    \n", "    # try to get the actual content\n", "    if hasattr(response, 'msgs') and response.msgs:\n", "        rprint(f\"\\nFound [cyan]{len(response.msgs)}[/cyan] messages:\")\n", "        for i, msg in enumerate(response.msgs):\n", "            rprint(f\"Message {i + 1}: {msg.content}\")\n", "    \n", "    rprint(\"\\n[green]Done[/green]\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Running the ACIToolkit Method\n", "\n", "1. Save the above script as `main_toolkit.py`\n", "2. Make sure your `.env` file has the required variables (same as MCP method)\n", "3. Run the script:\n", "\n", "```bash\n", "python main_toolkit.py\n", "```\n", "\n", "4. Enter your query when prompted, for example:\n", "\n", "```\n", "\"Create a GitHub repository named 'my-aci-toolkit-demo' and add a README.md file with the content '# ACI Toolkit Demo'.\"\n", "```\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Comparing Both Methods\n", "\n", "|Feature|MCP Approach|ACIToolkit Approach|\n", "|---|---|---|\n", "|**Setup Complexity**|More complex (requires config files)|Simpler (direct import)|\n", "|**Flexibility**|High (full MCP ecosystem)|Moderate (ACI-focused)|\n", "|**Performance**|Slightly more overhead|More direct, faster|\n", "|**Use Case**|Complex multi-server setups|Quick integrations|\n", "|**Dependencies**|Requires `uv` and MCP config|Just CAMEL and ACI|"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Choose MCP Approach when:**\n", "\n", "- You need to integrate multiple MCP servers\n", "- You want fine-grained control over server configuration\n", "- You're building complex multi-agent systems\n", "\n", "**Choose ACIToolkit Approach when:**\n", "\n", "- You want quick and simple ACI integration\n", "- You're prototyping or building straightforward workflows\n", "- You prefer minimal configuration overhead"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## ✅ Checking the Results (Both Methods)\n", "\n", "Once either agent finishes processing, head to your GitHub account to verify the results:\n", "\n", "1. Look for the newly created repository in your GitHub account\n", "2. Open the repo and verify that any files were created as requested\n", "3. Check the repository description and other metadata\n", "\n", "## 🔧 Troubleshooting and Tips (Both Methods)\n", "\n", "- **No Repo Created?** Double-check that your GitHub app is linked in ACI.dev and that your `.env` file has the correct `ACI_API_KEY` and `LINKED_ACCOUNT_OWNER_ID`.\n", "- **Event Loop Errors? (MCP Method)** If you hit a \"RuntimeError: Event loop is already running,\" try adding `import nest_asyncio; nest_asyncio.apply()` at the top of `main_mcp.py` to handle async conflicts.\n", "- **Import Errors? (ACIToolkit Method)** Make sure you have the latest version of CAMEL AI installed with `pip install --upgrade \"camel-ai[all]\"`\n", "- **Tool Loading Issues?** Both methods automatically discover available tools from your ACI account. Ensure your apps are properly enabled in ACI.dev Project Settings.\n", "- **API Rate Limits?** If you hit rate limits, the agents will typically handle retries automatically, but you may need to wait a moment between requests.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Example Queries\n", "\n", "You can modify the user query to ask different questions, such as:\n", "\n", "- \"Create a new repository and add multiple files with different content\"\n", "- \"Search for recent articles about AI agents and create a summary document\"\n", "- \"List my existing repositories and their descriptions\"\n", "- \"Create an issue in my repository with a bug report\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Conclusion\n", "\n", "The world of AI agents and tooling is buzzing with potential, and MCP is a solid step toward making LLMs more than just clever chatbots.\n", "\n", "In this cookbook, you've learned how to:\n", "\n", "- Understand the evolution from traditional tooling to MCP\n", "- Set up ACI.dev's enhanced MCP servers with CAMEL AI\n", "- Create practical AI agents that can interact with multiple services\n", "- Handle authentication and configuration seamlessly\n", "- Build workflows that span multiple applications\n", "\n", "As new ideas and implementations pop up in the agentic space, it's worth staying curious and watching for what's next. The future's wide open, and tools like these are just the start.\n", "\n", "**Happy coding!**\n"]}, {"cell_type": "markdown", "metadata": {"id": "s6Det-fcMb9A"}, "source": ["That's everything: Got questions about 🐫 CAMEL-AI? Join us on [Discord](https://discord.camel-ai.org)! Whether you want to share feedback, explore the latest in multi-agent systems, get support, or connect with others on exciting projects, we’d love to have you in the community! 🤝\n", "\n", "Check out some of our other work:\n", "\n", "1. 🐫 Creating Your First CAMEL Agent [free Colab](https://docs.camel-ai.org/cookbooks/create_your_first_agent.html)\n", "\n", "2.  Graph RAG Cookbook [free Colab](https://colab.research.google.com/drive/1uZKQSuu0qW6ukkuSv9TukLB9bVaS1H0U?usp=sharing)\n", "\n", "3. 🧑‍⚖️ Create A Hackathon Judge Committee with Workforce [free Colab](https://colab.research.google.com/drive/*********************************?usp=sharing)\n", "\n", "4. 🔥 3 ways to ingest data from websites with Firecrawl & CAMEL [free Colab](https://colab.research.google.com/drive/1lOmM3VmgR1hLwDKdeLGFve_75RFW0R9I?usp=sharing)\n", "\n", "5. 🦥 Agentic SFT Data Generation with CAMEL and Mistral Models, Fine-Tuned with Unsloth [free Colab](https://colab.research.google.com/drive/1lYgArBw7ARVPSpdwgKLYnp_NEXiNDOd-?usp=sharingg)\n", "\n", "Thanks from everyone at 🐫 CAMEL-AI\n", "\n", "\n", "<div class=\"align-center\">\n", "  <a href=\"https://www.camel-ai.org/\"><img src=\"https://i.postimg.cc/KzQ5rfBC/button.png\"width=\"150\"></a>\n", "  <a href=\"https://discord.camel-ai.org\"><img src=\"https://i.postimg.cc/L4wPdG9N/join-2.png\"  width=\"150\"></a></a>\n", "  \n", "⭐ <i>Star us on [*Github*](https://github.com/camel-ai/camel), join our [*Discord*](https://discord.camel-ai.org) or follow our [*X*](https://x.com/camelaiorg)\n", "</div>\n"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "kratos", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 0}