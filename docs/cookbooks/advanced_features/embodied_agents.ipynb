{"cells": [{"cell_type": "markdown", "metadata": {"id": "APw8wDolb0L9"}, "source": ["# Embodied Agents"]}, {"cell_type": "markdown", "metadata": {"id": "vLCfmNtRb-jR"}, "source": ["You can also check this cookbook in colab [here](https://colab.research.google.com/drive/17qCB6ezYfva87dNWlGA3D3zQ20NI-Sfk?usp=sharing)\n", "\n", "⭐ <i>Star us on [*Github*](https://github.com/camel-ai/camel), join our [*Discord*](https://discord.camel-ai.org) or follow our [*X*](https://x.com/camelaiorg)"]}, {"cell_type": "markdown", "metadata": {"id": "jHQhWNnhcOch"}, "source": ["## Philosophical Bits\n", "\n", "We believe the essence of intelligence emerges from its dynamic interactions with the external environment, where the use of various tools becomes a pivotal factor in its development and manifestation.\n", "\n", "The `EmbodiedAgent()` in CAMEL is an advanced conversational agent that leverages **code interpreters** and **tool agents** (*e.g.*, `HuggingFaceToolAgent()`) to execute diverse tasks efficiently. This agent represents a blend of advanced programming and AI capabilities, and is able to interact and respond within a dynamic environment."]}, {"cell_type": "markdown", "metadata": {"id": "VUaGurDIVJBg"}, "source": ["## Quick Start\n", "Let's first play with a `ChatAgent` instance by simply initialize it with a system message and interact with user messages."]}, {"cell_type": "markdown", "metadata": {"id": "u9NVFz-HVLXb"}, "source": ["### 🕹 Step 0: Preparations"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "UtcC3c-KVZmU"}, "outputs": [], "source": ["%pip install \"camel-ai==0.2.16\""]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "CmgKGeCxVON-"}, "outputs": [], "source": ["from camel.agents import EmbodiedAgent\n", "from camel.generators import SystemMessageGenerator as sys_msg_gen\n", "from camel.messages import BaseMessage as bm\n", "from camel.types import RoleType"]}, {"cell_type": "markdown", "metadata": {"id": "MyTTCe3IR_Lr"}, "source": ["### Setting Up API Keys"]}, {"cell_type": "markdown", "metadata": {"id": "REqzgGL9SEaD"}, "source": ["You'll need to set up your API keys for OpenAI."]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "PNBFEXc-R-0s", "outputId": "6e97c175-684c-47d7-866c-c23aea59910a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter your API key: ··········\n"]}], "source": ["import os\n", "from getpass import getpass\n", "\n", "# Prompt for the API key securely\n", "openai_api_key = getpass('Enter your API key: ')\n", "os.environ[\"OPENAI_API_KEY\"] = openai_api_key"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Alternatively, if running on Colab, you could save your API keys and tokens as **Colab Secrets**, and use them across notebooks.\n", "\n", "To do so, **comment out** the above **manual** API key prompt code block(s), and **uncomment** the following codeblock.\n", "\n", "⚠️ Don't forget granting access to the API key you would be using to the current notebook."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# from google.colab import userdata\n", "\n", "# os.environ[\"OPENAI_API_KEY\"] = userdata.get(\"OPENAI_API_KEY\")"]}, {"cell_type": "markdown", "metadata": {"id": "wcetZrjEcyo_"}, "source": ["### 🕹 Step 1: Define the Role\n", "We first need to set up the necessary information."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "i-pIc9eTc0SH"}, "outputs": [], "source": ["# Set the role name and the task\n", "role = 'Programmer'\n", "task = 'Writing and executing codes.'\n", "\n", "# Create the meta_dict and the role_tuple\n", "meta_dict = dict(role=role, task=task)\n", "role_tuple = (role, RoleType.EMBODIMENT)"]}, {"cell_type": "markdown", "metadata": {"id": "Lf6oJvsQc3lj"}, "source": ["The `meta_dict` and `role_type` will be used to generate the system message.\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "yA62jAfDc5CK"}, "outputs": [], "source": ["# Generate the system message based on this\n", "sys_msg = sys_msg_gen().from_dict(meta_dict=meta_dict, role_tuple=role_tuple)"]}, {"cell_type": "markdown", "metadata": {"id": "BEUEpNa_c8sS"}, "source": ["### 🕹 Step 2: Initialize the Agent 🐫\n", "Based on the system message, we are ready to initialize our embodied agent."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "VetPjL70c9be"}, "outputs": [], "source": ["embodied_agent = EmbodiedAgent(system_message=sys_msg,\n", "                               tool_agents=None,\n", "                               code_interpreter=None,\n", "                               verbose=True)"]}, {"cell_type": "markdown", "metadata": {"id": "o6aDvI1qc_kH"}, "source": ["Be aware that the default argument values for `tool_agents` and `code_interpreter` are `None`, and the underlying code interpreter is using the `SubProcessInterpreter()`, which handles the execution of code in Python and Bash within a subprocess."]}, {"cell_type": "markdown", "metadata": {"id": "MXzNaWp9dCvo"}, "source": ["### 🕹 Step 3: Interact with the Agent with `.step()`\n", "Use the base message wrapper to generate the user message."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"id": "Ts52u_UVdDJA"}, "outputs": [], "source": ["usr_msg = bm.make_user_message(\n", "    role_name='user',\n", "    content=('1. write a bash script to install numpy. '\n", "             '2. then write a python script to compute '\n", "             'the dot product of [8, 9] and [5, 4], '\n", "             'and print the result. '\n", "             '3. then write a script to search for '\n", "             'the weather at london with wttr.in/london.'))"]}, {"cell_type": "markdown", "metadata": {"id": "BlHF6LkRdFLo"}, "source": ["And feed that into your agents:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6hKpSzssdGvc", "outputId": "c7a8a799-de9f-4453-926f-c39ecef258a9"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[35m> Explanation:\n", "To accomplish the tasks you've outlined, I will perform the following actions:\n", "\n", "1. **Write a Bash script** to install NumPy using `pip`. This will ensure that the necessary library is available for the Python script that follows.\n", "2. **Write a Python script** to compute the dot product of the two lists `[8, 9]` and `[5, 4]`. The dot product is calculated as the sum of the products of the corresponding entries of the two sequences.\n", "3. **Write a script** to fetch the weather information for London using the `wttr.in` service. This will provide a simple way to get weather data from the command line.\n", "\n", "### Step 1: <PERSON><PERSON> to Install NumPy\n", "\n", "I'll create a Bash script named `install_numpy.sh` that will check if `pip` is installed and then use it to install NumPy.\n", "\n", "### Step 2: Python Script for Dot Product Calculation\n", "\n", "I'll create a Python script named `dot_product.py` that will compute the dot product of the two lists and print the result.\n", "\n", "### Step 3: <PERSON><PERSON><PERSON> to Fetch Weather Information\n", "\n", "I'll create a Bash script named `get_weather.sh` that will use `curl` to fetch the weather information for London from `wttr.in`.\n", "\n", "Now, let's implement these actions in code.\n", "\n", "\u001b[35m> Code:\n", "# Step 1: Bash script to install NumPy\n", "echo '#!/bin/bash' > install_numpy.sh\n", "echo 'if command -v pip &> /dev/null; then' >> install_numpy.sh\n", "echo '    echo \"Installing NumPy...\"' >> install_numpy.sh\n", "echo '    pip install numpy' >> install_numpy.sh\n", "echo 'else' >> install_numpy.sh\n", "echo '    echo \"pip is not installed. Please install pip first.\"' >> install_numpy.sh\n", "echo 'fi' >> install_numpy.sh\n", "chmod +x install_numpy.sh\n", "\n", "# Step 2: Python script for dot product calculation\n", "echo 'import numpy as np' > dot_product.py\n", "echo 'a = np.array([8, 9])' >> dot_product.py\n", "echo 'b = np.array([5, 4])' >> dot_product.py\n", "echo 'dot_product = np.dot(a, b)' >> dot_product.py\n", "echo 'print(\"Dot product:\", dot_product)' >> dot_product.py\n", "\n", "# Step 3: <PERSON>sh script to fetch weather information\n", "echo '#!/bin/bash' > get_weather.sh\n", "echo 'curl wttr.in/london' >> get_weather.sh\n", "chmod +x get_weather.sh\n", "\n", "\u001b[35m> Explanation:\n", "### Explanation of the Code\n", "\n", "1. **install_numpy.sh**: This script checks if `pip` is installed and installs NumPy if it is. It provides feedback if `pip` is not found.\n", "2. **dot_product.py**: This Python script uses NumPy to calculate the dot product of two arrays and prints the result.\n", "3. **get_weather.sh**: This script uses `curl` to fetch and display the weather information for London from `wttr.in`.\n", "\n", "### Next Steps\n", "\n", "I will execute these scripts in the appropriate order to ensure that NumPy is installed before running the Python script. After that, I will fetch the weather information for London. \n", "\n", "Let's execute the installation script first.\n", "\n", "\u001b[35m> Code:\n", "./install_numpy.sh\n", "\n", "\u001b[35m> Explanation:\n", "After confirming that NumPy is installed, I will run the Python script:\n", "\n", "\u001b[35m> Code:\n", "python3 dot_product.py\n", "\n", "\u001b[35m> Explanation:\n", "Finally, I will run the weather script:\n", "\n", "\u001b[35m> Code:\n", "./get_weather.sh\n", "\n", "\u001b[35m> Explanation:\n", "Now, I will perform these actions.\n", "\n", "The following bash code will run on your computer:\n", "\u001b[36m# Step 1: Bash script to install NumPy\n", "echo '#!/bin/bash' > install_numpy.sh\n", "echo 'if command -v pip &> /dev/null; then' >> install_numpy.sh\n", "echo '    echo \"Installing NumPy...\"' >> install_numpy.sh\n", "echo '    pip install numpy' >> install_numpy.sh\n", "echo 'else' >> install_numpy.sh\n", "echo '    echo \"pip is not installed. Please install pip first.\"' >> install_numpy.sh\n", "echo 'fi' >> install_numpy.sh\n", "chmod +x install_numpy.sh\n", "\n", "# Step 2: Python script for dot product calculation\n", "echo 'import numpy as np' > dot_product.py\n", "echo 'a = np.array([8, 9])' >> dot_product.py\n", "echo 'b = np.array([5, 4])' >> dot_product.py\n", "echo 'dot_product = np.dot(a, b)' >> dot_product.py\n", "echo 'print(\"Dot product:\", dot_product)' >> dot_product.py\n", "\n", "# Step 3: <PERSON>sh script to fetch weather information\n", "echo '#!/bin/bash' > get_weather.sh\n", "echo 'curl wttr.in/london' >> get_weather.sh\n", "chmod +x get_weather.sh\u001b[39m\n", "Running code? [Y/n]:y\n", "The following bash code will run on your computer:\n", "\u001b[36m./install_numpy.sh\u001b[39m\n", "Running code? [Y/n]:y\n", "The following bash code will run on your computer:\n", "\u001b[36mpython3 dot_product.py\u001b[39m\n", "Running code? [Y/n]:y\n", "The following bash code will run on your computer:\n", "\u001b[36m./get_weather.sh\u001b[39m\n", "Running code? [Y/n]:y\n", "======stderr======\n", "\u001b[31m  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100  8906  100  8906    0     0   9068      0 --:--:-- --:--:-- --:--:--  9069\n", "100  8906  100  8906    0     0   9068      0 --:--:-- --:--:-- --:--:--  9069\n", "\u001b[39m\n", "==================\n"]}], "source": ["response = embodied_agent.step(usr_msg)"]}, {"cell_type": "markdown", "metadata": {"id": "rP4Zma5ddJxh"}, "source": ["Under the hood, the agent will perform multiple actions within its action space in the OS to fulfill the user request. It will compose code to implement the action – no worries, it will ask for your permission before execution.\n", "\n", "Ideally you should get the output similar to this, if you allow the agent to perform actions:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "qA0bpOFldKPV", "outputId": "421418d2-61ca-4c65-f08e-83038c50a85a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1. write a bash script to install numpy. 2. then write a python script to compute the dot product of [8, 9] and [5, 4], and print the result. 3. then write a script to search for the weather at london with wttr.in/london.\n", "> Embodied Actions:\n", "\n", "> Executed Results:\n", "Executing code block 0: {\n", "Installing NumPy...\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (1.26.4)\n", "}\n", "Executing code block 1: {\n", "The dot product of [8, 9] and [5, 4] is: 76\n", "}\n", "Executing code block 2: {\n", "Fetching weather information for London...\n", "Weather report: London\n", "\n", "  \u001b[38;5;250m     .-.     \u001b[0m Light rain shower\n", "  \u001b[38;5;250m    (   ).   \u001b[0m \u001b[38;5;118m15\u001b[0m °C\u001b[0m          \n", "  \u001b[38;5;250m   (___(__)  \u001b[0m \u001b[1m↗\u001b[0m \u001b[38;5;190m12\u001b[0m km/h\u001b[0m      \n", "  \u001b[38;5;111m    ‘ ‘ ‘ ‘  \u001b[0m 3 km\u001b[0m           \n", "  \u001b[38;5;111m   ‘ ‘ ‘ ‘   \u001b[0m 3.4 mm\u001b[0m         \n", "                                                       ┌─────────────┐                                                       \n", "┌──────────────────────────────┬───────────────────────┤  Thu 26 Sep ├───────────────────────┬──────────────────────────────┐\n", "│            Morning           │             Noon      └──────┬──────┘     Evening           │             Night            │\n", "├──────────────────────────────┼──────────────────────────────┼──────────────────────────────┼──────────────────────────────┤\n", "│ \u001b[38;5;226m _`/\"\"\u001b[38;5;250m.-.    \u001b[0m Patchy light d…│ \u001b[38;5;226m _`/\"\"\u001b[38;5;250m.-.    \u001b[0m Patchy light d…│ \u001b[38;5;226m _`/\"\"\u001b[38;5;250m.-.    \u001b[0m Light rain sho…│ \u001b[38;5;226m _`/\"\"\u001b[38;5;250m.-.    \u001b[0m Patchy rain ne…│\n", "│ \u001b[38;5;226m  ,\\_\u001b[38;5;250m(   ).  \u001b[0m \u001b[38;5;154m16\u001b[0m °C\u001b[0m          │ \u001b[38;5;226m  ,\\_\u001b[38;5;250m(   ).  \u001b[0m \u001b[38;5;154m17\u001b[0m °C\u001b[0m          │ \u001b[38;5;226m  ,\\_\u001b[38;5;250m(   ).  \u001b[0m \u001b[38;5;118m+14\u001b[0m(\u001b[38;5;082m12\u001b[0m) °C\u001b[0m     │ \u001b[38;5;226m  ,\\_\u001b[38;5;250m(   ).  \u001b[0m \u001b[38;5;082m+12\u001b[0m(\u001b[38;5;082m10\u001b[0m) °C\u001b[0m     │\n", "│ \u001b[38;5;226m   /\u001b[38;5;250m(___(__) \u001b[0m \u001b[1m↗\u001b[0m \u001b[38;5;220m19\u001b[0m-\u001b[38;5;208m24\u001b[0m km/h\u001b[0m   │ \u001b[38;5;226m   /\u001b[38;5;250m(___(__) \u001b[0m \u001b[1m↗\u001b[0m \u001b[38;5;214m20\u001b[0m-\u001b[38;5;214m23\u001b[0m km/h\u001b[0m   │ \u001b[38;5;226m   /\u001b[38;5;250m(___(__) \u001b[0m \u001b[1m↗\u001b[0m \u001b[38;5;214m23\u001b[0m-\u001b[38;5;196m35\u001b[0m km/h\u001b[0m   │ \u001b[38;5;226m   /\u001b[38;5;250m(___(__) \u001b[0m \u001b[1m↗\u001b[0m \u001b[38;5;214m20\u001b[0m-\u001b[38;5;202m29\u001b[0m km/h\u001b[0m   │\n", "│ \u001b[38;5;111m     ‘ ‘ ‘ ‘ \u001b[0m 5 km\u001b[0m           │ \u001b[38;5;111m     ‘ ‘ ‘ ‘ \u001b[0m 5 km\u001b[0m           │ \u001b[38;5;111m     ‘ ‘ ‘ ‘ \u001b[0m 10 km\u001b[0m          │ \u001b[38;5;111m     ‘ ‘ ‘ ‘ \u001b[0m 10 km\u001b[0m          │\n", "│ \u001b[38;5;111m    ‘ ‘ ‘ ‘  \u001b[0m 0.5 mm | 100%\u001b[0m  │ \u001b[38;5;111m    ‘ ‘ ‘ ‘  \u001b[0m 0.2 mm | 100%\u001b[0m  │ \u001b[38;5;111m    ‘ ‘ ‘ ‘  \u001b[0m 0.6 mm | 100%\u001b[0m  │ \u001b[38;5;111m    ‘ ‘ ‘ ‘  \u001b[0m 0.0 mm | 65%\u001b[0m   │\n", "└──────────────────────────────┴──────────────────────────────┴──────────────────────────────┴──────────────────────────────┘\n", "                                                       ┌─────────────┐                                                       \n", "┌──────────────────────────────┬───────────────────────┤  Fri 27 Sep ├───────────────────────┬──────────────────────────────┐\n", "│            Morning           │             Noon      └──────┬──────┘     Evening           │             Night            │\n", "├──────────────────────────────┼──────────────────────────────┼──────────────────────────────┼──────────────────────────────┤\n", "│ \u001b[38;5;250m     .-.     \u001b[0m Light rain     │ \u001b[38;5;226m _`/\"\"\u001b[38;5;250m.-.    \u001b[0m Patchy rain ne…│ \u001b[38;5;226m    \\   /    \u001b[0m <PERSON>          │ \u001b[38;5;226m    \\   /    \u001b[0m Clear          │\n", "│ \u001b[38;5;250m    (   ).   \u001b[0m \u001b[38;5;046m+9\u001b[0m(\u001b[38;5;047m7\u001b[0m) °C\u001b[0m       │ \u001b[38;5;226m  ,\\_\u001b[38;5;250m(   ).  \u001b[0m \u001b[38;5;082m+11\u001b[0m(\u001b[38;5;046m9\u001b[0m) °C\u001b[0m      │ \u001b[38;5;226m     .-.     \u001b[0m \u001b[38;5;082m+11\u001b[0m(\u001b[38;5;046m8\u001b[0m) °C\u001b[0m      │ \u001b[38;5;226m     .-.     \u001b[0m \u001b[38;5;046m+9\u001b[0m(\u001b[38;5;046m8\u001b[0m) °C\u001b[0m       │\n", "│ \u001b[38;5;250m   (___(__)  \u001b[0m \u001b[1m↘\u001b[0m \u001b[38;5;220m18\u001b[0m-\u001b[38;5;208m26\u001b[0m km/h\u001b[0m   │ \u001b[38;5;226m   /\u001b[38;5;250m(___(__) \u001b[0m \u001b[1m↘\u001b[0m \u001b[38;5;214m23\u001b[0m-\u001b[38;5;202m31\u001b[0m km/h\u001b[0m   │ \u001b[38;5;226m  ― (   ) ―  \u001b[0m \u001b[1m↘\u001b[0m \u001b[38;5;220m16\u001b[0m-\u001b[38;5;208m24\u001b[0m km/h\u001b[0m   │ \u001b[38;5;226m  ― (   ) ―  \u001b[0m \u001b[1m↘\u001b[0m \u001b[38;5;190m10\u001b[0m-\u001b[38;5;220m16\u001b[0m km/h\u001b[0m   │\n", "│ \u001b[38;5;111m    ‘ ‘ ‘ ‘  \u001b[0m 9 km\u001b[0m           │ \u001b[38;5;111m     ‘ ‘ ‘ ‘ \u001b[0m 10 km\u001b[0m          │ \u001b[38;5;226m     `-’     \u001b[0m 10 km\u001b[0m          │ \u001b[38;5;226m     `-’     \u001b[0m 10 km\u001b[0m          │\n", "│ \u001b[38;5;111m   ‘ ‘ ‘ ‘   \u001b[0m 0.8 mm | 100%\u001b[0m  │ \u001b[38;5;111m    ‘ ‘ ‘ ‘  \u001b[0m 0.1 mm | 100%\u001b[0m  │ \u001b[38;5;226m    /   \\    \u001b[0m 0.0 mm | 0%\u001b[0m    │ \u001b[38;5;226m    /   \\    \u001b[0m 0.0 mm | 0%\u001b[0m    │\n", "└──────────────────────────────┴──────────────────────────────┴──────────────────────────────┴──────────────────────────────┘\n", "                                                       ┌─────────────┐                                                       \n", "┌──────────────────────────────┬───────────────────────┤  Sat 28 Sep ├───────────────────────┬──────────────────────────────┐\n", "│            Morning           │             Noon      └──────┬──────┘     Evening           │             Night            │\n", "├──────────────────────────────┼──────────────────────────────┼──────────────────────────────┼──────────────────────────────┤\n", "│ \u001b[38;5;226m    \\   /    \u001b[0m <PERSON>          │ \u001b[38;5;226m _`/\"\"\u001b[38;5;250m.-.    \u001b[0m Patchy rain ne…│               Cloudy         │ \u001b[38;5;226m   \\  /\u001b[0m       Partly Cloudy  │\n", "│ \u001b[38;5;226m     .-.     \u001b[0m \u001b[38;5;082m+11\u001b[0m(\u001b[38;5;046m9\u001b[0m) °C\u001b[0m      │ \u001b[38;5;226m  ,\\_\u001b[38;5;250m(   ).  \u001b[0m \u001b[38;5;118m+14\u001b[0m(\u001b[38;5;118m13\u001b[0m) °C\u001b[0m     │ \u001b[38;5;250m     .--.    \u001b[0m \u001b[38;5;118m13\u001b[0m °C\u001b[0m          │ \u001b[38;5;226m _ /\"\"\u001b[38;5;250m.-.    \u001b[0m \u001b[38;5;082m12\u001b[0m °C\u001b[0m          │\n", "│ \u001b[38;5;226m  ― (   ) ―  \u001b[0m \u001b[1m→\u001b[0m \u001b[38;5;190m11\u001b[0m-\u001b[38;5;226m14\u001b[0m km/h\u001b[0m   │ \u001b[38;5;226m   /\u001b[38;5;250m(___(__) \u001b[0m \u001b[1m→\u001b[0m \u001b[38;5;190m12\u001b[0m-\u001b[38;5;226m14\u001b[0m km/h\u001b[0m   │ \u001b[38;5;250m  .-(    ).  \u001b[0m \u001b[1m→\u001b[0m \u001b[38;5;118m6\u001b[0m-\u001b[38;5;190m10\u001b[0m km/h\u001b[0m    │ \u001b[38;5;226m   \\_\u001b[38;5;250m(   ).  \u001b[0m \u001b[1m↗\u001b[0m \u001b[38;5;118m5\u001b[0m-\u001b[38;5;154m8\u001b[0m km/h\u001b[0m     │\n", "│ \u001b[38;5;226m     `-’     \u001b[0m 10 km\u001b[0m          │ \u001b[38;5;111m     ‘ ‘ ‘ ‘ \u001b[0m 10 km\u001b[0m          │ \u001b[38;5;250m (___.__)__) \u001b[0m 10 km\u001b[0m          │ \u001b[38;5;226m   /\u001b[38;5;250m(___(__) \u001b[0m 10 km\u001b[0m          │\n", "│ \u001b[38;5;226m    /   \\    \u001b[0m 0.0 mm | 0%\u001b[0m    │ \u001b[38;5;111m    ‘ ‘ ‘ ‘  \u001b[0m 0.1 mm | 100%\u001b[0m  │               0.0 mm | 0%\u001b[0m    │               0.0 mm | 0%\u001b[0m    │\n", "└──────────────────────────────┴──────────────────────────────┴──────────────────────────────┴──────────────────────────────┘\n", "Location: London, Greater London, England, UK [51.5073219,-0.1276474]\n", "\n", "Follow \u001b[46m\u001b[30m@igor_chubin\u001b[0m for wttr.in updates\n", "(stderr:   % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "  0     0    0     0    0     0      0      0 --:--:--  0:00:01 --:--:--     0\n", "  0     0    0     0    0     0      0      0 --:--:--  0:00:02 --:--:--     0\n", "100  8977  100  8977    0     0   3495      0  0:00:02  0:00:02 --:--:--  3495\n", ")}\n", "Executing code block 3: {\n", "}\n", "\n"]}], "source": ["print(response.msg.content)"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}