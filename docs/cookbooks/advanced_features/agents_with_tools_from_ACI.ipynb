{"cells": [{"cell_type": "markdown", "metadata": {"id": "y5ra1tKcXxt9"}, "source": ["# Using Tools from ACI"]}, {"cell_type": "markdown", "source": ["You can also check this cookbook in colab [here](https://colab.research.google.com/drive/1vMdwL4MZdWk8O8vFwc9ROuC1KtynFfSi?usp=sharing)"], "metadata": {"id": "7l6fBprLYBmw"}}, {"cell_type": "markdown", "metadata": {"id": "9Tz2K7D6Xxt-"}, "source": ["[ACI.dev](https://github.com/aipotheosis-labs/aci) is the open source platform that connects your AI agents to 600+ tool integrations. Integrate [ACI.dev](https://www.aci.dev/docs/introduction/overview) with CAMEL agents to let them seamlessly interact with these external apps."]}, {"cell_type": "markdown", "metadata": {"id": "Rp5aGsG0Xxt-"}, "source": ["\n", "⭐ <i>Star us on [*Github*](https://github.com/camel-ai/camel), join our [*Discord*](https://discord.camel-ai.org) or follow our [*X*](https://x.com/camelaiorg)"]}, {"cell_type": "markdown", "metadata": {"id": "dMB-ePtYXxt_"}, "source": ["*Goal: Star a repository on GitHub with natural language & CAMEL Agent*"]}, {"cell_type": "markdown", "metadata": {"id": "AxObRHzdXxt_"}, "source": ["### Install Packages & Connect a Tool"]}, {"cell_type": "markdown", "metadata": {"id": "Nys9izCgXxt_"}, "source": ["Integrate ACI with CAMEL agents to let them seamlessly interact with external apps."]}, {"cell_type": "markdown", "metadata": {"id": "Nw-ruYT3XxuA"}, "source": ["Ensure you have the necessary packages installed and connect your GitHub account to allow your CAMEL-AI agents to utilize GitHub functionalities on [*ACI.dev*](https://platform.aci.dev/apps)."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Vbzhv9tRXxuA"}, "outputs": [], "source": ["# Run command\n", "%pip install \"camel-ai[all]==0.2.59\""]}, {"cell_type": "markdown", "metadata": {"id": "kSdmyLrZXxuB"}, "source": ["### Prepare your environment by initializing necessary imports from CAMEL."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "aQIxupKSXxuB"}, "outputs": [], "source": ["from camel.agents import ChatAgent\n", "from camel.models import ModelFactory\n", "from camel.toolkits import ACIToolkit\n", "from camel.types import ModelPlatformType, ModelType"]}, {"cell_type": "markdown", "metadata": {"id": "JaBbWyEpXxuB"}, "source": ["### Provide the API key and the LINKED_ACCOUNT_OWNER from [*ACI.dev*](https://platform.aci.dev/apps) to the SDK."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "fAhEfnlwXxuB"}, "outputs": [], "source": ["import os\n", "from getpass import getpass\n", "\n", "# Prompt for the API key securely\n", "openai_api_key = getpass('Enter your API key: ')\n", "os.environ[\"OPENAI_API_KEY\"] = openai_api_key\n", "# Prompt for the ACI key securely\n", "aci_api_key = getpass('Enter your ACI API key: ')\n", "os.environ[\"ACI_API_KEY\"] = aci_api_key\n", "# Prompt for your linked account owner id securely\n", "linked_account_owner_id = getpass('Enter your linked account owner id: ')\n", "os.environ[\"LINKED_ACCOUNT_OWNER\"] = linked_account_owner_id"]}, {"cell_type": "markdown", "metadata": {"id": "xSAU5mkpXxuC"}, "source": ["### Let’s run CAMEL agents with tools from ACI!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "4ZkkAvqmXxuC"}, "outputs": [], "source": ["# Get the value of the environment variable \"LINKED_ACCOUNT_OWNER\"\n", "LINKED_ACCOUNT_OWNER = os.getenv(\"LINKED_ACCOUNT_OWNER\")\n", "\n", "# Check if the environment variable was set\n", "if LINKED_ACCOUNT_OWNER is None:\n", "    raise ValueError(\"LINKED_ACCOUNT_owner environment variable is not set.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ONtulpJaXxuC"}, "outputs": [], "source": ["# Initialize ACI Toolkit with GitHub integration\n", "aci_toolkit = ACIToolkit(linked_account_owner_id=LINKED_ACCOUNT_OWNER)\n", "\n", "# Create default model instance\n", "model = ModelFactory.create(\n", "    model_platform=ModelPlatformType.DEFAULT,\n", "    model_type=ModelType.DEFAULT,\n", ")\n", "\n", "# Set up chat agent with GitHub tools\n", "chat_agent = ChatAgent(\n", "    model=model,\n", "    tools=aci_toolkit.get_tools(),  # GitHub tools enabled\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "SQZTOcdTXxuC", "outputId": "01df4d08-7f6d-4d82-aef3-2ac8ec77a997"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["msgs=[BaseMessage(role_name='assistant', role_type=<RoleType.ASSISTANT: 'assistant'>, meta_dict={}, content='The repository **camel-ai/camel** has been successfully starred!', video_bytes=None, image_list=None, image_detail='auto', video_detail='low', parsed=None)] terminated=False info={'id': 'chatcmpl-BXS3BEiBjdRUJx2Ucu5ZdRadsE7s7', 'usage': {'prompt_tokens': 7713, 'completion_tokens': 41, 'total_tokens': 7754}, 'termination_reasons': ['stop'], 'num_tokens': 71, 'tool_calls': [ToolCallingRecord(tool_name='GITHUB__STAR_REPOSITORY', args={'path': {'repo': 'camel', 'owner': 'camel-ai'}}, result={'success': True, 'data': {}}, tool_call_id='call_tW60m9BWrXGmRMd5AbwASRdx')], 'external_tool_call_requests': None}\n"]}], "source": ["# Execute GitHub star command\n", "response = chat_agent.step(\"star the repo camel-ai/camel\")\n", "print(response)"]}, {"cell_type": "markdown", "source": ["That's everything: Got questions about 🐫 CAMEL-AI? Join us on [Discord](https://discord.camel-ai.org)! Whether you want to share feedback, explore the latest in multi-agent systems, get support, or connect with others on exciting projects, we’d love to have you in the community! 🤝\n", "\n", "Check out some of our other work:\n", "\n", "1. 🐫 Creating Your First CAMEL Agent [free Colab](https://docs.camel-ai.org/cookbooks/create_your_first_agent.html)\n", "\n", "2.  Graph RAG Cookbook [free Colab](https://colab.research.google.com/drive/1uZKQSuu0qW6ukkuSv9TukLB9bVaS1H0U?usp=sharing)\n", "\n", "3. 🧑‍⚖️ Create A Hackathon Judge Committee with Workforce [free Colab](https://colab.research.google.com/drive/*********************************?usp=sharing)\n", "\n", "4. 🔥 3 ways to ingest data from websites with Firecrawl & CAMEL [free Colab](https://colab.research.google.com/drive/1lOmM3VmgR1hLwDKdeLGFve_75RFW0R9I?usp=sharing)\n", "\n", "5. 🦥 Agentic SFT Data Generation with CAMEL and Mistral Models, Fine-Tuned with Unsloth [free Colab](https://colab.research.google.com/drive/1lYgArBw7ARVPSpdwgKLYnp_NEXiNDOd-?usp=sharingg)\n", "\n", "Thanks from everyone at 🐫 CAMEL-AI\n", "\n", "\n", "<div class=\"align-center\">\n", "  <a href=\"https://www.camel-ai.org/\"><img src=\"https://i.postimg.cc/KzQ5rfBC/button.png\"width=\"150\"></a>\n", "  <a href=\"https://discord.camel-ai.org\"><img src=\"https://i.postimg.cc/L4wPdG9N/join-2.png\"  width=\"150\"></a></a>\n", "  \n", "⭐ <i>Star us on [*Github*](https://github.com/camel-ai/camel), join our [*Discord*](https://discord.camel-ai.org) or follow our [*X*](https://x.com/camelaiorg)\n", "</div>\n"], "metadata": {"id": "s6Det-fcMb9A"}}], "metadata": {"kernelspec": {"display_name": "ai_agent_py11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}, "colab": {"provenance": []}}, "nbformat": 4, "nbformat_minor": 0}