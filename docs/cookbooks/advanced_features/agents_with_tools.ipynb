{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {"id": "ssX_map8c6mx"}, "source": ["# Tools Cookbook"]}, {"cell_type": "markdown", "metadata": {"id": "9S0pePjZkwUz"}, "source": ["You can also check this cookbook in colab [here](https://colab.research.google.com/drive/1f1jYwDy6pB8QB6c_UdoBvFx6wr6kA5xq?usp=sharing)\n", "\n", "⭐ <i>Star us on [*Github*](https://github.com/camel-ai/camel), join our [*Discord*](https://discord.camel-ai.org) or follow our [*X*](https://x.com/camelaiorg)"]}, {"cell_type": "markdown", "metadata": {"id": "u1K3dKYUdMqJ"}, "source": ["## *TLDR:*\n", "*CAMEL allows AI agents to extend their capabilities by integrating custom tools, similar to how humans use tools to surpass natural limits. This tutorial shows how to set up and customize tools within CAMEL, from basic functions like calculators to creating multi-agent systems that collaborate on tasks. You’ll learn to equip AI agents with the ability to use tools for various tasks, making them more powerful and versatile. Engage with the CAMEL-AI community and explore extensive resources to push the boundaries of AI development. Ready to enhance your AI agents? Dive into the tutorial and start building.*"]}, {"cell_type": "markdown", "metadata": {"id": "eIT3wf4E3_Al"}, "source": ["## ‍Table of Content:\n", "\n", "* Introduction\n", "* Tool Usage of a Single Agent (Customize Your Own Tools)\n", "* AI Society with Tool Usage\n", "* Conclusion"]}, {"cell_type": "markdown", "metadata": {"id": "3hEOpgXI4NZg"}, "source": ["## Introduction\n", "The key difference between humans and animals lies in the human ability to create and use tools, allowing us to shape the world beyond natural limits. Similarly, in AI, Large Language Models (LLMs) enable agents to utilize external tools, acting as extensions of their capabilities. These tools, each with a specific name, purpose, input, and output, empower agents to perform tasks otherwise impossible.\n", "\n", "This tutorial will show you how to use tools integrated by CAMEL and how to customize your own tools."]}, {"cell_type": "markdown", "metadata": {"id": "_5Xjj24c4VAV"}, "source": ["## Tool Usage of a Single Agent\n", "A single agent can utilize multiple tools to answer questions, take actions, or perform complex tasks. Here you will build an agent using both the supported toolkit in CAMEL and the tool customized by you.\n", "\n", "First we are going to take the search tool as an example for utilizing existing tools but you can also see some of the other tools supported by CAMEL below.\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "id": "1W4sgCoEdEpg"}, "outputs": [], "source": ["!pip install \"camel-ai[all]==0.2.16\""]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "-Di3R-xNNviB"}, "outputs": [], "source": ["from camel.agents import ChatAgent\n", "from camel.configs import ChatGPTConfig\n", "from camel.toolkits import (\n", "    SearchToolkit,\n", "    # MathToolkit,\n", "    # GoogleMapsToolkit,\n", "    # TwitterToolkit,\n", "    # WeatherToolkit,\n", "    # RetrievalToolkit,\n", "    # TwitterToolkit,\n", "    # SlackToolkit,\n", "    # LinkedInToolkit,\n", "    # RedditToolkit,\n", ")\n", "from camel.messages import BaseMessage\n", "from camel.models import ModelFactory\n", "from camel.types import ModelPlatformType, ModelType"]}, {"cell_type": "markdown", "metadata": {"id": "7OmxjHKPWgp0"}, "source": ["After importing necessary modules, you need to set up your OpenAI key."]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ICse1xFtSFvf", "outputId": "ef9d4e60-f504-4020-aa0a-66cc5b2e6029"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter your API key: ··········\n"]}], "source": ["import os\n", "from getpass import getpass\n", "\n", "# Prompt for the API key securely\n", "openai_api_key = getpass('Enter your API key: ')\n", "os.environ[\"OPENAI_API_KEY\"] = openai_api_key"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Alternatively, if running on Colab, you could save your API keys and tokens as **Colab Secrets**, and use them across notebooks.\n", "\n", "To do so, **comment out** the above **manual** API key prompt code block(s), and **uncomment** the following codeblock.\n", "\n", "⚠️ Don't forget granting access to the API key you would be using to the current notebook."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# from google.colab import userdata\n", "\n", "# os.environ[\"OPENAI_API_KEY\"] = userdata.get(\"OPENAI_API_KEY\")"]}, {"cell_type": "markdown", "metadata": {"id": "bJ1J9e4mWkBZ"}, "source": ["Now you have done that, let’s customize a tool by taking the simple math calculator, functions add and sub, as an example. When you define your own function, make sure the argument name and docstring are clear so that the agent can understand what this function can do and when to use the function based on the function information you provide. \n", "> This is just to demonstrate the use of custom tools, the built-in MathToolkit already includes tools for add and sub."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "XPOqaGYaXDlH"}, "outputs": [], "source": ["def add(a: int, b: int) -> int:\n", "    r\"\"\"Adds two numbers.\n", "\n", "    Args:\n", "        a (int): The first number to be added.\n", "        b (int): The second number to be added.\n", "\n", "    Returns:\n", "        integer: The sum of the two numbers.\n", "    \"\"\"\n", "    return a + b\n", "\n", "def sub(a: int, b: int) -> int:\n", "    r\"\"\"Do subtraction between two numbers.\n", "\n", "    Args:\n", "        a (int): The minuend in subtraction.\n", "        b (int): The subtrahend in subtraction.\n", "\n", "    Returns:\n", "        integer: The result of subtracting :obj:`b` from :obj:`a`.\n", "    \"\"\"\n", "    return a - b"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"id": "uv0pYDQvXGER"}, "source": ["Add these 2 customized functions as CAMEL’s FunctionTool list:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "gWGxfYn6VbTw"}, "outputs": [], "source": ["from camel.toolkits import FunctionTool\n", "\n", "\n", "MATH_FUNCS: list[FunctionTool] = [\n", "    FunctionTool(func) for func in [add, sub]\n", "]"]}, {"cell_type": "markdown", "metadata": {"id": "nfhwO6_HXVj3"}, "source": ["Then you can add the tool from CAMEL and the one defined by yourself to the tool list:\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "b2LQwcVeXfTC"}, "outputs": [], "source": ["tools_list = [\n", "    # *MathToolkit().get_tools(),\n", "    *SearchToolkit().get_tools(),\n", "    *MATH_FUNCS,\n", "]"]}, {"cell_type": "markdown", "metadata": {"id": "B00S0YeWXhjk"}, "source": ["Next let's set the parameters to the agent and initianize ChatAgent to call the tool:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"id": "xJRlrUFnOj4v"}, "outputs": [], "source": ["# Set the backend mode, this model should support tool calling\n", "model=ModelFactory.create(\n", "    model_platform=ModelPlatformType.OPENAI,\n", "    model_type=ModelType.GPT_4O_MINI\n", ")\n", "\n", "# Set message for the assistant\n", "assistant_sys_msg =  \"\"\"You are a helpful assistant to do search task.\"\"\"\n", "\n", "\n", "# Set the agent\n", "agent = ChatAgent(\n", "    assistant_sys_msg,\n", "    model=model,\n", "    tools=tools_list\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "EiXsPSsUYkmY"}, "source": ["Here we define two test prompts for the agent, asking about the facts about University of Oxford. Here the agent needs to take advantage of the searching capability to know when University of Oxford is founded and the calculating skills to obtain the estimated age of the Uni."]}, {"cell_type": "code", "execution_count": 8, "metadata": {"id": "FFI2B-prYqfX"}, "outputs": [], "source": ["# Set prompt for the search task\n", "prompt_search = (\"\"\"When was University of Oxford set up\"\"\")\n", "# Set prompt for the calculation task\n", "prompt_calculate = (\"\"\"Assume now is 2024 in the Gregorian calendar, University of Oxford was set up in 1096, estimate the current age of University of Oxford\"\"\")\n", "\n", "# Convert the two prompt as message that can be accepted by the Agent\n", "user_msg_search = BaseMessage.make_user_message(role_name=\"User\", content=prompt_search)\n", "user_msg_calculate = BaseMessage.make_user_message(role_name=\"User\", content=prompt_calculate)\n", "\n", "# Get response\n", "assistant_response_search = agent.step(user_msg_search)\n", "assistant_response_calculate = agent.step(user_msg_calculate)"]}, {"cell_type": "markdown", "metadata": {"id": "bF3Sg9h7ZHcq"}, "source": ["\n", "Let’s see the agent' performance for answering above questions. The agent should tell you correctly when University of Oxford was set up and its estimated age!"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "kZxoXxQ-TJRn", "outputId": "2f9070fd-eb61-4686-ef61-850539b394d6"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[FunctionCallingRecord(func_name='search_wiki', args={'entity': 'University of Oxford'}, result=\"The University of Oxford is a collegiate research university in Oxford, England. There is evidence of teaching as early as 1096, making it the oldest university in the English-speaking world and the world's second-oldest university in continuous operation. It grew rapidly from 1167, when <PERSON> banned English students from attending the University of Paris. After disputes between students and Oxford townsfolk, some Oxford academics fled northeast to Cambridge, where, in 1209, they established the University of Cambridge. The two English ancient universities share many common features and are jointly referred to as Oxbridge.\")]\n"]}], "source": ["print(assistant_response_search.info['tool_calls'])"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "arIWzrtyWVqX", "outputId": "c0560025-9f7f-4ead-ab0b-b9ef430b06fe"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[FunctionCallingRecord(func_name='sub', args={'a': 2024, 'b': 1096}, result=928)]\n"]}], "source": ["print(assistant_response_calculate.info['tool_calls'])"]}, {"cell_type": "markdown", "metadata": {"id": "DWib8WZOZQeA"}, "source": ["## AI Society with Tool <PERSON>age\n", "Now you've enabled a single agent to utilize tools, but you can surelytake this concept further. Let's establish a small AI ecosystem. This setup will consist of two agents: a user agent and an assistant agent. The assistant agent will be the one we've just configured with tool-using capabilities."]}, {"cell_type": "code", "execution_count": 11, "metadata": {"id": "DeRHdVnmXd6d"}, "outputs": [], "source": ["from camel.societies import RolePlaying\n", "from camel.agents.chat_agent import FunctionCallingRecord\n", "from camel.utils import print_text_animated\n", "from colorama import Fore"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "GJX-E1V-XQv7", "outputId": "febde069-faf7-4358-c157-fe024a8c8330"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[32mAI Assistant sys message:\n", "BaseMessage(role_name='Searcher', role_type=<RoleType.ASSISTANT: 'assistant'>, meta_dict={'task': 'Assume now is 2024 in the Gregorian calendar, estimate the current age of University of Oxford and then add 10 more years to this age.', 'assistant_role': 'Searcher', 'user_role': 'Professor'}, content='===== RULES OF ASSISTANT =====\\nNever forget you are a Searcher and I am a Professor. Never flip roles! Never instruct me!\\nWe share a common interest in collaborating to successfully complete a task.\\nYou must help me to complete the task.\\nHere is the task: Assume now is 2024 in the Gregorian calendar, estimate the current age of University of Oxford and then add 10 more years to this age.. Never forget our task!\\nI must instruct you based on your expertise and my needs to complete the task.\\n\\nI must give you one instruction at a time.\\nYou must write a specific solution that appropriately solves the requested instruction and explain your solutions.\\nYou must decline my instruction honestly if you cannot perform the instruction due to physical, moral, legal reasons or your capability and explain the reasons.\\nUnless I say the task is completed, you should always start with:\\n\\nSolution: <YOUR_SOLUTION>\\n\\n<YOUR_SOLUTION> should be very specific, include detailed explanations and provide preferable detailed implementations and examples and lists for task-solving.\\nAlways end <YOUR_SOLUTION> with: Next request.', video_bytes=None, image_list=None, image_detail='auto', video_detail='low')\n", "\n", "\u001b[34mAI User sys message:\n", "BaseMessage(role_name='Professor', role_type=<RoleType.USER: 'user'>, meta_dict={'task': 'Assume now is 2024 in the Gregorian calendar, estimate the current age of University of Oxford and then add 10 more years to this age.', 'assistant_role': 'Searcher', 'user_role': 'Professor'}, content='===== RULES OF USER =====\\nNever forget you are a Professor and I am a Searcher. Never flip roles! You will always instruct me.\\nWe share a common interest in collaborating to successfully complete a task.\\nI must help you to complete the task.\\nHere is the task: Assume now is 2024 in the Gregorian calendar, estimate the current age of University of Oxford and then add 10 more years to this age.. Never forget our task!\\nYou must instruct me based on my expertise and your needs to solve the task ONLY in the following two ways:\\n\\n1. Instruct with a necessary input:\\nInstruction: <YOUR_INSTRUCTION>\\nInput: <YOUR_INPUT>\\n\\n2. Instruct without any input:\\nInstruction: <YOUR_INSTRUCTION>\\nInput: None\\n\\nThe \"Instruction\" describes a task or question. The paired \"Input\" provides further context or information for the requested \"Instruction\".\\n\\nYou must give me one instruction at a time.\\nI must write a response that appropriately solves the requested instruction.\\nI must decline your instruction honestly if I cannot perform the instruction due to physical, moral, legal reasons or my capability and explain the reasons.\\nYou should instruct me not ask me questions.\\nNow you must start to instruct me using the two ways described above.\\nDo not add anything else other than your instruction and the optional corresponding input!\\nKeep giving me instructions and necessary inputs until you think the task is completed.\\nWhen the task is completed, you must only reply with a single word <CAMEL_TASK_DONE>.\\nNever say <CAMEL_TASK_DONE> unless my responses have solved your task.', video_bytes=None, image_list=None, image_detail='auto', video_detail='low')\n", "\n", "\u001b[33mOriginal task prompt:\n", "Assume now is 2024 in the Gregorian calendar, estimate the current age of University of Oxford and then add 10 more years to this age.\n", "\n", "\u001b[36mSpecified task prompt:\n", "None\n", "\n", "\u001b[31mFinal task prompt:\n", "Assume now is 2024 in the Gregorian calendar, estimate the current age of University of Oxford and then add 10 more years to this age.\n", "\n", "\u001b[34mAI User:\n", "\n", "Instruction: Estimate the founding year of the University of Oxford.  \n", "Input: None\n", "\n", "\n", "\u001b[32mAI Assistant:\n", "\n", "Function Execution: search_wiki\n", "\tArgs: {'entity': 'University of Oxford'}\n", "\tResult: The University of Oxford is a collegiate research university in Oxford, England. There is evidence of teaching as early as 1096, making it the oldest university in the English-speaking world and the world's second-oldest university in continuous operation. It grew rapidly from 1167, when <PERSON> banned English students from attending the University of Paris. After disputes between students and Oxford townsfolk, some Oxford academics fled northeast to Cambridge, where, in 1209, they established the University of Cambridge. The two English ancient universities share many common features and are jointly referred to as Oxbridge.\n", "\n", "Solution: The University of Oxford is believed to have evidence of teaching as early as 1096. This makes it the oldest university in the English-speaking world. To estimate its founding year, we can use 1096 as a reference point. \n", "\n", "Next, we will add 10 years to this estimated founding year to fulfill the task requirements.\n", "\n", "Next request.\n", "\n", "\n", "\u001b[34mAI User:\n", "\n", "Instruction: Calculate the current age of the University of Oxford in 2024 based on the estimated founding year of 1096.  \n", "Input: None\n", "\n", "\n", "\u001b[32mAI Assistant:\n", "\n", "Function Execution: sub\n", "\tArgs: {'a': 2024, 'b': 1096}\n", "\tResult: 928\n", "\n", "Solution: To calculate the current age of the University of Oxford in 2024 based on the estimated founding year of 1096, we subtract the founding year from the current year:\n", "\n", "2024 - 1096 = 928\n", "\n", "Thus, the current age of the University of Oxford in 2024 is 928 years.\n", "\n", "Next, we will add 10 more years to this age as per the task requirements.\n", "\n", "Next request.\n", "\n", "\n", "\u001b[34mAI User:\n", "\n", "Instruction: Add 10 years to the current age of the University of Oxford (928 years) to find the final age.  \n", "Input: None\n", "\n", "\n", "\u001b[32mAI Assistant:\n", "\n", "Function Execution: add\n", "\tArgs: {'a': 928, 'b': 10}\n", "\tResult: 938\n", "\n", "Solution: To find the final age of the University of Oxford after adding 10 years to its current age of 928 years, we perform the following calculation:\n", "\n", "928 + 10 = 938\n", "\n", "Therefore, the final age of the University of Oxford, after adding 10 years, is 938 years.\n", "\n", "Next request.\n", "\n", "\n", "\u001b[34mAI User:\n", "\n", "<CAMEL_TASK_DONE>\n", "\n", "\n"]}], "source": ["# Set a task\n", "task_prompt=(\"Assume now is 2024 in the Gregorian calendar, \"\n", "        \"estimate the current age of University of Oxford \"\n", "        \"and then add 10 more years to this age.\")\n", "\n", "# Set role playing\n", "role_play_session = RolePlaying(\n", "    assistant_role_name=\"Searcher\",\n", "    user_role_name=\"Professor\",\n", "    assistant_agent_kwargs=dict(\n", "        model=ModelFactory.create(\n", "            model_platform=ModelPlatformType.OPENAI,\n", "            model_type=ModelType.GPT_4O_MINI,\n", "        ),\n", "        tools=tools_list,\n", "    ),\n", "    user_agent_kwargs=dict(\n", "        model=ModelFactory.create(\n", "            model_platform=ModelPlatformType.OPENAI,\n", "            model_type=ModelType.GPT_4O_MINI,\n", "        ),\n", "    ),\n", "    task_prompt=task_prompt,\n", "    with_task_specify=False,\n", ")\n", "\n", "# Set the limit for the chat turn\n", "chat_turn_limit=10\n", "\n", "print(\n", "    Fore.GREEN\n", "    + f\"AI Assistant sys message:\\n{role_play_session.assistant_sys_msg}\\n\"\n", ")\n", "print(\n", "    Fore.BLUE + f\"AI User sys message:\\n{role_play_session.user_sys_msg}\\n\"\n", ")\n", "\n", "print(Fore.YELLOW + f\"Original task prompt:\\n{task_prompt}\\n\")\n", "print(\n", "    Fore.CYAN\n", "    + \"Specified task prompt:\"\n", "    + f\"\\n{role_play_session.specified_task_prompt}\\n\"\n", ")\n", "print(Fore.RED + f\"Final task prompt:\\n{role_play_session.task_prompt}\\n\")\n", "\n", "n = 0\n", "input_msg = role_play_session.init_chat()\n", "while n < chat_turn_limit:\n", "    n += 1\n", "    assistant_response, user_response = role_play_session.step(input_msg)\n", "\n", "    if assistant_response.terminated:\n", "        print(\n", "            Fore.GREEN\n", "            + (\n", "                \"AI Assistant terminated. Reason: \"\n", "                f\"{assistant_response.info['termination_reasons']}.\"\n", "            )\n", "        )\n", "        break\n", "    if user_response.terminated:\n", "        print(\n", "            Fore.GREEN\n", "            + (\n", "                \"AI User terminated. \"\n", "                f\"Reason: {user_response.info['termination_reasons']}.\"\n", "            )\n", "        )\n", "        break\n", "\n", "    # Print output from the user\n", "    print_text_animated(\n", "        Fore.BLUE + f\"AI User:\\n\\n{user_response.msg.content}\\n\"\n", "    )\n", "\n", "    if \"CAMEL_TASK_DONE\" in user_response.msg.content:\n", "        break\n", "\n", "    # Print output from the assistant, including any function\n", "    # execution information\n", "    print_text_animated(Fore.GREEN + \"AI Assistant:\")\n", "    tool_calls: list[FunctionCallingRecord] = assistant_response.info[\n", "        'tool_calls'\n", "    ]\n", "    for func_record in tool_calls:\n", "        print_text_animated(f\"{func_record}\")\n", "    print_text_animated(f\"{assistant_response.msg.content}\\n\")\n", "\n", "\n", "    input_msg = assistant_response.msg"]}, {"cell_type": "markdown", "metadata": {"id": "vtqMIJAn7Vbe"}, "source": ["## Conclusion\n", "We anticipate that the integration of custom tool usage within AI agents, as demonstrated through CAMEL, will continue to evolve and expand. This approach not only empowers agents to perform tasks beyond their native capabilities but also fosters collaboration in multi-agent systems. By standardizing the interface for tool usage, CAMEL simplifies the process of customizing and deploying tools across various AI applications, saving time and enhancing versatility. To fully utilize these capabilities, ensure your CAMEL-AI setup is up to date. Dive into the tutorial and start building more powerful AI agents today!"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}