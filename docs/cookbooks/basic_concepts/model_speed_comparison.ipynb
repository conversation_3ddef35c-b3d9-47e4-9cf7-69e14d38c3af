{"cells": [{"cell_type": "markdown", "metadata": {"id": "APw8wDolb0L9"}, "source": ["# Model Speed Comparison Cookbook"]}, {"cell_type": "markdown", "metadata": {"id": "vLCfmNtRb-jR"}, "source": ["You can also check this cookbook in colab [here](https://colab.research.google.com/drive/1oZfsgYfPPLVtMeA6DhsCd5jbdiAzEQSW?usp=sharing).\n", "\n", "<div class=\"align-center\">\n", "  <a href=\"https://www.camel-ai.org/\"><img src=\"https://i.postimg.cc/KzQ5rfBC/button.png\"width=\"150\"></a>\n", "  <a href=\"https://discord.camel-ai.org\"><img src=\"https://i.postimg.cc/L4wPdG9N/join-2.png\"  width=\"150\"></a></a>\n", "  \n", "⭐ <i>Star us on [*Github*](https://github.com/camel-ai/camel), join our [*Discord*](https://discord.camel-ai.org) or follow our [*X*](https://x.com/camelaiorg)\n", "</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This notebook demonstrates how to set up and leverage CAMEL's ability to explore the speed of different AI models by creating instances of `ChatAgent` and measuring their response times to user prompts.\n", "\n", "In this notebook, you'll explore:\n", "\n", "* **CAMEL**: A powerful multi-agent framework that enables Retrieval-Augmented Generation and multi-agent role-playing scenarios, allowing for sophisticated AI-driven tasks.\n", "\n", "* **ChatAgent()**: The class is a cornerstone of CAMEL. \n", "\n", "* **BaseMessage**: The base class for message objects used in the CAMEL chat system."]}, {"cell_type": "markdown", "metadata": {"id": "XuZwbB1IIuUi"}, "source": ["## 📦 Installation"]}, {"cell_type": "markdown", "metadata": {"id": "gIJK4Lu5Iwm0"}, "source": ["Ensure you have CAMEL AI installed in your Python environment:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "UtcC3c-KVZmU"}, "outputs": [], "source": ["!pip install \"camel-ai==0.2.16\""]}, {"cell_type": "markdown", "metadata": {"id": "MyTTCe3IR_Lr"}, "source": ["## 🔑 Setting Up API Keys"]}, {"cell_type": "markdown", "metadata": {"id": "REqzgGL9SEaD"}, "source": ["You'll need to set up your API keys for OpenAI and SambaNova."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "PNBFEXc-R-0s", "outputId": "1c9ad95d-50c5-4d8e-e83a-8ed24d44b2f7"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter your OpenAI API key: ··········\n", "Enter your SambaNova API key: ··········\n"]}], "source": ["import os\n", "from getpass import getpass\n", "\n", "# Prompt for the API key securely\n", "openai_api_key = getpass('Enter your OpenAI API key: ')\n", "os.environ[\"OPENAI_API_KEY\"] = openai_api_key\n", "\n", "sambanova_api_key = getpass('Enter your SambaNova API key: ')\n", "os.environ[\"SAMBA_API_KEY\"] = sambanova_api_key"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Alternatively, if running on Colab, you could save your API keys and tokens as **Colab Secrets**,\n", "\n", "and use them across notebooks.\n", "\n", "To do so, **comment out** the above **manual** API key prompt code block(s),\n", "\n", "and **uncomment** the following codeblock.\n", "\n", "⚠️ Don't forget granting access to the API key you would be using to the current notebook."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# from google.colab import userdata\n", "\n", "# os.environ[\"OPENAI_API_KEY\"] = userdata.get(\"OPENAI_API_KEY\")\n", "# os.environ[\"SAMBA_API_KEY\"] = userdata.get(\"SAMBA_API_KEY\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 620}, "id": "ZT2tYqYh6EDj", "outputId": "93440028-f9a0-47e0-aafb-1055601f37ce"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.10/dist-packages/camel/models/openai_model.py:107: UserWarning: Warning: You are using an O1 model (O1_MINI or O1_PREVIEW), which has certain limitations, reference: `https://platform.openai.com/docs/guides/reasoning`.\n", "  warnings.warn(\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import time\n", "import matplotlib.pyplot as plt\n", "from camel.agents import ChatAgent\n", "from camel.configs import SambaCloudAPIConfig, ChatGPTConfig\n", "from camel.messages import BaseMessage\n", "from camel.models import ModelFactory\n", "from camel.types import ModelPlatformType, ModelType\n", "\n", "# Create model instances\n", "def create_models():\n", "    model_configs = [\n", "        (ModelPlatformType.OPENAI, ModelType.GPT_4O_MINI, ChatGPTConfig(temperature=0.0, max_tokens=2000), \"OpenAI GPT-4O Mini\"),\n", "        (ModelPlatformType.OPENAI, ModelType.GPT_4O, ChatGPTConfig(temperature=0.0, max_tokens=2000), \"OpenAI GPT-4O\"),\n", "        # NOTE: OpenAI O1 model requires additional additional credentials\n", "        (ModelPlatformType.OPENAI, ModelType.O1_PREVIEW, ChatGPTConfig(temperature=0.0), \"OpenAI O1 Preview\"),\n", "        (ModelPlatformType.SAMBA, \"Meta-Llama-3.1-8B-Instruct\", SambaCloudAPIConfig(temperature=0.0, max_tokens=2000), \"SambaNova Llama 8B\"),\n", "        (ModelPlatformType.SAMBA, \"Meta-Llama-3.1-70B-Instruct\", SambaCloudAPIConfig(temperature=0.0, max_tokens=2000), \"SambaNova Llama 70B\"),\n", "        (ModelPlatformType.SAMBA, \"Meta-Llama-3.1-405B-Instruct\", SambaCloudAPIConfig(temperature=0.0, max_tokens=2000), \"SambaNova Llama 405B\")\n", "    ]\n", "\n", "    models = [(ModelFactory.create(model_platform=platform, model_type=model_type, model_config_dict=config.as_dict(), url=\"https://api.sambanova.ai/v1\" if platform == ModelPlatformType.SAMBA else None), name)\n", "              for platform, model_type, config, name in model_configs]\n", "    return models\n", "\n", "# Define messages\n", "def create_messages():\n", "    sys_msg = BaseMessage.make_assistant_message(role_name=\"Assistant\", content=\"You are a helpful assistant.\")\n", "    user_msg = BaseMessage.make_user_message(role_name=\"User\", content=\"Tell me a long story.\")\n", "    return sys_msg, user_msg\n", "\n", "# Initialize ChatAgent instances\n", "def initialize_agents(models, sys_msg):\n", "    return [(ChatAgent(system_message=sys_msg, model=model), name) for model, name in models]\n", "\n", "# Measure response time for a given agent\n", "def measure_response_time(agent, message):\n", "    start_time = time.time()\n", "    response = agent.step(message)\n", "    end_time = time.time()\n", "    tokens_per_second = response.info['usage'][\"completion_tokens\"] / (end_time - start_time)\n", "    return tokens_per_second\n", "\n", "# Visualize results\n", "def plot_results(model_names, tokens_per_sec):\n", "    plt.figure(figsize=(10, 6))\n", "    plt.barh(model_names, tokens_per_sec, color='skyblue')\n", "    plt.xlabel(\"Tokens per Second\")\n", "    plt.title(\"Model Speed Comparison: Tokens per Second\")\n", "    plt.gca().invert_yaxis()\n", "    plt.show()\n", "\n", "# Main execution\n", "models = create_models()\n", "sys_msg, user_msg = create_messages()\n", "agents = initialize_agents(models, sys_msg)\n", "\n", "# Measure response times and collect data\n", "model_names = []\n", "tokens_per_sec = []\n", "\n", "for agent, model_name in agents:\n", "    model_names.append(model_name)\n", "    tokens_per_sec.append(measure_response_time(agent, user_msg))\n", "\n", "# Visualize the results\n", "plot_results(model_names, tokens_per_sec)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["That's everything: Got questions about 🐫 CAMEL-AI? Join us on [Discord](https://discord.camel-ai.org)! Whether you want to share feedback, explore the latest in multi-agent systems, get support, or connect with others on exciting projects, we’d love to have you in the community! 🤝\n", "\n", "Check out some of our other work:\n", "\n", "1. 🐫 Creating Your First CAMEL Agent [free Colab](https://docs.camel-ai.org/cookbooks/create_your_first_agent.html)\n", "\n", "2.  Graph RAG Cookbook [free Colab](https://colab.research.google.com/drive/1uZKQSuu0qW6ukkuSv9TukLB9bVaS1H0U?usp=sharing)\n", "\n", "3. 🧑‍⚖️ Create A Hackathon Judge Committee with Workforce [free Colab](https://colab.research.google.com/drive/*********************************?usp=sharing)\n", "\n", "4. 🔥 3 ways to ingest data from websites with Firecrawl & CAMEL [free Colab](https://colab.research.google.com/drive/1lOmM3VmgR1hLwDKdeLGFve_75RFW0R9I?usp=sharing)\n", "\n", "5. 🦥 Agentic SFT Data Generation with CAMEL and Mistral Models, Fine-Tuned with Unsloth [free Colab](https://colab.research.google.com/drive/1lYgArBw7ARVPSpdwgKLYnp_NEXiNDOd-?usp=sharingg)\n", "\n", "Thanks from everyone at 🐫 CAMEL-AI\n", "\n", "\n", "<div class=\"align-center\">\n", "  <a href=\"https://www.camel-ai.org/\"><img src=\"https://i.postimg.cc/KzQ5rfBC/button.png\"width=\"150\"></a>\n", "  <a href=\"https://discord.camel-ai.org\"><img src=\"https://i.postimg.cc/L4wPdG9N/join-2.png\"  width=\"150\"></a></a>\n", "  \n", "⭐ <i>Star us on [*Github*](https://github.com/camel-ai/camel), join our [*Discord*](https://discord.camel-ai.org) or follow our [*X*](https://x.com/camelaiorg)\n", "</div>\n"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}