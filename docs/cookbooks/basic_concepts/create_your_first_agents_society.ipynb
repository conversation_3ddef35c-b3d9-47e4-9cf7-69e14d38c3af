{"cells": [{"cell_type": "markdown", "metadata": {"id": "_nTMiZtBfNJs"}, "source": ["# Creating Your First Agent Society"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can also check this cookbook in colab [here](https://colab.research.google.com/drive/1cmWPxXEsyMbmjPhD2bWfHuhd_Uz6FaJQ?usp=sharing).\n", "\n", "<div class=\"align-center\">\n", "  <a href=\"https://www.camel-ai.org/\"><img src=\"https://i.postimg.cc/KzQ5rfBC/button.png\"width=\"150\"></a>\n", "  <a href=\"https://discord.camel-ai.org\"><img src=\"https://i.postimg.cc/L4wPdG9N/join-2.png\"  width=\"150\"></a></a>\n", "  \n", "⭐ <i>Star us on [*Github*](https://github.com/camel-ai/camel), join our [*Discord*](https://discord.camel-ai.org) or follow our [*X*](https://x.com/camelaiorg)\n", "</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This notebook demonstrates how to set up and leverage CAMEL's ability to create your first agent society through `RolePlaying()`class.\n", "\n", "In this notebook, you'll explore:\n", "\n", "* **CAMEL**: A powerful multi-agent framework that enables Retrieval-Augmented Generation and multi-agent role-playing scenarios, allowing for sophisticated AI-driven tasks.\n", "\n", "* **Agent Society**: Enabling multi-agent communication for the task solving."]}, {"cell_type": "markdown", "metadata": {"id": "wFlfaGrjVAUy"}, "source": ["## Philosophical Bits\n", "\n", "\n", "\n", "> *What magical trick makes us intelligent? The trick is that there is no trick. The power of intelligence stems from our vast diversity, not from any single, perfect principle.*\n", ">\n", "> -- <PERSON>, The Society of Mind, p. 308\n", "\n", "In this section, we will take a spite of the task-oriented `RolePlaying()` class. We design this in an instruction-following manner. The essence is that to solve a complex task, you can enable two communicative agents collaboratively working together step by step to reach solutions. The main concepts include:\n", "\n", "- **Task**: a task can be as simple as an idea, initialized by an inception prompt.\n", "\n", "- **AI User**: the agent who is expected to provide instructions.\n", "\n", "- **AI Assistant**: the agent who is expected to respond with solutions that fulfills the instructions."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📦 Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install \"camel-ai==0.2.16\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔑 Setting Up API Keys"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You'll need to set up your API keys for OpenAI."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "PNBFEXc-R-0s", "outputId": "421b4396-dd5f-49e6-fe7f-2b6d404e1990"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter your API key: ··········\n"]}], "source": ["import os\n", "from getpass import getpass\n", "\n", "# Prompt for the API key securely\n", "openai_api_key = getpass('Enter your API key: ')\n", "os.environ[\"OPENAI_API_KEY\"] = openai_api_key"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Alternatively, if running on Colab, you could save your API keys and tokens as **Colab Secrets**,\n", "\n", "and use them across notebooks.\n", "\n", "\n", "To do so, **comment out** the above **manual** API key prompt code block(s),\n", "\n", "and **uncomment** the following codeblock.\n", "\n", "\n", "⚠️ Don't forget granting access to the API key you would be using to the current notebook."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# from google.colab import userdata\n", "\n", "# os.environ[\"OPENAI_API_KEY\"] = userdata.get(\"OPENAI_API_KEY\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Quick Start\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "CmgKGeCxVON-"}, "outputs": [], "source": ["# Import necessary classes\n", "from camel.societies import RolePlaying\n", "from camel.types import TaskType, ModelType, ModelPlatformType\n", "from camel.configs import ChatGPTConfig\n", "from camel.models import ModelFactory\n", "\n", "model = ModelFactory.create(\n", "    model_platform=ModelPlatformType.OPENAI,\n", "    model_type=ModelType.GPT_4O_MINI,\n", "    model_config_dict=ChatGPTConfig(temperature=0.0).as_dict(), # [Optional] the config for model\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "Lef5LTRdVSsm"}, "source": ["### 🕹 Step 1: Configure the Role-Playing Session\n"]}, {"cell_type": "markdown", "metadata": {"id": "WXibf_JkYdn0"}, "source": ["#### Set the `Task` Arguments"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "aX9wCqeTVXDh"}, "outputs": [], "source": ["task_kwargs = {\n", "    'task_prompt': 'Develop a plan to TRAVEL TO THE PAST and make changes.',\n", "    'with_task_specify': True,\n", "    'task_specify_agent_kwargs': {'model': model}\n", "}"]}, {"cell_type": "markdown", "metadata": {"id": "tXx13MmbYgFh"}, "source": ["#### Set the `User` Arguments\n", "You may think the user as the `instruction sender`."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "be-97ApoVdDf"}, "outputs": [], "source": ["user_role_kwargs = {\n", "    'user_role_name': 'an ambitious aspiring TIME TRAVELER',\n", "    'user_agent_kwargs': {'model': model}\n", "}"]}, {"cell_type": "markdown", "metadata": {"id": "zLJzupaMYm11"}, "source": ["#### Set the `Assistant` Arguments\n", "Again, you may think the assistant as the `instruction executor`."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "rfzyC54hYtVu"}, "outputs": [], "source": ["assistant_role_kwargs = {\n", "    'assistant_role_name': 'the best-ever experimental physicist',\n", "    'assistant_agent_kwargs': {'model': model}\n", "}"]}, {"cell_type": "markdown", "metadata": {"id": "j90-Y5ChV-iX"}, "source": ["### Step 2: Kickstart Your Society\n", "Putting them altogether – your role-playing session is ready to go!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "yDiLpZ7CY1N_"}, "outputs": [], "source": ["society = RolePlaying(\n", "    **task_kwargs,             # The task arguments\n", "    **user_role_kwargs,        # The instruction sender's arguments\n", "    **assistant_role_kwargs,   # The instruction receiver's arguments\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "kklmZh0iY6pY"}, "source": ["### Step 3: Solving Tasks with Your Society\n", "Hold your bytes. Prior to our travel, let's define a small helper function."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "VI6d62uVY6IZ"}, "outputs": [], "source": ["def is_terminated(response):\n", "    \"\"\"\n", "    Give alerts when the session should be terminated.\n", "    \"\"\"\n", "    if response.terminated:\n", "        role = response.msg.role_type.name\n", "        reason = response.info['termination_reasons']\n", "        print(f'AI {role} terminated due to {reason}')\n", "\n", "    return response.terminated"]}, {"cell_type": "markdown", "metadata": {"id": "tTUlFZjVZCsz"}, "source": ["Time to chart our course – writing a simple loop for our society to proceed:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Bq8AiGEXZC9P"}, "outputs": [], "source": ["def run(society, round_limit: int=10):\n", "\n", "    # Get the initial message from the ai assistant to the ai user\n", "    input_msg = society.init_chat()\n", "\n", "    # Starting the interactive session\n", "    for _ in range(round_limit):\n", "\n", "        # Get the both responses for this round\n", "        assistant_response, user_response = society.step(input_msg)\n", "\n", "        # Check the termination condition\n", "        if is_terminated(assistant_response) or is_terminated(user_response):\n", "            break\n", "\n", "        # Get the results\n", "        print(f'[AI User] {user_response.msg.content}.\\n')\n", "        # Check if the task is end\n", "        if 'CAMEL_TASK_DONE' in user_response.msg.content:\n", "            break\n", "        print(f'[AI Assistant] {assistant_response.msg.content}.\\n')\n", "\n", "\n", "\n", "        # Get the input message for the next round\n", "        input_msg = assistant_response.msg\n", "\n", "    return None"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "DoJ2ZaWyZE57", "outputId": "55d8d45b-2a97-44b9-c819-30af254cb426"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[AI User] Instruction: Research the principles of quantum entanglement and wormhole theory to understand their potential applications in time travel.  \n", "Input: None.\n", "\n", "[AI Assistant] Solution: To understand the potential applications of quantum entanglement and wormhole theory in time travel, we need to delve into the fundamental principles of each concept.\n", "\n", "1. **Quantum Entanglement**:\n", "   - **Definition**: Quantum entanglement is a phenomenon where two or more particles become interconnected in such a way that the state of one particle instantly influences the state of another, regardless of the distance separating them.\n", "   - **Key Principles**:\n", "     - **Non-locality**: Changes to one entangled particle affect its partner instantaneously, suggesting a form of communication that transcends space.\n", "     - **Superposition**: Particles exist in multiple states until measured, which could theoretically allow for multiple timelines or outcomes.\n", "   - **Potential Applications in Time Travel**:\n", "     - **Information Transfer**: If we could manipulate entangled particles, we might send information back in time without physically traveling, potentially influencing past events subtly.\n", "     - **Quantum Teleportation**: This process could allow for the transfer of quantum states across time, which might be a stepping stone toward more complex time travel mechanisms.\n", "\n", "2. **Wormhole Theory**:\n", "   - **Definition**: A wormhole is a hypothetical tunnel-like structure in spacetime that connects two separate points in space and time.\n", "   - **Key Principles**:\n", "     - **<PERSON><PERSON><PERSON>**: This concept suggests that wormholes could connect distant parts of the universe or different times.\n", "     - **Stability and Exotic Matter**: For a wormhole to be traversable, it would require \"exotic matter\" with negative energy density to keep it open, which has not yet been discovered.\n", "   - **Potential Applications in Time Travel**:\n", "     - **Time Dilation**: By traveling through a wormhole, one could theoretically experience time differently, allowing for travel to the past or future.\n", "     - **Creating Stable Wormholes**: If we can find a way to stabilize wormholes, they could serve as shortcuts through spacetime, enabling time travel.\n", "\n", "3. **Combining Quantum Entanglement and Wormhole Theory**:\n", "   - **Quantum Wormholes**: Some theories suggest that entangled particles could be connected through microscopic wormholes, allowing for instantaneous communication across time.\n", "   - **Temporal Influence**: By using quantum entanglement to send information through a wormhole, we could influence key historical events without creating paradoxes, as the information could be framed as a suggestion rather than a direct alteration.\n", "\n", "In summary, both quantum entanglement and wormhole theory offer intriguing possibilities for time travel. The key lies in understanding how to manipulate these phenomena without causing significant disruptions to the timeline.\n", "\n", "Next request..\n", "\n", "[AI User] Instruction: Identify a pivotal historical event suitable for our time travel mission and explain why it is significant.  \n", "Input: None.\n", "\n", "[AI Assistant] Solution: A pivotal historical event suitable for our time travel mission is the signing of the Declaration of Independence on July 4, 1776. This event is significant for several reasons:\n", "\n", "1. **Foundational Document**: The Declaration of Independence is a foundational document for the United States, articulating the colonies' reasons for seeking independence from British rule. It established the principles of individual rights and government by consent, which have influenced democratic movements worldwide.\n", "\n", "2. **Catalyst for Change**: The signing marked the beginning of a revolutionary movement that led to the establishment of a new nation. It inspired other countries and colonies to pursue their own independence and democratic governance, making it a crucial moment in the history of democracy.\n", "\n", "3. **Influence on Human Rights**: The Declaration's assertion that \"all men are created equal\" and are endowed with certain unalienable rights has had a lasting impact on human rights discourse. It laid the groundwork for future civil rights movements and the global fight for equality.\n", "\n", "4. **Global Impact**: The ideas expressed in the Declaration resonated beyond the United States, influencing revolutions in France, Latin America, and other regions. Its principles have been referenced in various international documents, including the Universal Declaration of Human Rights.\n", "\n", "5. **Potential for Subtle Influence**: By subtly influencing key figures involved in the drafting or signing of the Declaration, we could reinforce the commitment to certain ideals, such as equality and justice, ensuring that these principles are more robustly integrated into the foundation of the new nation.\n", "\n", "In summary, the signing of the Declaration of Independence is a significant event that not only shaped the course of American history but also had profound implications for the development of democratic ideals and human rights globally. This makes it an ideal target for our time travel mission, as influencing this event could lead to a more equitable and just society.\n", "\n", "Next request..\n", "\n", "[AI User] Instruction: Develop a strategy for subtly influencing key figures during the signing of the Declaration of Independence without causing significant alterations to the timeline.  \n", "Input: None.\n", "\n", "[AI Assistant] Solution: To subtly influence key figures during the signing of the Declaration of Independence without causing significant alterations to the timeline, we can develop a multi-faceted strategy that focuses on persuasion, information sharing, and fostering collaboration among the signers. Here’s a detailed plan:\n", "\n", "1. **Identify Key Figures**:\n", "   - Focus on influential signers such as <PERSON> (the principal author), <PERSON>, <PERSON>, and <PERSON>. Their thoughts and actions will significantly impact the document's content and the overall sentiment of the Congress.\n", "\n", "2. **Establish a Discrete Presence**:\n", "   - Utilize the time machine to arrive a few days before the signing. This allows for a low-profile presence in Philadelphia, where the Continental Congress is convening. Disguise as a local merchant or a visitor to blend in with the crowd.\n", "\n", "3. **Engage in Informal Conversations**:\n", "   - Approach key figures in casual settings, such as taverns or public gatherings. Use these interactions to discuss the importance of certain ideals, such as equality, justice, and the rights of individuals. \n", "   - For example, while conversing with <PERSON>, emphasize the significance of including a strong statement on human rights, which could lead him to refine his language in the draft.\n", "\n", "4. **Disseminate Thought-Provoking Literature**:\n", "   - Leave behind pamphlets or books that discuss Enlightenment ideas, particularly those of <PERSON> and <PERSON><PERSON><PERSON><PERSON>, which advocate for natural rights and the separation of powers. This can inspire the signers to incorporate these concepts into the Declaration.\n", "   - Ensure that these materials are presented as popular literature of the time, making them seem like a natural part of the intellectual discourse.\n", "\n", "5. **Foster Collaboration Among Signers**:\n", "   - Encourage discussions among the signers about the importance of unity and the collective vision for the new nation. This can be done by organizing informal gatherings where they can share their thoughts and concerns.\n", "   - Highlight the need for a united front against British rule, which can help solidify their resolve and commitment to the Declaration.\n", "\n", "6. **Subtle Suggestions**:\n", "   - During the drafting process, make subtle suggestions that can be framed as collective ideas. For instance, if a signer expresses doubt about a particular phrase, gently propose an alternative that emphasizes equality or justice without directly imposing your views.\n", "   - Use phrases like, \"Have you considered how this might resonate with the common people?\" to guide their thinking without overtly directing it.\n", "\n", "7. **Monitor Reactions**:\n", "   - Pay attention to the reactions of the signers to gauge the effectiveness of your influence. If certain ideas resonate well, continue to reinforce those themes in subsequent conversations.\n", "\n", "8. **Exit Strategy**:\n", "   - Once the signing is complete, discreetly exit the scene to avoid drawing attention to your presence. Ensure that your influence remains untraceable, allowing history to unfold as it should.\n", "\n", "By employing this strategy, we can subtly influence the key figures involved in the signing of the Declaration of Independence, ensuring that the core ideals of equality and justice are more robustly integrated into the document while avoiding significant alterations to the timeline.\n", "\n", "Next request..\n", "\n", "[AI User] Instruction: Outline potential paradoxes or risks associated with our time travel mission and propose solutions to mitigate them.  \n", "Input: None.\n", "\n", "[AI Assistant] Solution: Engaging in a time travel mission to influence the signing of the Declaration of Independence presents several potential paradoxes and risks. Here’s an outline of these issues along with proposed solutions to mitigate them:\n", "\n", "1. **The Grandfather Paradox**:\n", "   - **Description**: This paradox arises when a time traveler inadvertently prevents their own existence by altering a significant event in the past. For example, if our actions lead to a drastically different Declaration, it could change the course of history, potentially erasing our own timeline.\n", "   - **Mitigation**: Focus on subtle influences rather than direct alterations. Ensure that any changes made do not fundamentally alter the outcome of the signing or the existence of key figures. For instance, reinforcing existing ideas rather than introducing entirely new concepts can help maintain the timeline's integrity.\n", "\n", "2. **Causal Loop**:\n", "   - **Description**: A causal loop occurs when an event is both the cause and the effect of itself. For example, if we influence a signer to include a specific phrase that we later cite as the reason for our time travel, it creates a loop with no clear origin.\n", "   - **Mitigation**: Avoid introducing ideas or phrases that do not already exist in the historical context. Instead, enhance discussions around existing concepts, ensuring that any influence feels organic and part of the natural discourse of the time.\n", "\n", "3. **<PERSON><PERSON><PERSON>back**:\n", "   - **Description**: Changes made in the past could lead to unforeseen consequences in the future, creating a ripple effect that alters history in unpredictable ways.\n", "   - **Mitigation**: Conduct thorough research on the historical context and potential outcomes of our actions. Limit our influence to reinforcing established ideas rather than introducing new ones. Additionally, maintain a low profile to minimize our impact on the broader historical narrative.\n", "\n", "4. **Altered Perceptions**:\n", "   - **Description**: Our presence and influence could lead key figures to question their own beliefs or motivations, potentially causing them to act differently than they would have without our intervention.\n", "   - **Mitigation**: Engage in conversations that encourage reflection without leading to doubt. Use language that aligns with their existing beliefs and values, ensuring that our influence feels like a natural extension of their thoughts rather than a challenge to their convictions.\n", "\n", "5. **Historical Integrity**:\n", "   - **Description**: The risk of creating a timeline that diverges significantly from the established history could lead to a loss of historical integrity, affecting future events and societal development.\n", "   - **Mitigation**: Establish clear objectives for our influence, focusing on enhancing the core principles of the Declaration without altering its fundamental purpose. Document our observations to ensure we can track any changes and assess their impact on the timeline.\n", "\n", "6. **Moral and Ethical Considerations**:\n", "   - **Description**: The ethical implications of influencing historical events raise questions about the morality of altering the course of history for perceived betterment.\n", "   - **Mitigation**: Clearly define our ethical framework before the mission. Ensure that our actions align with principles of justice and equality, and consider the long-term implications of our influence on future generations.\n", "\n", "By identifying these potential paradoxes and risks, we can develop strategies to mitigate them effectively, ensuring that our time travel mission remains focused and responsible while achieving our desired outcomes.\n", "\n", "Next request..\n", "\n", "[AI User] Instruction: Create a timeline for our time travel mission, detailing key phases and actions to be taken before, during, and after the signing of the Declaration of Independence.  \n", "Input: None.\n", "\n", "[AI Assistant] Solution: Here’s a detailed timeline for our time travel mission, outlining key phases and actions to be taken before, during, and after the signing of the Declaration of Independence:\n", "\n", "### Timeline for Time Travel Mission\n", "\n", "#### Phase 1: Pre-Mission Preparation (1 Month Before Departure)\n", "- **Research Historical Context**: \n", "  - Study the political climate of 1776, key figures involved, and the content of the Declaration of Independence.\n", "  - Identify existing debates and sentiments among the Continental Congress members regarding independence.\n", "\n", "- **Develop Influence Strategy**: \n", "  - Outline specific ideas and themes to reinforce during the mission, focusing on equality, justice, and unity.\n", "  - Prepare materials (pamphlets, books) that align with Enlightenment ideas to disseminate subtly.\n", "\n", "- **Time Machine Calibration**: \n", "  - Ensure the time machine is calibrated for a precise arrival in Philadelphia on July 1, 1776, allowing for a few days of preparation before the signing.\n", "\n", "#### Phase 2: Arrival and Initial Engagement (July 1-3, 1776)\n", "- **Arrival in Philadelphia**: \n", "  - Disguise as a local merchant or visitor to blend in with the crowd.\n", "  - Familiarize yourself with the local environment and key locations where signers gather.\n", "\n", "- **Informal Conversations**: \n", "  - Engage in discussions with influential figures (e.g., <PERSON>, <PERSON>, <PERSON>) in taverns or public spaces.\n", "  - Introduce ideas about the importance of individual rights and the need for a strong statement on equality.\n", "\n", "- **Disseminate Literature**: \n", "  - Leave pamphlets or books discussing Enlightenment principles in places where key figures are likely to find them.\n", "  - Ensure these materials appear as part of the contemporary intellectual discourse.\n", "\n", "#### Phase 3: Building Momentum (July 4, 1776 - Day of Signing)\n", "- **Foster Collaboration**: \n", "  - Organize informal gatherings among signers to discuss the Declaration and the importance of unity against British rule.\n", "  - Encourage open dialogue about the content of the Declaration, subtly guiding discussions toward key themes.\n", "\n", "- **Subtle Suggestions**: \n", "  - During the drafting process, make discrete suggestions to key figures about phrasing or ideas that emphasize equality and justice.\n", "  - Frame these suggestions as collective thoughts to ensure they feel organic.\n", "\n", "- **Monitor Reactions**: \n", "  - Pay attention to the responses of the signers to gauge the effectiveness of your influence.\n", "  - Adjust your approach based on their receptiveness to certain ideas.\n", "\n", "#### Phase 4: The Signing Ceremony (July 4, 1776)\n", "- **Attend the Signing**: \n", "  - Be present at the signing ceremony, maintaining a low profile.\n", "  - Observe the interactions and discussions among the signers as they finalize the document.\n", "\n", "- **Final Encouragement**: \n", "  - If appropriate, offer a final word of encouragement to key figures about the importance of the Declaration and its potential impact on future generations.\n", "\n", "#### Phase 5: Post-Signing Reflection (July 5-7, 1776)\n", "- **Document Observations**: \n", "  - Record your observations and any changes in the discussions or content of the Declaration that resulted from your influence.\n", "  - Note the reactions of the signers and the overall atmosphere during the signing.\n", "\n", "- **Exit Strategy**: \n", "  - Discreetly exit Philadelphia to avoid drawing attention to your presence.\n", "  - Ensure that your departure does not disrupt the timeline or raise suspicions.\n", "\n", "#### Phase 6: Return and Analysis (Post-Mission)\n", "- **Return to Present**: \n", "  - Use the time machine to return to the present day.\n", "  \n", "- **Analyze Impact**: \n", "  - Compare the historical record of the Declaration of Independence with your observations to assess the impact of your influence.\n", "  - Evaluate any changes in the timeline and their implications for future events.\n", "\n", "- **Document Findings**: \n", "  - Compile a comprehensive report detailing the mission, the strategies employed, and the outcomes observed.\n", "  - Reflect on the ethical considerations and lessons learned from the experience.\n", "\n", "By following this timeline, we can effectively execute our time travel mission, ensuring that our influence is subtle and aligned with the historical context while achieving our desired outcomes.\n", "\n", "Next request..\n", "\n", "[AI User] Instruction: Prepare a contingency plan in case unexpected events or challenges arise during the time travel mission.  \n", "Input: None.\n", "\n", "[AI Assistant] Solution: Preparing a contingency plan is essential to address unexpected events or challenges that may arise during our time travel mission. Here’s a detailed plan outlining potential scenarios and corresponding responses:\n", "\n", "### Contingency Plan for Time Travel Mission\n", "\n", "#### Scenario 1: Encountering Unforeseen Historical Figures\n", "- **Challenge**: Unexpected interactions with influential figures not originally planned for, such as loyalists or British officials, could complicate our mission.\n", "- **Response**:\n", "  - Maintain a low profile and avoid direct engagement with these figures unless necessary.\n", "  - If approached, use general conversation to gauge their views without revealing our intentions.\n", "  - If a confrontation arises, pivot the conversation to neutral topics or common interests to defuse tension.\n", "\n", "#### Sc<PERSON><PERSON> 2: Changes in the Timeline\n", "- **Challenge**: Our subtle influences may lead to unexpected changes in the timeline, such as key figures altering their positions or decisions.\n", "- **Response**:\n", "  - Monitor the reactions and discussions of the signers closely. If significant deviations occur, reassess our approach and adjust our influence accordingly.\n", "  - If necessary, consider a temporary withdrawal from direct influence to allow the original timeline to stabilize before re-engaging.\n", "\n", "#### Scenario 3: Discovery of Our True Identity\n", "- **Challenge**: If any historical figures become suspicious of our identity or intentions, it could jeopardize the mission.\n", "- **Response**:\n", "  - Prepare a plausible backstory that aligns with the historical context (e.g., a traveling merchant or a supporter of independence).\n", "  - If confronted, calmly assert your identity and redirect the conversation to shared interests or current events to regain trust.\n", "\n", "#### Scenario 4: Communication Breakdown\n", "- **Challenge**: Difficulty in conveying ideas or suggestions due to language barriers or misunderstandings of the historical context.\n", "- **Response**:\n", "  - Use simple, clear language and avoid anachronistic terms or concepts that may confuse the signers.\n", "  - If misunderstandings occur, clarify your points by relating them to widely accepted ideas of the time, such as references to common grievances against British rule.\n", "\n", "#### <PERSON><PERSON><PERSON> 5: Unforeseen Events (e.g., <PERSON><PERSON><PERSON>, <PERSON>)\n", "- **Challenge**: Illness or adverse weather conditions could hinder our ability to engage with key figures or attend the signing.\n", "- **Response**:\n", "  - Have a backup plan for remote engagement, such as leaving notes or pamphlets in strategic locations where key figures are likely to find them.\n", "  - If illness strikes, prioritize recovery and use the time to observe and document the environment and interactions from a distance.\n", "\n", "#### <PERSON><PERSON><PERSON> 6: Ethical Dilemmas\n", "- **Challenge**: Encountering situations that raise ethical concerns about the influence we are exerting on historical figures.\n", "- **Response**:\n", "  - Establish a clear ethical framework before the mission, focusing on principles of justice and equality.\n", "  - If faced with a dilemma, pause to reassess the potential consequences of our actions and prioritize the greater good over personal objectives.\n", "\n", "#### <PERSON><PERSON><PERSON> 7: Technical Malfunction of the Time Machine\n", "- **Challenge**: A malfunction could prevent timely return or lead to unintended time jumps.\n", "- **Response**:\n", "  - Ensure that the time machine is equipped with a manual override and emergency return function.\n", "  - If a malfunction occurs, attempt to troubleshoot the issue using available resources. If unsuccessful, activate the emergency return function to return to the present.\n", "\n", "### Conclusion\n", "By preparing for these potential challenges with a comprehensive contingency plan, we can enhance our readiness and adaptability during the time travel mission. This proactive approach will help ensure that we can navigate unexpected events while maintaining the integrity of our objectives.\n", "\n", "Next request..\n", "\n", "[AI User] Instruction: Finalize the mission by summarizing the key objectives and desired outcomes of our time travel project.  \n", "Input: None.\n", "\n", "[AI Assistant] Solution: Here’s a summary of the key objectives and desired outcomes of our time travel project focused on influencing the signing of the Declaration of Independence:\n", "\n", "### Key Objectives\n", "\n", "1. **Subtle Influence on Key Figures**:\n", "   - Engage with influential signers such as <PERSON>, <PERSON>, and <PERSON> to reinforce the principles of equality, justice, and individual rights.\n", "   - Encourage discussions that lead to a stronger articulation of these ideals in the Declaration.\n", "\n", "2. **Preservation of Historical Integrity**:\n", "   - Ensure that any influence exerted does not drastically alter the timeline or the fundamental purpose of the Declaration.\n", "   - Focus on enhancing existing ideas rather than introducing new concepts that could lead to significant changes in history.\n", "\n", "3. **Fostering Unity Among Signers**:\n", "   - Promote collaboration and a sense of shared purpose among the signers to strengthen their resolve against British rule.\n", "   - Create an environment where open dialogue can flourish, allowing for the natural evolution of ideas.\n", "\n", "4. **Documentation of Observations**:\n", "   - Record detailed observations of interactions, discussions, and the overall atmosphere during the signing process.\n", "   - Analyze the impact of our influence on the final content of the Declaration and the reactions of the signers.\n", "\n", "### Desired Outcomes\n", "\n", "1. **Enhanced Declaration of Independence**:\n", "   - Achieve a Declaration that more robustly articulates the principles of equality and justice, potentially leading to a stronger foundation for the United States and its democratic ideals.\n", "\n", "2. **Positive Ripple Effects**:\n", "   - Influence the course of history in a way that promotes the spread of democratic ideals and human rights, inspiring future movements for equality and justice both in the United States and globally.\n", "\n", "3. **Successful Mission Execution**:\n", "   - Complete the mission without drawing attention to our presence or causing significant disruptions to the timeline.\n", "   - Return to the present with valuable insights and a comprehensive report on the mission's outcomes.\n", "\n", "4. **Ethical Reflection**:\n", "   - Engage in a thoughtful reflection on the ethical implications of our actions and the responsibilities that come with influencing historical events.\n", "\n", "By focusing on these key objectives and desired outcomes, we can ensure that our time travel project is both impactful and responsible, contributing positively to the historical narrative while maintaining the integrity of the timeline.\n", "\n", "Next request..\n", "\n", "[AI User] Instruction: Prepare a final checklist to ensure all aspects of the time travel mission are ready before departure.  \n", "Input: None.\n", "\n", "[AI Assistant] Solution: Here’s a comprehensive final checklist to ensure all aspects of the time travel mission are ready before departure:\n", "\n", "### Final Checklist for Time Travel Mission\n", "\n", "#### Pre-Departure Preparations\n", "1. **Research and Documentation**:\n", "   - [ ] Complete research on the historical context of the Declaration of Independence.\n", "   - [ ] Identify key figures involved in the signing and their positions.\n", "   - [ ] Gather relevant literature on Enlightenment ideas to disseminate.\n", "\n", "2. **Influence Strategy**:\n", "   - [ ] Finalize the specific themes and ideas to reinforce during the mission.\n", "   - [ ] Prepare a list of subtle suggestions to offer during discussions.\n", "\n", "3. **Time Machine Readiness**:\n", "   - [ ] Ensure the time machine is fully operational and calibrated for July 1, 1776.\n", "   - [ ] Test the emergency return function and manual override.\n", "   - [ ] Pack necessary tools and resources for troubleshooting.\n", "\n", "#### Arrival Preparations\n", "4. **Disguise and Identity**:\n", "   - [ ] Prepare appropriate period-accurate clothing and accessories for blending in.\n", "   - [ ] Develop a plausible backstory for your identity (e.g., local merchant).\n", "\n", "5. **Materials for Influence**:\n", "   - [ ] Print or prepare pamphlets and books discussing Enlightenment principles.\n", "   - [ ] Ensure materials are designed to appear as part of the contemporary discourse.\n", "\n", "#### Engagement Strategy\n", "6. **Plan for Initial Engagement**:\n", "   - [ ] Identify key locations (taverns, public gatherings) for informal conversations.\n", "   - [ ] Prepare conversation starters that align with the interests of key figures.\n", "\n", "7. **Collaboration and Unity**:\n", "   - [ ] Outline strategies for fostering collaboration among signers.\n", "   - [ ] Plan informal gatherings to encourage open dialogue.\n", "\n", "#### Contingency Measures\n", "8. **Contingency Plans**:\n", "   - [ ] Review potential challenges and corresponding responses (e.g., unexpected figures, timeline changes).\n", "   - [ ] Ensure ethical guidelines are established and understood.\n", "\n", "9. **Emergency Protocols**:\n", "   - [ ] Confirm procedures for technical malfunctions or unexpected events.\n", "   - [ ] Pack a small emergency kit with basic supplies for unforeseen circumstances.\n", "\n", "#### Final Checks\n", "10. **Mission Objectives**:\n", "    - [ ] Review key objectives and desired outcomes to ensure clarity of purpose.\n", "    - [ ] Confirm that all team members (if applicable) are aligned on mission goals.\n", "\n", "11. **Documentation Tools**:\n", "    - [ ] Prepare notebooks or recording devices for documenting observations.\n", "    - [ ] Ensure all documentation tools are functional and accessible.\n", "\n", "12. **Departure Readiness**:\n", "    - [ ] Confirm the time machine is set for departure.\n", "    - [ ] Conduct a final check of all equipment and materials.\n", "    - [ ] Ensure all team members are present and accounted for.\n", "\n", "By completing this final checklist, we can ensure that all aspects of the time travel mission are thoroughly prepared, increasing the likelihood of a successful and impactful experience.\n", "\n", "Next request..\n", "\n", "[AI User] <CAMEL_TASK_DONE>.\n", "\n"]}], "source": ["run(society)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🌟 Highlights"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In this notebook, This notebook has guided you through setting up and use agent society for task solving.\n", "\n", "Key tools utilized in this notebook include:\n", "\n", "* **CAMEL**: A powerful multi-agent framework that enables Retrieval-Augmented Generation and multi-agent role-playing scenarios, allowing for sophisticated AI-driven tasks.\n", "\n", "* **Agent Society**: Enabling multi-agent communication for the task solving."]}, {"cell_type": "markdown", "metadata": {}, "source": ["That's everything: Got questions about 🐫 CAMEL-AI? Join us on [Discord](https://discord.camel-ai.org)! Whether you want to share feedback, explore the latest in multi-agent systems, get support, or connect with others on exciting projects, we’d love to have you in the community! 🤝\n", "\n", "Check out some of our other work:\n", "\n", "1. 🐫 Creating Your First CAMEL Agent [free Colab](https://docs.camel-ai.org/cookbooks/create_your_first_agent.html)\n", "\n", "2.  Graph RAG Cookbook [free Colab](https://colab.research.google.com/drive/1uZKQSuu0qW6ukkuSv9TukLB9bVaS1H0U?usp=sharing)\n", "\n", "3. 🧑‍⚖️ Create A Hackathon Judge Committee with Workforce [free Colab](https://colab.research.google.com/drive/*********************************?usp=sharing)\n", "\n", "4. 🔥 3 ways to ingest data from websites with Firecrawl & CAMEL [free Colab](https://colab.research.google.com/drive/1lOmM3VmgR1hLwDKdeLGFve_75RFW0R9I?usp=sharing)\n", "\n", "5. 🦥 Agentic SFT Data Generation with CAMEL and Mistral Models, Fine-Tuned with Unsloth [free Colab](https://colab.research.google.com/drive/1lYgArBw7ARVPSpdwgKLYnp_NEXiNDOd-?usp=sharingg)\n", "\n", "Thanks from everyone at 🐫 CAMEL-AI\n", "\n", "\n", "<div class=\"align-center\">\n", "  <a href=\"https://www.camel-ai.org/\"><img src=\"https://i.postimg.cc/KzQ5rfBC/button.png\"width=\"150\"></a>\n", "  <a href=\"https://discord.camel-ai.org\"><img src=\"https://i.postimg.cc/L4wPdG9N/join-2.png\"  width=\"150\"></a></a>\n", "  \n", "⭐ <i>Star us on [*Github*](https://github.com/camel-ai/camel), join our [*Discord*](https://discord.camel-ai.org) or follow our [*X*](https://x.com/camelaiorg)\n", "</div>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python (case study)", "language": "python", "name": "case_study"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 0}