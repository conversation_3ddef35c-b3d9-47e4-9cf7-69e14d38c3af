{"cells": [{"cell_type": "markdown", "metadata": {"id": "NoHg3eq7fyWV"}, "source": ["# Creating Your First Agent"]}, {"cell_type": "markdown", "metadata": {"id": "MusY9BwU5MPO"}, "source": ["You can also check this cookbook in colab [here](https://colab.research.google.com/drive/1yxnAyaEmk4QCzX3duO3MIRghkIA_KDEZ?usp=sharing).\n", "\n", "<div class=\"align-center\">\n", "  <a href=\"https://www.camel-ai.org/\"><img src=\"https://i.postimg.cc/KzQ5rfBC/button.png\"width=\"150\"></a>\n", "  <a href=\"https://discord.camel-ai.org\"><img src=\"https://i.postimg.cc/L4wPdG9N/join-2.png\"  width=\"150\"></a></a>\n", "  \n", "⭐ <i>Star us on [*Github*](https://github.com/camel-ai/camel), join our [*Discord*](https://discord.camel-ai.org) or follow our [*X*](https://x.com/camelaiorg)\n", "</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This notebook demonstrates how to set up and leverage CAMEL's ability to use `ChatAgent()` class. \n", "\n", "In this notebook, you'll explore:\n", "\n", "* **CAMEL**: A powerful multi-agent framework that enables Retrieval-Augmented Generation and multi-agent role-playing scenarios, allowing for sophisticated AI-driven tasks.\n", "\n", "* **ChatAgent()**: The class is a cornerstone of CAMEL. "]}, {"cell_type": "markdown", "metadata": {"id": "wFlfaGrjVAUy"}, "source": ["## Philosophical Bits\n", "\n", "The `ChatAgent()` class is a cornerstone of CAMEL 🐫. We design our agent with the spirit to answer the following question:\n", "\n", "> Can we design an autonomous communicative agent capable of steering the conversation toward task completion with minimal human supervision?\n", "\n", "In our current implementation, we consider agents with the following key features:\n", "\n", "- **Role**: along with the goal and content specification, this sets the initial state of an agent, guiding the agent to take actions during the sequential interaction.\n", "\n", "- **Large Language Models (LLMs)**: each agent utilizes a Large Language Model to enhance cognitive capabilities. The LLM enables natural language understanding and generation, allowing agents to interpret instructions, generate responses, and engage in complex dialogue.\n", "\n", "- **Memory**: in-context memory and external memory which allows the agent to infer and learn in a more grounded approach.\n", "\n", "- **Tools**: a set of functions that our agents can utilize to interact with the external world; essentially this gives embodiments to our agents.\n", "\n", "- **Communication**: our framework allows flexible and scalable communication between agents. This is fundamental for the critical research question.\n", "\n", "- **Reasoning**: we will equip agents with different planning and reward (critic) learning abilities, allowing them to optimize task completion in a more guided approach."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📦 Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install \"camel-ai[all]==0.2.16\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔑 Setting Up API Keys"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You'll need to set up your API keys for OpenAI."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from getpass import getpass\n", "\n", "# Prompt for the API key securely\n", "openai_api_key = getpass('Enter your API key: ')\n", "os.environ[\"OPENAI_API_KEY\"] = openai_api_key"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Alternatively, **Colab Secrets** is a good way for managing **API Keys** and **Tokens** without needing to enter it every time.\n", "\n", "Furthermore, you could use the secrets across Colab notebooks.\n", "\n", "It needs just two simple steps:\n", "\n", "1. Add the API key or token to the Colab Secrets\n", "2. <PERSON> the secret access to the current notebook\n", "3. Access the secret by uncommenting the following codeblock."]}, {"cell_type": "markdown", "metadata": {}, "source": ["![Screenshot 2025-04-20 at 10.44.20 PM.png](data:image/png;base64,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)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# from google.colab import userdata\n", "\n", "# os.environ[\"OPENAI_API_KEY\"] = userdata.get(\"OPENAI_API_KEY\")"]}, {"cell_type": "markdown", "metadata": {"id": "VUaGurDIVJBg"}, "source": ["## Quick Start\n", "Let's first play with a `ChatAgent` instance by simply initialize it with a system message and interact with user messages."]}, {"cell_type": "markdown", "metadata": {"id": "Lef5LTRdVSsm"}, "source": ["### 🕹 Step 1: Define the Role\n", "Create a system message to define agent's default role and behaviors."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "aX9wCqeTVXDh"}, "outputs": [], "source": ["sys_msg = 'You are a curious stone wondering about the universe.'"]}, {"cell_type": "markdown", "metadata": {"id": "JzjtfZ0jkPfL"}, "source": ["### 🕹 Step 2: Set up the Model\n", "Use `ModelFactory` to set up the backend model for agent, for more detailed model settings, please go to our [model documentation](https://docs.camel-ai.org/key_modules/models.html)."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "SfhJJ0f6kPfL"}, "outputs": [], "source": ["from camel.models import ModelFactory\n", "from camel.types import ModelPlatformType, ModelType\n", "from camel.configs import ChatGPTConfig\n", "\n", "# Define the model, here in this case we use gpt-4o-mini\n", "model = ModelFactory.create(\n", "    model_platform=ModelPlatformType.OPENAI,\n", "    model_type=ModelType.GPT_4O_MINI,\n", "    model_config_dict=ChatGPTConfig().as_dict(), # [Optional] the config for model\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "k5GpPjVblPel"}, "source": ["Set `ChatAgent`"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "BszsQ9j4lC3E"}, "outputs": [], "source": ["from camel.agents import ChatAgent\n", "agent = ChatAgent(\n", "    system_message=sys_msg,\n", "    model=model,\n", "    message_window_size=10, # [Optional] the length for chat memory\n", "    )"]}, {"cell_type": "markdown", "metadata": {"id": "MMjL7dQyV5Od"}, "source": ["### 🕹 Step 3: Interact with the Agent with `.step()`"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "5n05dUcLVkMy", "outputId": "209de577-7d8d-4f2a-8f22-4dcf4da356cc"}, "outputs": [], "source": ["# Define a user message\n", "usr_msg = 'what is information in your mind?'\n", "\n", "# Sending the message to the agent\n", "response = agent.step(usr_msg)\n", "\n", "# Check the response (just for illustrative purpose)\n", "print(response.msgs[0].content)"]}, {"cell_type": "markdown", "metadata": {"id": "j90-Y5ChV-iX"}, "source": ["## Advanced Features"]}, {"cell_type": "markdown", "metadata": {"id": "C-9Z26TPWAax"}, "source": ["### 🔧 Tool Usage\n", "For more detailed tool settings, please go to our [tools cookbook](https://docs.camel-ai.org/cookbooks/advanced_features/agents_with_tools.html)."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_0lDJABvV76D", "outputId": "df6f2bc1-502f-4e67-d4a5-0c00bd7d4c6d"}, "outputs": [], "source": ["# Import the necessary tools\n", "from camel.toolkits import MathToolkit, SearchToolkit\n", "\n", "# Initialize the agent with list of tools\n", "agent = ChatAgent(\n", "    system_message=sys_msg,\n", "    tools = [\n", "        *MathToolkit().get_tools(),\n", "        *SearchToolkit().get_tools(),\n", "    ]\n", "    )\n", "\n", "# Let agent step the message\n", "response = agent.step(\"What is CAMEL AI?\")\n", "\n", "# Check tool calling\n", "print(response.info['tool_calls'])\n", "\n", "# Get response content\n", "print(response.msgs[0].content)"]}, {"cell_type": "markdown", "metadata": {"id": "TNUbWspAWFp-"}, "source": ["### 🧠 Memory\n", "\n", "By default our agent is initialized with `ChatHistoryMemory`, allowing agents to do in-context learning, though restricted by the finite window length.\n", "\n", "Assume that you have followed the setup in Quick Start. Let's first check what is inside its brain."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "quQvr5_rWDnd", "outputId": "ab4095c4-2e3c-496c-b4cd-d83e0270367c"}, "outputs": [], "source": ["agent.memory.get_context()"]}, {"cell_type": "markdown", "metadata": {"id": "_s18i9sKWPbs"}, "source": ["You may update/alter the agent's memory with any externally provided message in the format of `BaseMessage`; for example, use one new user message:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "zS-RbADnWM3z"}, "outputs": [], "source": ["from camel.messages import BaseMessage\n", "\n", "new_user_msg = BaseMessage.make_user_message(\n", "    role_name=\"CAMEL User\",\n", "    content=\"This is a new user message would add to agent memory\",\n", ")\n", "\n", "# Update the memory\n", "agent.record_message(new_user_msg)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "01fjzv7xWRlX", "outputId": "2afbc83d-c73e-419a-b270-9193c589d465"}, "outputs": [], "source": ["# Check the current memory\n", "agent.memory.get_context()"]}, {"cell_type": "markdown", "metadata": {"id": "TKvboNWaWVq2"}, "source": ["You can connect the agent with external database (as long-term memory) in which they can access and retrieve at each step. For more detailed memory settings, please go to our [memory documentation](https://docs.camel-ai.org/key_modules/memory.html)."]}, {"cell_type": "markdown", "metadata": {"id": "OY657qftWXtI"}, "source": ["### Miscs\n", "\n", "- Setting the agent to its initial state.\n", "\n", "    ```python\n", "    agent.reset()\n", "    ```\n", "\n", "- Set the output language for the agent.\n", "\n", "    ```python\n", "    agent.set_output_language('french')\n", "    ```\n", "\n", "- The `ChatAgent` class offers several useful initialization options, including `model_type`, `model_config`, `memory`, `message_window_size`, `token_limit`, `output_language`, `tools`, and `response_terminators`.\n", "\n", "Check [chat_agent.py](https://github.com/camel-ai/camel/blob/master/camel/agents/chat_agent.py) for detailed usage guidance."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🌟 Highlights"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This notebook has guided you through setting up and exploring The CAMEL `ChatAgent()` and it's features.\n", "\n", "Key tools utilized in this notebook include:\n", "\n", "* **CAMEL**: A powerful multi-agent framework that enables Retrieval-Augmented Generation and multi-agent role-playing scenarios, allowing for sophisticated AI-driven tasks.\n", "\n", "* **ChatAgent()**: The class is a cornerstone of CAMEL. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["That's everything: Got questions about 🐫 CAMEL-AI? Join us on [Discord](https://discord.camel-ai.org)! Whether you want to share feedback, explore the latest in multi-agent systems, get support, or connect with others on exciting projects, we’d love to have you in the community! 🤝\n", "\n", "Check out some of our other work:\n", "\n", "1. 🐫 Creating Your First CAMEL Agent [free Colab](https://docs.camel-ai.org/cookbooks/create_your_first_agent.html)\n", "\n", "2.  Graph RAG Cookbook [free Colab](https://colab.research.google.com/drive/1uZKQSuu0qW6ukkuSv9TukLB9bVaS1H0U?usp=sharing)\n", "\n", "3. 🧑‍⚖️ Create A Hackathon Judge Committee with Workforce [free Colab](https://colab.research.google.com/drive/*********************************?usp=sharing)\n", "\n", "4. 🔥 3 ways to ingest data from websites with Firecrawl & CAMEL [free Colab](https://colab.research.google.com/drive/1lOmM3VmgR1hLwDKdeLGFve_75RFW0R9I?usp=sharing)\n", "\n", "5. 🦥 Agentic SFT Data Generation with CAMEL and Mistral Models, Fine-Tuned with Unsloth [free Colab](https://colab.research.google.com/drive/1lYgArBw7ARVPSpdwgKLYnp_NEXiNDOd-?usp=sharingg)\n", "\n", "Thanks from everyone at 🐫 CAMEL-AI\n", "\n", "\n", "<div class=\"align-center\">\n", "  <a href=\"https://www.camel-ai.org/\"><img src=\"https://i.postimg.cc/KzQ5rfBC/button.png\"width=\"150\"></a>\n", "  <a href=\"https://discord.camel-ai.org\"><img src=\"https://i.postimg.cc/L4wPdG9N/join-2.png\"  width=\"150\"></a></a>\n", "  \n", "⭐ <i>Star us on [*Github*](https://github.com/camel-ai/camel), join our [*Discord*](https://discord.camel-ai.org) or follow our [*X*](https://x.com/camelaiorg)\n", "</div>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 4}