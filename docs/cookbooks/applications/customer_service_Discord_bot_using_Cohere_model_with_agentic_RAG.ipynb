{"cells": [{"cell_type": "markdown", "metadata": {"id": "DcziiQNCZi7z"}, "source": ["# Customer Service Discord Bot Using Cohere model with Agentic RAG\n", "\n", "In this cookbook, we are going to be implementing a Discord bot that provides customer service assistance for the Cohere AI platform via its comprehensive\n", "documentation sources and listings.\n", "\n", "<div class=\"align-center\">\n", "  <a href=\"https://www.camel-ai.org/\"><img src=\"https://i.postimg.cc/KzQ5rfBC/button.png\"width=\"150\"></a>\n", "  <a href=\"https://discord.camel-ai.org\"><img src=\"https://i.postimg.cc/L4wPdG9N/join-2.png\"  width=\"150\"></a></a>\n", "  \n", "⭐ <i>Star us on [*Github*](https://github.com/camel-ai/camel), join our [*Discord*](https://discord.camel-ai.org) or follow our [*X*](https://x.com/camelaiorg)\n", "</div>"]}, {"cell_type": "markdown", "metadata": {"id": "auMWFWdmZi71"}, "source": ["## Installation and Setup  \n", "Setting up environment, by installing the CAMEL package with all its dependencies"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "eYIPPfHAZi71"}, "outputs": [], "source": ["!pip install \"camel-ai[all]==0.2.16\"\n", "!pip install starlette\n", "!pip install nest_asyncio"]}, {"cell_type": "markdown", "metadata": {"id": "EWVuxF3hZi72"}, "source": ["Next, proceed with setting up API keys for Firecrawl and the model (Cohere)\n", "\n", "If you don't have a FireCrawl API key, you can obtain one by following these steps:\n", "\n", "1. Visit the FireCrawl API Key page https://www.firecrawl.dev/app/api-keys\n", "\n", "2. Log in or sign up for a FireCrawl account.\n", "\n", "3. Navigate to the 'API Keys' section.\n", "\n", "4. Click on 'Create API Key' button to generate a new API key.\n", "\n", "For more details, you can also check the Firecrawl documentation: https://docs.firecrawl.dev/api-reference/introduction"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "seKoFg-UZi72"}, "outputs": [], "source": ["import os\n", "from getpass import getpass\n", "\n", "firecrawl_api_key = getpass(\"Enter your API key: \")\n", "os.environ[\"FIRECRAWL_API_KEY\"] = firecrawl_api_key"]}, {"cell_type": "markdown", "metadata": {"id": "m6_Dy33sZi72"}, "source": ["If you don't have a Cohere API key, you can obtain one by following these steps:\n", "\n", "1. Visit the Cohere dashboard (https://dashboard.cohere.com/api-keys) and follow the on-screen instructions related to account signup/login.\n", "\n", "2. In the left pane dashboard, search for the term \"API Keys\".\n", "\n", "3. On the API Key management page, click on the \"Create Trial Key\" button under the Trial keys section to generate a new trial key without any subscription.\n", "\n", "For more details, you can also check <PERSON>here's documentation: https://docs.cohere.com/cohere-documentation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "w8U-W14MZi72"}, "outputs": [], "source": ["import os\n", "from getpass import getpass\n", "\n", "cohere_api_key = getpass(\"Enter your API key: \")\n", "os.environ[\"COHERE_API_KEY\"] = cohere_api_key"]}, {"cell_type": "markdown", "metadata": {"id": "mQU1cdzJZi72"}, "source": ["## Knowledge Crawling and Storage\n", "\n", "Use Firecrawl to crawl a website and store the content in a markdown file:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "jB4bwYJ5Zi73"}, "outputs": [], "source": ["import os\n", "from camel.loaders import Firecrawl\n", "\n", "os.makedirs('local_data', exist_ok=True)\n", "\n", "firecrawl = Firecrawl()\n", "\n", "knowledge = firecrawl.crawl(\n", "    url=\"https://docs.cohere.com/docs/the-cohere-platform\"\n", ")[\"data\"][0][\"markdown\"]\n", "\n", "with open('local_data/cohere_platform.md', 'w') as file:\n", "     file.write(knowledge)"]}, {"cell_type": "markdown", "metadata": {"id": "f4RnSKfoZi73"}, "source": ["## Basic Agent Setup\n", "\n", "Command R is a large language model optimized for conversational interaction and long context tasks. It targets the “scalable” category of models that balance high performance with strong accuracy, enabling companies to move beyond proof of concept and into production.\n", "\n", "Use Command R  model:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "90Q9mtrPZi74"}, "outputs": [], "source": ["from camel.configs import CohereConfig\n", "from camel.models import ModelFactory\n", "from camel.types import ModelPlatformType, ModelType\n", "\n", "cohere_model = ModelFactory.create(\n", "    model_platform=ModelPlatformType.COHERE,\n", "    model_type=ModelType.COHERE_COMMAND_R,\n", "    model_config_dict=CohereConfig(temperature=0.0).as_dict(),\n", ")\n", "\n", "\n", "# Use Cohere model\n", "model = cohere_model\n", "\n", "# Setting up a ChatAgent with a system prompt\n", "from camel.agents import ChatAgent\n", "from camel.messages import BaseMessage\n", "\n", "agent = ChatAgent(\n", "    system_message=\"You're a helpful assistant\",\n", "    message_window_size=10,\n", "    model=model\n", ")\n", "\n", "knowledge_message = BaseMessage.make_user_message(\n", "    role_name=\"User\", content=f\"Based on the following knowledge: {knowledge}\"\n", ")\n", "agent.update_memory(knowledge_message, \"user\")"]}, {"cell_type": "markdown", "metadata": {"id": "YG68UVGbZi74"}, "source": ["## Basic Chatbot Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "xqE8O5JSZi74", "outputId": "bd69ac6e-9082-4883-f155-c590b3932688"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Start chatting! Type 'exit' to end the conversation.\n", "Assistant: Here are some of <PERSON><PERSON>'s latest models:\n", "\n", " - Command A\n", " - Command R7B\n", " - Command R+\n", " - Command R\n", " - <PERSON><PERSON>\n", " - Embed\n", "\n", "You can find a full list of Cohere's models and more detailed information about them on the official Cohere website and documentation. The models I mentioned above are from the Command family of models and are primarily focused on text generation for applications like conversational agents, summarization, and copywriting.\n", "\n", "Additionally, Cohere also offers models for specific use cases, such as:\n", "\n", " - Retrieval Augmented Generation (RAG): Enables the model to access external data sources, improving the accuracy and factuality of its responses.\n", " - Fine-Tuning: Custom models can be created by uploading a dataset, and Cohere trains and deploys the model for the user. This can be done with generative models, multi-label classification models, rerank models, and chat models.\n", " - Semantic Search: Embeddings-based search goes beyond keywords and considers the context and user intent to provide more meaningful results.\n", "\n", "Cohere also continuously works on improving its models and platform to make them more accessible and powerful. Remember to check the Cohere website for the most up-to-date information and any new additions!\n", "Ending conversation.\n"]}], "source": ["print(\"Start chatting! Type 'exit' to end the conversation.\")\n", "while True:\n", "    user_input = input(\"User: \")\n", "\n", "    if user_input.lower() == \"exit\":\n", "        print(\"Ending conversation.\")\n", "        break\n", "\n", "    assistant_response = agent.step(user_input)\n", "    print(f\"Assistant: {assistant_response.msgs[0].content}\")"]}, {"cell_type": "markdown", "metadata": {"id": "9XygYToUZi75"}, "source": ["## Basic Discord Bot Integration\n", "\n", "To build a discord bot, a discord bot token is necessary.\n", "\n", "If you don't have a bot token, you can obtain one by following these steps:\n", "\n", "1. Go to the Discord Developer Portal:https://discord.com/developers/applications\n", "\n", "2. Log in with your Discord account, or create an account if you don't have one\n", "\n", "3. <PERSON><PERSON> on 'New Application' to create a new bot.\n", "\n", "4. Give your application a name and click 'Create'.\n", "\n", "5. Navigate to the 'Bot' tab on the left sidebar and click '<PERSON><PERSON>'.\n", "\n", "6. Once the bot is created, you will find a 'Token' section. Click 'Reset Token' to generate a new token.\n", "\n", "7. <PERSON><PERSON> the generated token securely.\n", "\n", "To invite the bot:\n", "\n", "1. Navigate to the 'OAuth2' tab, then to 'URL Generator'.\n", "\n", "2. Under 'Scopes', select 'bot'.\n", "\n", "3. Under 'Bot Permissions', select the permissions your bot will need (e.g., 'Send Messages', 'Read Messages' for our bot use)\n", "\n", "4. Copy the generated URL and paste it into your browser to invite the bot to your server.\n", "\n", "To grant the bot permissions:\n", "\n", "1. Navigate to the 'Bot' tab\n", "\n", "2. Under 'Privileged Gateway Intents', check 'Server Members Intent' and 'Message Content Intent'.\n", "\n", "For more details, you can also check the official Discord bot documentation: https://discord.com/developers/docs/intro\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "2LahZ6ZIZi75"}, "outputs": [], "source": ["import os\n", "from getpass import getpass\n", "\n", "discord_bot_token = getpass('Enter your Discord bot token: ')\n", "os.environ[\"DISCORD_BOT_TOKEN\"] = discord_bot_token"]}, {"cell_type": "markdown", "metadata": {"id": "GLRIt2maZi75"}, "source": ["This code cell sets up a simple Discord bot using the DiscordApp class from the camel.bots library. The bot listens for messages in any channel it has access to and provides a response based on the input message."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "trbwrYZ4Zi75", "outputId": "f2c9c1ba-8e6f-45f5-c5a8-43344c379425"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[2025-04-15 02:51:34] [INFO    ] discord.client: logging in using static token\n", "[2025-04-15 02:51:34] [INFO    ] discord.client: logging in using static token\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-04-15 02:51:34,062 - discord.client - INFO - logging in using static token\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-04-15 02:51:36] [INFO    ] discord.gateway: Shard ID None has connected to Gateway (Session ID: 24062ffe37fc004bc4cdf22b7191e936).\n", "[2025-04-15 02:51:36] [INFO    ] discord.gateway: Shard ID None has connected to Gateway (Session ID: 24062ffe37fc004bc4cdf22b7191e936).\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-04-15 02:51:36,780 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 24062ffe37fc004bc4cdf22b7191e936).\n"]}], "source": ["from camel.bots import DiscordApp\n", "import nest_asyncio\n", "import discord\n", "\n", "nest_asyncio.apply()\n", "discord_bot = DiscordApp(token=discord_bot_token)\n", "\n", "@discord_bot.client.event\n", "async def on_message(message: discord.Message):\n", "    if message.author == discord_bot.client.user:\n", "        return\n", "\n", "    if message.type != discord.MessageType.default:\n", "        return\n", "\n", "    if message.author.bot:\n", "        return\n", "    user_input = message.content\n", "\n", "    agent.reset()\n", "    agent.update_memory(knowledge_message, \"user\")\n", "    assistant_response = agent.step(user_input)\n", "\n", "    response_content = assistant_response.msgs[0].content\n", "\n", "    if len(response_content) > 2000: # discord message length limit\n", "        for chunk in [response_content[i:i+2000] for i in range(0, len(response_content), 2000)]:\n", "            await message.channel.send(chunk)\n", "    else:\n", "        await message.channel.send(response_content)\n", "\n", "discord_bot.run()"]}, {"cell_type": "markdown", "source": ["![250415_02h58m41s_screenshot.png](data:image/png;base64,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)"], "metadata": {"id": "vm7KtY-mZpvm"}}, {"cell_type": "markdown", "metadata": {"id": "v4QyHGzNZi76"}, "source": ["## Integrating Qdrant for Large Files to build a more powerful Discord bot\n", "\n", "Qdrant is a vector similarity search engine and vector database. It is designed to perform fast and efficient similarity searches on large datasets of vectors. This enables the chatbot to access and utilize external information to provide more comprehensive and accurate responses. By storing knowledge as vectors, Qdrant enables efficient semantic search, allowing the chatbot to find relevant information based on the meaning of the user's query.\n", "\n", "Set up an embedding model and retriever for Qdrant:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "mdQD6vVCZi76", "outputId": "8dad310f-64ce-4a4b-d3d9-f687eb576ef3"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniforge3/envs/camel/lib/python3.12/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["from camel.embeddings import SentenceTransformerEncoder\n", "\n", "sentence_encoder = SentenceTransformerEncoder(model_name='intfloat/e5-large-v2')"]}, {"cell_type": "markdown", "metadata": {"id": "M_Q1Yc82Zi77"}, "source": ["Set up the AutoRetriever for automatically retrieving relevant information from a storage system."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "JpqVtOpCZi77"}, "outputs": [], "source": ["from camel.retrievers import AutoRetriever\n", "from camel.types import StorageType\n", "\n", "assistant_sys_msg = \"\"\"You are a helpful assistant to answer question,\n", "         I will give you the Original Query and Retrieved Context,\n", "        answer the Original Query based on the Retrieved Context,\n", "        if you can't answer the question just say I don't know.\"\"\"\n", "auto_retriever = AutoRetriever(\n", "              vector_storage_local_path=\"local_data2/\",\n", "              storage_type=StorageType.QDRANT,\n", "              embedding_model=sentence_encoder\n", "            )\n", "qdrant_agent = ChatAgent(system_message=assistant_sys_msg, model=model)"]}, {"cell_type": "markdown", "metadata": {"id": "Tqt2sIiAZi77"}, "source": ["Use Auto RAG to retrieve first and then answer the user's query using CAMEL `ChatAgent` based on the retrieved info:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "MlKHdGg2Zi77"}, "outputs": [], "source": ["from camel.bots import DiscordApp\n", "import nest_asyncio\n", "import discord\n", "\n", "nest_asyncio.apply()\n", "discord_q_bot = DiscordApp(token=discord_bot_token)\n", "\n", "@discord_q_bot.client.event # triggers when a message is sent in the channel\n", "async def on_message(message: discord.Message):\n", "    if message.author == discord_q_bot.client.user:\n", "        return\n", "\n", "    if message.type != discord.MessageType.default:\n", "        return\n", "\n", "    if message.author.bot:\n", "        return\n", "    user_input = message.content\n", "\n", "    retrieved_info = auto_retriever.run_vector_retriever(\n", "        query=user_input,\n", "        contents=[\n", "            \"local_data/cohere_platform.md\",\n", "        ],\n", "        top_k=3,\n", "        return_detailed_info=False,\n", "        similarity_threshold=0.5\n", "    )\n", "\n", "    user_msg = str(retrieved_info)\n", "    assistant_response = qdrant_agent.step(user_msg)\n", "    response_content = assistant_response.msgs[0].content\n", "\n", "    if len(response_content) > 2000: # discord message length limit\n", "        for chunk in [response_content[i:i+2000] for i in range(0, len(response_content), 2000)]:\n", "            await message.channel.send(chunk)\n", "    else:\n", "        await message.channel.send(response_content)\n", "\n", "discord_q_bot.run()"]}, {"cell_type": "markdown", "source": ["![250415_02h58m26s_screenshot.png](data:image/png;base64,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)"], "metadata": {"id": "KUiMDynVamwd"}}, {"cell_type": "markdown", "metadata": {"id": "8B1xvRftZi78"}, "source": ["That's everything: Got questions about 🐫 CAMEL-AI? Join us on [Discord](https://discord.camel-ai.org)! Whether you want to share feedback, explore the latest in multi-agent systems, get support, or connect with others on exciting projects, we’d love to have you in the community! 🤝\n", "\n", "Check out some of our other work:\n", "\n", "1. 🐫 Creating Your First CAMEL Agent [free Colab](https://docs.camel-ai.org/cookbooks/create_your_first_agent.html)\n", "\n", "2.  Graph RAG Cookbook [free Colab](https://colab.research.google.com/drive/1uZKQSuu0qW6ukkuSv9TukLB9bVaS1H0U?usp=sharing)\n", "\n", "3. 🧑‍⚖️ Create A Hackathon Judge Committee with Workforce [free Colab](https://colab.research.google.com/drive/*********************************?usp=sharing)\n", "\n", "4. 🔥 3 ways to ingest data from websites with Firecrawl & CAMEL [free Colab](https://colab.research.google.com/drive/1lOmM3VmgR1hLwDKdeLGFve_75RFW0R9I?usp=sharing)\n", "\n", "5. 🦥 Agentic SFT Data Generation with CAMEL and Mistral Models, Fine-Tuned with Unsloth [free Colab](https://colab.research.google.com/drive/1lYgArBw7ARVPSpdwgKLYnp_NEXiNDOd-?usp=sharingg)\n", "\n", "Thanks from everyone at 🐫 CAMEL-AI"]}, {"cell_type": "markdown", "metadata": {"id": "ZPtpf-K2Zi78"}, "source": ["⭐ **Star the Repo**\n", "\n", "If you find CAMEL useful or interesting, please consider giving it a star on our [CAMEL GitHub Repo](https://github.com/camel-ai/camel)! Your stars help others find this project and motivate us to continue improving it."]}], "metadata": {"kernelspec": {"display_name": "camel", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}, "colab": {"provenance": []}}, "nbformat": 4, "nbformat_minor": 0}