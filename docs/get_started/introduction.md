---
title: "Introduction"
icon: rocket
description: |
  CAMEL-AI is an open-source community for finding the scaling laws of agents for data generation, world simulation, and task automation.
---

## What is CAMEL-AI?

**CAMEL‑AI is an open‑source, modular framework for building intelligent multi‑agent systems.** It provides the primitives to:

- Create **Agents** that reason, plan, and act
- Compose **Societies** of agents with defined roles
- Integrate **Interpreters** for code execution and analysis
- Manage **Memory** for long‑horizon context and learning
- Orchestrate **Retrieval‑Augmented Generation (RAG)** pipelines
- Generate **Synthetic Data** at scale with self‑instruct and verifier loops
- Simulate **Worlds** and agent interactions in environments like social networks

## Core Components

- **Agents**: Atomic reasoning units driven by LLMs, capable of tool calls and decision‑making
- **Societies**: Coordinator layers that assign roles, delegate tasks, and manage collaboration
- **Interpreters**: Execution backends (Python, shell, browsers) for live code evaluation and automation
- **Memory & Storage**: Persistent context layers for chat history, tool outputs, and learned knowledge
- **RAG Pipelines**: Combine chunking, retrieval, and generation for grounded, accurate responses
- **Synthetic Data Engines**: Self‑instruct, Chain‑of‑Thought, and Source2Synth pipelines with verifiers
- **World Simulation**: Platforms like Oasis for large‑scale multi‑agent social simulations
- **Task Automation**: Benchmarks like CRAB for real‑world multi‑step software workflows

## Ecosystem Highlights

<CardGroup cols={2}>
  <Card title="OASIS" icon="globe" href="https://github.com/camel-ai/oasis">
    Large‑scale social simulation environment: model Reddit, Twitter, and user interactions
  </Card>

  <Card title="CRAB Benchmark" icon="crab" href="https://crab.camel-ai.org">
    Cross‑environment agent automation tasks across Ubuntu and Android platforms
  </Card>

  <Card title="Project Loong" icon="dragon" href="/cookbooks/loong/">
    Verifier‑driven synthetic data generation for domain‑specific QA at scale
  </Card>

  <Card title="OWL 🦉" icon="brain-circuit" href="https://github.com/camel-ai/owl">
    OWL (Optimized Workforce Learning) is a multi-agent automation framework for real-world tasks. Built on CAMEL-AI, 
    it enables dynamic agent collaboration using tools like browsers, code interpreters, and multimodal models.
  </Card>
</CardGroup>

## Ready to Get Started?

<CardGroup cols={3}>
  <Card title="Quickstart" icon="bolt" href="/cookbooks/basic_concepts/create_your_first_agent">
    Spin up your first agent in under 5 minutes
  </Card>

  <Card title="Installation" icon="wrench" href="/get_started/installation">
    pip install camel-ai[all] – all toolkits and interpreters included
  </Card>

  <Card title="Explore Cookbooks" icon="book-open" href="/cookbooks/">
    Hands‑on examples: data gen, RAG, simulations, and more
  </Card>
</CardGroup>
