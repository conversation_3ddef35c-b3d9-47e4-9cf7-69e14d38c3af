---
title: "Task Generation Cookbook"
---

You can also check this cookbook in colab [here](https://colab.research.google.com/drive/1n_SjiE7NRmpUUBcRge-gqKAv5mzWPcU0?usp=sharing)

⭐ <i>Star us on [*Github*](https://github.com/camel-ai/camel), join our [*Discord*](https://discord.camel-ai.org) or follow our [*X*](https://x.com/camelaiorg)</i>

In this tutorial, we will focus on demonstrating how to use the task module in the CAMEL framework. We will guide you through creating, evolving, and decomposing tasks to illustrate how the task module can be utilized for efficient task management in agent-based systems.

Sections included:

- Setting up a ChatAgent with the CAMEL framework
- Creating a Task and evolving it with the agent using TaskManager
- Task decomposition using the CAMEL task module

Let's go step by step!

## Step 1: Import necessary CAMEL modules

First, we need to import the required CAMEL modules for creating the ChatAgent and handling tasks.


```python
!pip install "camel-ai==0.2.16"
```


```python
from camel.agents import ChatAgent
from camel.configs import ChatGPTConfig
from camel.messages import BaseMessage
from camel.models import ModelFactory
from camel.tasks import (
    Task,
    TaskManager,
)
from camel.types import (
    ModelPlatformType,
    ModelType,
)
```

Set your OpenAI key


```python
import os
from getpass import getpass

# Prompt for the API key securely
openai_api_key = getpass('Enter your API key: ')
os.environ["OPENAI_API_KEY"] = openai_api_key
```

Alternatively, if running on Colab, you could save your API keys and tokens as **Colab Secrets**, and use them across notebooks.

To do so, **comment out** the above **manual** API key prompt code block(s), and **uncomment** the following codeblock.

⚠️ Don't forget granting access to the API key you would be using to the current notebook.


```python
# import os
# from google.colab import userdata

# os.environ["OPENAI_API_KEY"] = userdata.get("OPENAI_API_KEY")
```

## Step 2: Set up the Large Language Model (LLM)

Next, we set up the model configuration. We are using a GPT-4O Mini in this case for our assistant agent. The configuration is designed to ensure the agent’s behavior remains deterministic by setting temperature=0.0, meaning no randomness will be introduced in the responses.


```python
# Create the model using the configuration
model = ModelFactory.create(
    model_platform=ModelPlatformType.OPENAI,
    model_type=ModelType.GPT_4O_MINI,
    model_config_dict=ChatGPTConfig(temperature=0.0).as_dict(), # [Optional] the config for model
)
```

## Step 3: Initialize the ChatAgent

We now create a ChatAgent using the previously defined model. This agent will interact with tasks in the CAMEL framework, following the role of a personal math tutor and programmer.


```python
# Set up the assistant's system message
assistant_sys_msg = BaseMessage.make_user_message(
    role_name="Teacher",
    content="You are a personal math tutor and programmer.",
)

# Initialize the ChatAgent
agent = ChatAgent(assistant_sys_msg, model)
agent.reset()  # Reset the agent's internal state
```

## Step 4: Evolve and decompose tasks

We now create a Task that represents a math problem for the assistant to solve. In this case, we are asking the assistant to calculate how much Weng earned for babysitting based on her hourly rate and the time she worked.


```python
# Create a Task for the agent to solve
task = Task(
    content="Weng earns $12 an hour for babysitting. Yesterday, she just did 51 minutes of babysitting. How much did she earn?",
    id="0",  # Task identifier
)

# Print the task to see the original form
print(task.to_string())
```

### Evolve the Task

We can evolve the task using the TaskManager, which allows the agent to potentially update or reframe the task based on its internal logic and context.


```python
task_manager = TaskManager(task)

evolved_task = task_manager.evolve(task, agent=agent)
if evolved_task is not None:
    print(evolved_task.to_string())
else:
    print("Evolved task is None.")
```

### Decompose the Task

Sometimes, tasks are complex and need to be broken down into smaller subtasks. We use decompose() to allow the agent to split the original task into simpler parts.


```python
new_tasks = task.decompose(agent=agent)
for t in new_tasks:
    print(t.to_string())
```
