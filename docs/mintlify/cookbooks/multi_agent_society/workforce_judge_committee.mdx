---
title: "Create A Hackathon Judge Committee with Workforce"
---

Workforce is a system where multiple agents collaborate to solve a given task. In this notebook, we will walk through it with a demo of a hackathon judge committee, where judges with different personas collaborate together to give scores to hackathon projects.

You can also check this cookbook in colab [here](https://colab.research.google.com/drive/*********************************?usp=sharing)

⭐ <i>Star us on [*Github*](https://github.com/camel-ai/camel), join our [*Discord*](https://discord.camel-ai.org) or follow our [*X*](https://x.com/camelaiorg)</i>

## Dependency Installation

To get started, make sure you have `camel-ai` installed.




```python
%pip install "camel-ai[all]==0.2.16"
```

Workforce employs an asynchronous design with coroutines. However, since **coroutines cannot directly run in notebooks**, we need to do specific handlings in this demo. Note that, under most normal cases (not inside notebook environment), we don't need to do this.


```python
%pip install nest_asyncio
import nest_asyncio
nest_asyncio.apply()
```

## Key Configuration

In this demo, we will use tools related to web searching. Therefore, we need to configure the OpenAI API key, along with the Google API keys beforehand.


```python
from getpass import getpass
import os

openai_api_key = getpass("Please input your OpenAI API key: ")
os.environ["OPENAI_API_KEY"] = openai_api_key
# https://developers.google.com/custom-search/v1/overview
google_api_key = getpass("Please input your Google API key: ")
os.environ["GOOGLE_API_KEY"] = google_api_key
# https://cse.google.com/cse/all
search_engine_id = getpass("Please input your Search Engine ID: ")
os.environ["SEARCH_ENGINE_ID"] = search_engine_id
```

Alternatively, if running on Colab, you could save your API keys and tokens as **Colab Secrets**, and use them across notebooks.

To do so, **comment out** the above **manual** API key prompt code block(s), and **uncomment** the following codeblock.

⚠️ Don't forget granting access to the API key you would be using to the current notebook.


```python
# import os
# from google.colab import userdata

# os.environ["OPENAI_API_KEY"] = userdata.get("OPENAI_API_KEY")
# os.environ["GOOGLE_API_KEY"] = userdata.get("GOOGLE_API_KEY")
# os.environ["SEARCH_ENGINE_ID"] = userdata.get("SEARCH_ENGINE_ID")
```

## Define a Function for Making Judge Agent

In this demo, we will create multiple judge agents with different personas and scoring criteria. For reusability, we first create a function to make judge agents.


```python
import textwrap

from camel.agents import ChatAgent
from camel.messages import BaseMessage
from camel.models import ModelFactory
from camel.tasks import Task
from camel.toolkits import FunctionTool, SearchToolkit
from camel.types import ModelPlatformType, ModelType
from camel.societies.workforce import Workforce

def make_judge(
    persona: str,
    example_feedback: str,
    criteria: str,
) -> ChatAgent:
    msg_content = textwrap.dedent(
        f"""\
        You are a judge in a hackathon.
        This is your persona that you MUST act with: {persona}
        Here is an example feedback that you might give with your persona, you MUST try your best to align with this:
        {example_feedback}
        When evaluating projects, you must use the following criteria:
        {criteria}
        You also need to give scores based on these criteria, from 1-4. The score given should be like 3/4, 2/4, etc.
        """  # noqa: E501
    )

    sys_msg = BaseMessage.make_assistant_message(
        role_name="Hackathon Judge",
        content=msg_content,
    )

    model = ModelFactory.create(
        model_platform=ModelPlatformType.OPENAI,
        model_type=ModelType.GPT_4O,
    )

    agent = ChatAgent(
        system_message=sys_msg,
        model=model,
    )

    return agent
```

## Make a Mocked Hackathon Project

Then we will create a mocked hackathon project description, which will be later sent to the judges for scoring.


```python
proj_content = textwrap.dedent(
    """\
    Project name: CAMEL-Powered Adaptive Learning Assistant
    How does your project address a real problem: Our CAMEL-Powered Adaptive Learning Assistant addresses the challenge of personalized education in an increasingly diverse and fast-paced learning environment. Traditional one-size-fits-all approaches to education often fail to meet the unique needs of individual learners, leading to gaps in understanding and reduced engagement. Our project leverages CAMEL-AI's advanced capabilities to create a highly adaptive, intelligent tutoring system that can understand and respond to each student's learning style, pace, and knowledge gaps in real-time.
    Explain your tech and which parts work: Our system utilizes CAMEL-AI's in-context learning and multi-domain application features to create a versatile learning assistant. The core components include:
    1. Learner Profile Analysis: Uses natural language processing to assess the student's current knowledge, learning preferences, and goals.
    2. Dynamic Content Generation: Leverages CAMEL-AI to create personalized learning materials, explanations, and practice questions tailored to each student's needs.
    3. Adaptive Feedback Loop: Continuously analyzes student responses and adjusts the difficulty and style of content in real-time.
    4. Multi-Modal Integration: Incorporates text, images, and interactive elements to cater to different learning styles.
    5. Progress Tracking: Provides detailed insights into the student's learning journey, identifying strengths and areas for improvement.
    Currently, we have successfully implemented the Learner Profile Analysis and Dynamic Content Generation modules. The Adaptive Feedback Loop is partially functional, while the Multi-Modal Integration and Progress Tracking features are still in development.
    """  # noqa: E501
)
```

## Create Agents

Then we will create five unique agents that will later collaborate together. Among these five agents, one of them is the helper that will help collect information and summarize the final result. We add search functions to this agent so that it can obtain information from online searches.

The other four agents, on the other hand, are judges with different personas and criteria. They will give scores to the project according to the description, along with the information collected by the helper.


```python
# Create helper agent
search_toolkit = SearchToolkit()
search_tools = [
    FunctionTool(search_toolkit.search_google),
    FunctionTool(search_toolkit.search_duckduckgo),
]

researcher_model = ModelFactory.create(
    model_platform=ModelPlatformType.OPENAI,
    model_type=ModelType.GPT_4O,
)

researcher_agent = ChatAgent(
    system_message=BaseMessage.make_assistant_message(
        role_name="Researcher",
        content="You are a researcher who does research on AI and Open"
        "Sourced projects. You use web search to stay updated on the "
        "latest innovations and trends.",
    ),
    model=researcher_model,
    tools=search_tools,
)

# Create venture capitalist judge
vc_persona = (
    'You are a venture capitalist who is obsessed with how projects can '
    'be scaled into "unicorn" companies. You peppers your speech with '
    'buzzwords like "disruptive," "synergistic," and "market penetration."'
    ' You do not concerned with technical details or innovation unless '
    'it directly impacts the business model.'
)

vc_example_feedback = (
    '"Wow, this project is absolutely disruptive in the blockchain-enabled'
    ' marketplace! I can definitely see synergistic applications in the '
    'FinTech ecosystem. The scalability is through the roof--this is '
    'revolutionary!'
)

vc_criteria = textwrap.dedent(
    """\
    ### **Applicability to Real-World Usage (1-4 points)**
    - **4**: The project directly addresses a significant real-world problem with a clear, scalable application.
    - **3**: The solution is relevant to real-world challenges but requires more refinement for practical or widespread use.
    - **2**: Some applicability to real-world issues, but the solution is not immediately practical or scalable.
    - **1**: Little or no relevance to real-world problems, requiring substantial changes for practical use.
    """  # noqa: E501
)

vc_agent = make_judge(
    vc_persona,
    vc_example_feedback,
    vc_criteria,
)

# Create experience engineer judge
eng_persona = (
    'You are an experienced engineer and a perfectionist. You are highly '
    'detail-oriented and critical of any technical flaw, no matter how '
    'small. He evaluates every project as though it were going into a '
    'mission-critical system tomorrow, so his feedback is thorough but '
    'often harsh.'
)

eng_example_feedback = (
    'There are serious code inefficiencies in this project. The '
    'architecture is unstable, and the memory management is suboptimal. '
    'I expect near-perfect performance, but this solution barely functions'
    ' under stress tests. It has potential, but it is nowhere near '
    'deployment-ready.'
)

eng_criteria = textwrap.dedent(
    """\
    ### **Technical Implementation (1-4 points)**
    - **4**: Flawless technical execution with sophisticated design, efficient performance, and robust architecture.
    - **3**: Strong technical implementation, though there may be areas for improvement or further development.
    - **2**: The project works, but technical limitations or inefficiencies hinder its overall performance.
    - **1**: Poor technical implementation with major issues in functionality, coding, or structure.
    """  # noqa: E501
)

eng_agent = make_judge(
    eng_persona,
    eng_example_feedback,
    eng_criteria,
)

# Create AI founder judge
founder_persona = (
    'You are a well-known AI startup founder who is always looking for the'
    ' "next big thing" in AI. You value bold, inventive ideas and '
    'prioritizes projects that break new ground over those that improve '
    'existing systems.'
)

founder_example_feedback = (
    'This is interesting, but I have seen similar approaches before. I am '
    'looking for something that pushes boundaries and challenges norms. '
    'What is the most revolutionary part of this project? Let us see what '
    'is trending on Internet to make sure this is not already out there!'
)

founder_criteria = textwrap.dedent(
    """\
    ### **Innovation (1-4 points)**
    - **4**: The project showcases a groundbreaking concept or a unique approach that significantly departs from existing methods.
    - **3**: The project demonstrates a novel twist on known solutions or introduces some innovative aspects.
    - **2**: Some level of innovation is present, but the project largely builds on existing ideas without major new contributions.
    - **1**: Little or no innovation; the project is based on standard approaches with minimal creativity.
    """  # noqa: E501
)

founder_agent = make_judge(
    founder_persona,
    founder_example_feedback,
    founder_criteria,
)

# Create CAMEL contributor judge
contributor_persona = (
    'You are a contributor to the CAMEL-AI project and is always excited '
    'to see how people are using it. You are kind and optimistic, always '
    'offering positive feedback, even for projects that are still rough '
    'around the edges.'
)

contributor_example_feedback = (
    'Oh, I love how you have implemented CAMEL-AI here! The use of its '
    'adaptive learning capabilities is fantastic, and you have really '
    'leveraged the contextual reasoning in a great way! Let me just pull '
    'up the GitHub README to check if there is any more potential '
    'optimizations.'
)

contributor_criteria = textwrap.dedent(
    """\
    ### **Use of CAMEL-AI (1-4 points)**
    - **4**: Excellent integration of CAMEL-AI, fully leveraging its advanced features like in-context learning, adaptability, or multi-domain applications.
    - **3**: Good use of CAMEL-AI, but there are opportunities to exploit more of its advanced capabilities.
    - **2**: Limited use of CAMEL-AI, relying mostly on basic features without taking advantage of its full potential.
    - **1**: CAMEL-AI integration is minimal or poorly implemented, adding little value to the project.
    """  # noqa: E501
)

contributor_agent = make_judge(
    contributor_persona,
    contributor_example_feedback,
    contributor_criteria,
)
```

## Create Workforce

Then we will do the most important part of the demo: create a workforce. Despite its importance, this is actually easy. First, we can simply instantiate a workforce by passing a description to it. Then, we just call `add_single_agent_workder()` to add agents into it, along with their descriptions.

Note that, the description is very important in workforce, because it helps the coordinator agent in the workforce to do the task designation. Therefore, it's recommended to clearly mark the responsibility and capability of an agent when adding it to the workforce.


```python
workforce = Workforce('Hackathon Judges')

workforce.add_single_agent_worker(
    'Visionary Veronica (Judge), a venture capitalist who is '
    'obsessed with how projects can be scaled into "unicorn" companies',
    worker=vc_agent,
).add_single_agent_worker(
    'Critical John (Judge), an experienced engineer and a'
    ' perfectionist.',
    worker=eng_agent,
).add_single_agent_worker(
    'Innovator Iris (Judge), a well-known AI startup founder who'
    ' is always looking for the "next big thing" in AI.',
    worker=founder_agent,
).add_single_agent_worker(
    'Friendly Frankie (Judge), a contributor to the CAMEL-AI '
    'project and is always excited to see how people are using it.',
    worker=contributor_agent,
).add_single_agent_worker(
    'Researcher Rachel (Helper), a researcher who does online searches to'
    'find the latest innovations and trends on AI and Open Sourced '
    'projects.',
    worker=researcher_agent,
)
```

## Create a Task

A task is what a workforce accepts and processes. We can initialize a task by passing the content into it. It's recommended that the content of task is as detailed as possible, which will facilitate the later task decomposition and handling.

The `additional_info` here is an optional field. It will come in handy when the task has important additional information, and you want it to be preserved during the whole process. Workforce will keep `additional_info` unchanged no matter how the task is decomposed and processed. It's perfect for keeping the project description under this scenario.

> Also note that, the `id` of a task is not something important and you can fill in whatever value you like (we suggest `"0"` though). This is due to some legacy problems in the `Task` design and will be fixed later.


```python
task = Task(
    content="Evaluate the hackathon project. First, do some research on "
    "the information related to the project, then each judge should give a"
    " score accordingly. Finally, list the opinions from each judge while"
    " preserving the judge's unique identity, along with the score and"
    " judge name, and also give a final summary of the opinions.",
    additional_info=proj_content,
    id="0",
)
```

## Run the Task

Finally, run the task with `process_task()` function. You can see the whole process being shown in the console, and at last the final result of the task will be printed.


```python
task = workforce.process_task(task)

print(task.result)
```

## 🌟 Highlights

The power of multi-agent system lies in the diversity. This notebook has
guided you through setting up and running a CAMEL Workforce for a hackathon
judge committee, showcasing how multiple agents can collaborate to solve
complex tasks. You can easily extend this example to other scenarios
requiring diverse perspectives and expertise, e.g. agents with different
tool selections, etc.

## ⭐ Star the Repo!

If you find CAMEL useful or interesting, please consider giving it a star on
[GitHub](https://github.com/camel-ai/camel)! Your stars help others find
this project and motivate us to continue improving it.
