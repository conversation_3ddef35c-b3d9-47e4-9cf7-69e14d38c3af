<a id="camel.utils.async_func"></a>

<a id="camel.utils.async_func.sync_funcs_to_async"></a>

## sync_funcs_to_async

```python
def sync_funcs_to_async(funcs: list[FunctionTool]):
```

Convert a list of Python synchronous functions to Python
asynchronous functions.

**Parameters:**

- **funcs** (list[FunctionTool]): List of Python synchronous functions in the :obj:`FunctionTool` format.

**Returns:**

  list[FunctionTool]: List of Python asynchronous functions
in the :obj:`FunctionTool` format.
