<a id="camel.toolkits.meshy_toolkit"></a>

<a id="camel.toolkits.meshy_toolkit.MeshyToolkit"></a>

## MeshyToolkit

```python
class MeshyToolkit(BaseToolkit):
```

A class representing a toolkit for 3D model generation using Meshy.

This class provides methods that handle text/image to 3D model
generation using Meshy.

Call the generate_3d_model_complete method to generate a refined 3D model.

Ref:
https://docs.meshy.ai/api-text-to-3d-beta#create-a-text-to-3d-preview-task

<a id="camel.toolkits.meshy_toolkit.MeshyToolkit.__init__"></a>

### __init__

```python
def __init__(self, timeout: Optional[float] = None):
```

Initializes the MeshyToolkit with the API key from the
environment.

<a id="camel.toolkits.meshy_toolkit.MeshyToolkit.generate_3d_preview"></a>

### generate_3d_preview

```python
def generate_3d_preview(
    self,
    prompt: str,
    art_style: str,
    negative_prompt: str
):
```

Generates a 3D preview using the Meshy API.

**Parameters:**

- **prompt** (str): Description of the object.
- **art_style** (str): Art style for the 3D model.
- **negative_prompt** (str): What the model should not look like.

**Returns:**

  Dict[str, Any]: The result property of the response contains the
task id of the newly created Text to 3D task.

<a id="camel.toolkits.meshy_toolkit.MeshyToolkit.refine_3d_model"></a>

### refine_3d_model

```python
def refine_3d_model(self, preview_task_id: str):
```

Refines a 3D model using the Meshy API.

**Parameters:**

- **preview_task_id** (str): The task ID of the preview to refine.

**Returns:**

  Dict[str, Any]: The response from the Meshy API.

<a id="camel.toolkits.meshy_toolkit.MeshyToolkit.get_task_status"></a>

### get_task_status

```python
def get_task_status(self, task_id: str):
```

Retrieves the status or result of a specific 3D model generation
task using the Meshy API.

**Parameters:**

- **task_id** (str): The ID of the task to retrieve.

**Returns:**

  Dict[str, Any]: The response from the Meshy API.

<a id="camel.toolkits.meshy_toolkit.MeshyToolkit.wait_for_task_completion"></a>

### wait_for_task_completion

```python
def wait_for_task_completion(
    self,
    task_id: str,
    polling_interval: int = 10,
    timeout: int = 3600
):
```

Waits for a task to complete by polling its status.

**Parameters:**

- **task_id** (str): The ID of the task to monitor.
- **polling_interval** (int): Seconds to wait between status checks. (default: :obj:`10`)
- **timeout** (int): Maximum seconds to wait before timing out. (default: :obj:`3600`)

**Returns:**

  Dict[str, Any]: Final response from the API when task completes.

<a id="camel.toolkits.meshy_toolkit.MeshyToolkit.generate_3d_model_complete"></a>

### generate_3d_model_complete

```python
def generate_3d_model_complete(
    self,
    prompt: str,
    art_style: str,
    negative_prompt: str
):
```

Generates a complete 3D model by handling preview and refinement
stages

**Parameters:**

- **prompt** (str): Description of the object.
- **art_style** (str): Art style for the 3D model.
- **negative_prompt** (str): What the model should not look like.

**Returns:**

  Dict[str, Any]: The final refined 3D model response.

<a id="camel.toolkits.meshy_toolkit.MeshyToolkit.get_tools"></a>

### get_tools

```python
def get_tools(self):
```

**Returns:**

  List[FunctionTool]: A list of FunctionTool objects
representing the functions in the toolkit.
