<a id="camel.verifiers.models"></a>

<a id="camel.verifiers.models.VerificationOutcome"></a>

## VerificationOutcome

```python
class VerificationOutcome(Enum):
```

Enum representing the status of a verification.

<a id="camel.verifiers.models.VerificationOutcome.__bool__"></a>

### __bool__

```python
def __bool__(self):
```

Only VerificationOutcome.SUCCESS is truthy; others are falsy.

<a id="camel.verifiers.models.VerificationResult"></a>

## VerificationResult

```python
class VerificationResult(BaseModel):
```

Structured result from a verification.

<a id="camel.verifiers.models.VerifierConfig"></a>

## VerifierConfig

```python
class VerifierConfig(BaseModel):
```

Configuration for verifier behavior.
