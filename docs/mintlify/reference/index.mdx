---
title: "API Reference"
description: "Complete API documentation for CAMEL-AI framework"
---

# CAMEL-AI API Reference

Welcome to the comprehensive API reference for CAMEL-AI, a powerful framework for building multi-agent systems and AI applications.

## Overview

CAMEL-AI provides a rich set of modules and components to help you build sophisticated AI agents and multi-agent systems. This API reference covers all the core modules, utilities, and tools available in the framework.

## Core Modules

### 🤖 [Agents](/reference/camel.agents.base)
Build and manage AI agents with various capabilities including chat agents, critic agents, and specialized tool agents.

### 🧠 [Models](/reference/camel.models.base_model)
Interface with various language models from different providers including OpenAI, Anthropic, Google, and more.

### 💬 [Messages](/reference/camel.messages.base)
Handle message formatting, conversion, and management for agent communications.

### 🧩 [Prompts](/reference/camel.prompts.base)
Access pre-built prompt templates and create custom prompts for different use cases.

### 🔧 [Toolkits](/reference/camel.toolkits.base)
Extend agent capabilities with a comprehensive collection of tools for web search, file operations, APIs, and more.

## Data & Storage

### 📊 [Datasets](/reference/camel.datasets.base_generator)
Generate and manage datasets for training and evaluation purposes.

### 🗄️ [Storage](/reference/camel.storages.vectordb_storages.base)
Store and retrieve data using various storage backends including vector databases, key-value stores, and object storage.

### 🔍 [Retrievers](/reference/camel.retrievers.base)
Implement retrieval-augmented generation (RAG) with various retrieval strategies.

### 📝 [Memory](/reference/camel.memories.base)
Manage agent memory and context for long-running conversations.

## Advanced Features

### 🏢 [Societies](/reference/camel.societies.role_playing)
Create multi-agent societies with role-playing capabilities and complex interactions.

### ⚙️ [Runtime](/reference/camel.runtime.base)
Execute code and manage runtime environments for agent operations.

### 🔌 [Interpreters](/reference/camel.interpreters.base)
Run code in various environments including Python, Docker, and cloud platforms.

### 📥 [Loaders](/reference/camel.loaders.base_io)
Load and process data from various sources including web pages, documents, and APIs.

## Configuration & Types

### ⚙️ [Configs](/reference/camel.configs.base_config)
Configure models and services with provider-specific settings.

### 🏷️ [Types](/reference/camel.types.enums)
Type definitions and enumerations used throughout the framework.

### 📐 [Schemas](/reference/camel.schemas.base)
Define and validate data structures for structured outputs.

## Utilities & Extensions

### 🛠️ [Utilities](/reference/camel.utils.commons)
Common utility functions for various operations.

### 🔍 [Verifiers](/reference/camel.verifiers.base)
Verify and validate outputs from agents and models.

### 🏁 [Terminators](/reference/camel.terminators.base)
Control when conversations and processes should end.

### 🌐 [Environments](/reference/camel.environments.models)
Simulate environments for agent interactions and testing.

## Specialized Components

### 🤖 [Bots](/reference/camel.bots.telegram_bot)
Deploy agents as bots on various platforms like Discord, Slack, and Telegram.

### 📊 [Benchmarks](/reference/camel.benchmarks.base)
Evaluate agent performance using standardized benchmarks.

### 🎭 [Personas](/reference/camel.personas.persona)
Create and manage agent personas for role-playing scenarios.

### 🔄 [Data Generation](/reference/camel.datagen.cot_datagen)
Generate synthetic data for training and evaluation.

### 📚 [Data Collector](/reference/camel.data_collector.base)
Collect and process data from various sources.

### 🏢 [Datahubs](/reference/camel.datahubs.base)
Manage data repositories and hubs.

### 🧩 [Extractors](/reference/camel.extractors.base)
Extract structured information from unstructured data.

### 🔗 [Embeddings](/reference/camel.embeddings.base)
Generate and work with text embeddings from various providers.

## Getting Started

1. **Choose your use case**: Browse the modules above to find components relevant to your project
2. **Check examples**: Each module page includes usage examples and code snippets
3. **Explore integrations**: See how different modules work together in the [Cookbooks](/cookbooks)
4. **Join the community**: Get help and share your projects on our [Discord](https://discord.camel-ai.org)

## Need Help?

- 📖 **Documentation**: Start with our [Getting Started](/get_started/installation) guide
- 🍳 **Cookbooks**: Check out practical examples in our [Cookbooks](/cookbooks) section
- 💬 **Community**: Join our [Discord community](https://discord.camel-ai.org) for support
- 🐛 **Issues**: Report bugs on [GitHub](https://github.com/camel-ai/camel/issues)

---

*This API reference is automatically generated from the CAMEL-AI codebase. For the latest updates, visit our [GitHub repository](https://github.com/camel-ai/camel).* 