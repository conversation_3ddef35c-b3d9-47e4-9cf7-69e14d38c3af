<a id="camel.toolkits.non_visual_browser_toolkit.snapshot"></a>

<a id="camel.toolkits.non_visual_browser_toolkit.snapshot.PageSnapshot"></a>

## PageSnapshot

```python
class PageSnapshot:
```

Utility for capturing YAML-like page snapshots and diff-only
variants.

<a id="camel.toolkits.non_visual_browser_toolkit.snapshot.PageSnapshot.__init__"></a>

### __init__

```python
def __init__(self, page: 'Page'):
```

<a id="camel.toolkits.non_visual_browser_toolkit.snapshot.PageSnapshot._format_snapshot"></a>

### _format_snapshot

```python
def _format_snapshot(text: str):
```

<a id="camel.toolkits.non_visual_browser_toolkit.snapshot.PageSnapshot._compute_diff"></a>

### _compute_diff

```python
def _compute_diff(old: str, new: str):
```

<a id="camel.toolkits.non_visual_browser_toolkit.snapshot.PageSnapshot._detect_priorities"></a>

### _detect_priorities

```python
def _detect_priorities(self, snapshot_yaml: str):
```

Return sorted list of priorities present (1,2,3).
