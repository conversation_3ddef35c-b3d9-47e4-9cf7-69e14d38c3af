<a id="camel.toolkits.markitdown_toolkit"></a>

<a id="camel.toolkits.markitdown_toolkit.MarkItDownToolkit"></a>

## MarkItDownToolkit

```python
class MarkItDownToolkit(BaseToolkit):
```

A class representing a toolkit for MarkItDown.

<a id="camel.toolkits.markitdown_toolkit.MarkItDownToolkit.__init__"></a>

### __init__

```python
def __init__(self, timeout: Optional[float] = None):
```

<a id="camel.toolkits.markitdown_toolkit.MarkItDownToolkit.read_files"></a>

### read_files

```python
def read_files(self, file_paths: List[str]):
```

Scrapes content from a list of files and converts it to Markdown.

This function takes a list of local file paths, attempts to convert
each file into Markdown format, and returns the converted content.
The conversion is performed in parallel for efficiency.

Supported file formats include:
- PDF (.pdf)
- Microsoft Office: Word (.doc, .docx), Excel (.xls, .xlsx),
PowerPoint (.ppt, .pptx)
- EPUB (.epub)
- HTML (.html, .htm)
- Images (.jpg, .jpeg, .png) for OCR
- Audio (.mp3, .wav) for transcription
- Text-based formats (.csv, .json, .xml, .txt)
- ZIP archives (.zip)

**Parameters:**

- **file_paths** (List[str]): A list of local file paths to be converted.

**Returns:**

  Dict[str, str]: A dictionary where keys are the input file paths
and values are the corresponding content in Markdown format.
If conversion of a file fails, the value will contain an
error message.

<a id="camel.toolkits.markitdown_toolkit.MarkItDownToolkit.get_tools"></a>

### get_tools

```python
def get_tools(self):
```

**Returns:**

  List[FunctionTool]: A list of FunctionTool objects
representing the functions in the toolkit.
