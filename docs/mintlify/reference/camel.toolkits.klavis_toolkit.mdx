<a id="camel.toolkits.klavis_toolkit"></a>

<a id="camel.toolkits.klavis_toolkit.KlavisToolkit"></a>

## KlavisToolkit

```python
class KlavisToolkit(BaseToolkit):
```

A class representing a toolkit for interacting with Klavis API.

This class provides methods for interacting with Klavis MCP server
instances, retrieving server information, managing tools, and handling
authentication.

**Parameters:**

- **api_key** (str): The API key for authenticating with Klavis API.
- **base_url** (str): The base URL for Klavis API endpoints.
- **timeout** (Optional[float]): The timeout value for API requests in seconds. If None, no timeout is applied. (default: :obj:`None`)

<a id="camel.toolkits.klavis_toolkit.KlavisToolkit.__init__"></a>

### __init__

```python
def __init__(self, timeout: Optional[float] = None):
```

Initialize the KlavisToolkit with API client. The API key is
retrieved from environment variables.

<a id="camel.toolkits.klavis_toolkit.KlavisToolkit._request"></a>

### _request

```python
def _request(
    self,
    method: str,
    endpoint: str,
    payload: Optional[Dict[str, Any]] = None,
    additional_headers: Optional[Dict[str, str]] = None
):
```

Make an HTTP request to the Klavis API.

**Parameters:**

- **method** (str): HTTP method (e.g., 'GET', 'POST', 'DELETE').
- **endpoint** (str): API endpoint path.
- **payload** (Optional[Dict[str, Any]]): JSON payload for POST requests.
- **additional_headers** (Optional[Dict[str, str]]): Additional headers to include in the request.

**Returns:**

  Dict[str, Any]: The JSON response from the API or an error
dict.

<a id="camel.toolkits.klavis_toolkit.KlavisToolkit.create_server_instance"></a>

### create_server_instance

```python
def create_server_instance(
    self,
    server_name: str,
    user_id: str,
    platform_name: str
):
```

Create a Server-Sent Events (SSE) URL for a specified MCP server.

**Parameters:**

- **server_name** (str): The name of the target MCP server.
- **user_id** (str): The ID for the user requesting the server URL.
- **platform_name** (str): The name of the platform associated with the user.

**Returns:**

  Dict[str, Any]: Response containing the server instance details.

<a id="camel.toolkits.klavis_toolkit.KlavisToolkit.get_server_instance"></a>

### get_server_instance

```python
def get_server_instance(self, instance_id: str):
```

Get details of a specific server connection instance.

**Parameters:**

- **instance_id** (str): The ID of the connection instance whose status is being checked.

**Returns:**

  Dict[str, Any]: Details about the server instance.

<a id="camel.toolkits.klavis_toolkit.KlavisToolkit.delete_auth_data"></a>

### delete_auth_data

```python
def delete_auth_data(self, instance_id: str):
```

Delete authentication metadata for a specific server
connection instance.

**Parameters:**

- **instance_id** (str): The ID of the connection instance to delete auth for.

**Returns:**

  Dict[str, Any]: Status response for the operation.

<a id="camel.toolkits.klavis_toolkit.KlavisToolkit.delete_server_instance"></a>

### delete_server_instance

```python
def delete_server_instance(self, instance_id: str):
```

Completely removes a server connection instance.

**Parameters:**

- **instance_id** (str): The ID of the connection instance to delete.

**Returns:**

  Dict[str, Any]: Status response for the operation.

<a id="camel.toolkits.klavis_toolkit.KlavisToolkit.get_all_servers"></a>

### get_all_servers

```python
def get_all_servers(self):
```

**Returns:**

  Dict[str, Any]: Information about all available MCP servers.

<a id="camel.toolkits.klavis_toolkit.KlavisToolkit.set_auth_token"></a>

### set_auth_token

```python
def set_auth_token(self, instance_id: str, auth_token: str):
```

Sets an authentication token for a specific instance.

**Parameters:**

- **instance_id** (str): The ID for the connection instance.
- **auth_token** (str): The authentication token to save.

**Returns:**

  Dict[str, Any]: Status response for the operation.

<a id="camel.toolkits.klavis_toolkit.KlavisToolkit.list_tools"></a>

### list_tools

```python
def list_tools(self, server_url: str):
```

Lists all tools available for a specific remote MCP server.

**Parameters:**

- **server_url** (str): The full URL for connecting to the MCP server via Server-Sent Events (SSE).

**Returns:**

  Dict[str, Any]: Response containing the list of tools or an error.

<a id="camel.toolkits.klavis_toolkit.KlavisToolkit.call_tool"></a>

### call_tool

```python
def call_tool(
    self,
    server_url: str,
    tool_name: str,
    tool_args: Optional[Dict[str, Any]] = None
):
```

Calls a remote MCP server tool directly using the provided server
URL.

**Parameters:**

- **server_url** (str): The full URL for connecting to the MCP server via Server-Sent Events (SSE).
- **tool_name** (str): The name of the tool to call.
- **tool_args** (Optional[Dict[str, Any]]): The input parameters for the tool. Defaults to None, which might be treated as empty args by the server. (default: :obj:`None`)

**Returns:**

  Dict[str, Any]: Response containing the result of the tool call
or an error.

<a id="camel.toolkits.klavis_toolkit.KlavisToolkit.get_tools"></a>

### get_tools

```python
def get_tools(self):
```

**Returns:**

  List[FunctionTool]: A list of FunctionTool objects representing
the functions in the toolkit.
