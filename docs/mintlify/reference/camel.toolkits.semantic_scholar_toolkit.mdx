<a id="camel.toolkits.semantic_scholar_toolkit"></a>

<a id="camel.toolkits.semantic_scholar_toolkit.SemanticScholarToolkit"></a>

## SemanticScholarToolkit

```python
class SemanticScholarToolkit(BaseToolkit):
```

A toolkit for interacting with the Semantic Scholar
API to fetch paper and author data.

<a id="camel.toolkits.semantic_scholar_toolkit.SemanticScholarToolkit.__init__"></a>

### __init__

```python
def __init__(self, timeout: Optional[float] = None):
```

Initializes the SemanticScholarToolkit.

<a id="camel.toolkits.semantic_scholar_toolkit.SemanticScholarToolkit.fetch_paper_data_title"></a>

### fetch_paper_data_title

```python
def fetch_paper_data_title(self, paper_title: str, fields: Optional[List[str]] = None):
```

Fetches a SINGLE paper from the Semantic Scholar
API based on a paper title.

**Parameters:**

- **paper_title** (str): The title of the paper to fetch.
- **fields** (Optional[List[str]], optional): The fields to include in the response (default: :obj:`None`). If not provided defaults to ["title", "abstract", "authors", "year", "citationCount", "publicationTypes", "publicationDate", "openAccessPdf"].

**Returns:**

  dict: The response data from the API or error information if the
request fails.

<a id="camel.toolkits.semantic_scholar_toolkit.SemanticScholarToolkit.fetch_paper_data_id"></a>

### fetch_paper_data_id

```python
def fetch_paper_data_id(self, paper_id: str, fields: Optional[List[str]] = None):
```

Fetches a SINGLE paper from the Semantic Scholar
API based on a paper ID.

**Parameters:**

- **paper_id** (str): The ID of the paper to fetch.
- **fields** (Optional[List[str]], optional): The fields to include in the response (default: :obj:`None`). If not provided defaults to ["title", "abstract", "authors", "year", "citationCount", "publicationTypes", "publicationDate", "openAccessPdf"].

**Returns:**

  dict: The response data from the API or error information
if the request fails.

<a id="camel.toolkits.semantic_scholar_toolkit.SemanticScholarToolkit.fetch_bulk_paper_data"></a>

### fetch_bulk_paper_data

```python
def fetch_bulk_paper_data(
    self,
    query: str,
    year: str = '2023-',
    fields: Optional[List[str]] = None
):
```

Fetches MULTIPLE papers at once from the Semantic Scholar
API based on a related topic.

**Parameters:**

- **query** (str): The text query to match against the paper's title and abstract. For example, you can use the following operators and techniques to construct your query: Example 1: ((cloud computing) | virtualization) +security -privacy This will match papers whose title or abstract contains "cloud" and "computing", or contains the word "virtualization". The papers must also include the term "security" but exclude papers that contain the word "privacy".
- **year** (str, optional): The year filter for papers (default: :obj:`"2023-"`).
- **fields** (Optional[List[str]], optional): The fields to include in the response (default: :obj:`None`). If not provided defaults to ["title", "url", "publicationTypes", "publicationDate", "openAccessPdf"].

**Returns:**

  dict: The response data from the API or error information if the
request fails.

<a id="camel.toolkits.semantic_scholar_toolkit.SemanticScholarToolkit.fetch_recommended_papers"></a>

### fetch_recommended_papers

```python
def fetch_recommended_papers(
    self,
    positive_paper_ids: List[str],
    negative_paper_ids: List[str],
    fields: Optional[List[str]] = None,
    limit: int = 500,
    save_to_file: bool = False
):
```

Fetches recommended papers from the Semantic Scholar
API based on the positive and negative paper IDs.

**Parameters:**

- **positive_paper_ids** (list): A list of paper IDs (as strings) that are positively correlated to the recommendation.
- **negative_paper_ids** (list): A list of paper IDs (as strings) that are negatively correlated to the recommendation.
- **fields** (Optional[List[str]], optional): The fields to include in the response (default: :obj:`None`). If not provided defaults to ["title", "url", "citationCount", "authors", "publicationTypes", "publicationDate", "openAccessPdf"].
- **limit** (int, optional): The maximum number of recommended papers to return (default: :obj:`500`).
- **save_to_file** (bool, optional): If True, saves the response data to a file (default: :obj:`False`).

**Returns:**

  dict: A dictionary containing recommended papers sorted by
citation count.

<a id="camel.toolkits.semantic_scholar_toolkit.SemanticScholarToolkit.fetch_author_data"></a>

### fetch_author_data

```python
def fetch_author_data(
    self,
    ids: List[str],
    fields: Optional[List[str]] = None,
    save_to_file: bool = False
):
```

Fetches author information from the Semantic Scholar
API based on author IDs.

**Parameters:**

- **ids** (list): A list of author IDs (as strings) to fetch data for.
- **fields** (Optional[List[str]], optional): The fields to include in the response (default: :obj:`None`). If not provided defaults to ["name", "url", "paperCount", "hIndex", "papers"].
- **save_to_file** (bool, optional): Whether to save the results to a file (default: :obj:`False`).

**Returns:**

  dict: The response data from the API or error information if
the request fails.

<a id="camel.toolkits.semantic_scholar_toolkit.SemanticScholarToolkit.get_tools"></a>

### get_tools

```python
def get_tools(self):
```

**Returns:**

  List[FunctionTool]: A list of FunctionTool objects
representing the functions in the toolkit.
