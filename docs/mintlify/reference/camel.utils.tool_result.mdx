<a id="camel.utils.tool_result"></a>

<a id="camel.utils.tool_result.ToolResult"></a>

## ToolResult

```python
class ToolResult:
```

Special result type for tools that can return images along with text.

This class is used by ChatAgent to detect when a tool returns visual
content that should be included in the conversation context.

<a id="camel.utils.tool_result.ToolResult.__init__"></a>

### __init__

```python
def __init__(self, text: str, images: Optional[List[str]] = None):
```

Initialize a tool result.

**Parameters:**

- **text** (str): The text description or result of the tool operation.
- **images** (Optional[List[str]]): List of base64-encoded images to include in the conversation context. Images should be encoded as "data:image/\{format\};base64,\{data\}" format.

<a id="camel.utils.tool_result.ToolResult.__str__"></a>

### __str__

```python
def __str__(self):
```

Return the text representation of the result.

<a id="camel.utils.tool_result.ToolResult.__repr__"></a>

### __repr__

```python
def __repr__(self):
```

Return a detailed representation of the result.
