<a id="camel.types.agents.tool_calling_record"></a>

<a id="camel.types.agents.tool_calling_record.ToolCallingRecord"></a>

## ToolCallingRecord

```python
class ToolCallingRecord(BaseModel):
```

Historical records of tools called in the conversation.

**Parameters:**

- **func_name** (str): The name of the tool being called.
- **args** (Dict[str, Any]): The dictionary of arguments passed to the tool.
- **result** (Any): The execution result of calling this tool.
- **tool_call_id** (str): The ID of the tool call, if available.
- **images** (Optional[List[str]]): List of base64-encoded images returned by the tool, if any.

<a id="camel.types.agents.tool_calling_record.ToolCallingRecord.__str__"></a>

### __str__

```python
def __str__(self):
```

**Returns:**

  str: Modified string to represent the tool calling.

<a id="camel.types.agents.tool_calling_record.ToolCallingRecord.as_dict"></a>

### as_dict

```python
def as_dict(self):
```

**Returns:**

  dict[str, Any]: The tool calling record as a dictionary.
