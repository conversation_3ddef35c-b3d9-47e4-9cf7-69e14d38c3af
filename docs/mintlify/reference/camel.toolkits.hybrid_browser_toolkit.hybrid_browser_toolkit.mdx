<a id="camel.toolkits.hybrid_browser_toolkit.hybrid_browser_toolkit"></a>

<a id="camel.toolkits.hybrid_browser_toolkit.hybrid_browser_toolkit.HybridBrowserToolkit"></a>

## HybridBrowserToolkit

```python
class HybridBrowserToolkit(BaseToolkit):
```

A hybrid browser toolkit that can switch between TypeScript and Python
implementations.

This wrapper allows users to choose between:
- 'typescript': WebSocket-based implementation using TypeScript/Node.js
- 'python': Pure Python implementation using Playwright directly

<a id="camel.toolkits.hybrid_browser_toolkit.hybrid_browser_toolkit.HybridBrowserToolkit.__new__"></a>

### __new__

```python
def __new__(cls, **kwargs: Any):
```

Create a HybridBrowserToolkit instance with the specified mode.

**Parameters:**

- **mode** (`Literal["typescript", "python"]`): Implementation mode. - 'typescript': Uses WebSocket-based TypeScript implementation - 'python': Uses pure Python Playwright implementation Defaults to "typescript".
- **headless** (bool): Whether to run browser in headless mode. Defaults to True.
- **user_data_dir** (Optional[str]): Directory for user data persistence. Defaults to None.
- **stealth** (bool): Whether to enable stealth mode. Defaults to False.
- **web_agent_model** (Optional[BaseModelBackend]): Model for web agent operations. Defaults to None.
- **cache_dir** (str): Directory for caching. Defaults to "tmp/". (default: `"tmp/"`)
- **enabled_tools** (Optional[List[str]]): List of enabled tools. Defaults to None.
- **browser_log_to_file** (bool): Whether to log browser actions to file. Defaults to False.
- **session_id** (Optional[str]): Session identifier. Defaults to None.
- **default_start_url** (str): Default URL to start with. Defaults to "https://google.com/".
- **default_timeout** (Optional[int]): Default timeout in milliseconds. Defaults to None.
- **short_timeout** (Optional[int]): Short timeout in milliseconds. Defaults to None.
- **navigation_timeout** (Optional[int]): Navigation timeout in milliseconds. Defaults to None.
- **network_idle_timeout** (Optional[int]): Network idle timeout in milliseconds. Defaults to None.
- **screenshot_timeout** (Optional[int]): Screenshot timeout in milliseconds. Defaults to None.
- **page_stability_timeout** (Optional[int]): Page stability timeout in milliseconds. Defaults to None.
- **dom_content_loaded_timeout** (Optional[int]): DOM content loaded timeout in milliseconds. Defaults to None.
- **viewport_limit** (bool): Whether to filter page snapshot elements to only those visible in the current viewport. Defaults to False.
- **connect_over_cdp** (bool): Whether to connect to an existing browser via Chrome DevTools Protocol. Defaults to False. (Only supported in TypeScript mode)
- **cdp_url** (Optional[str]): WebSocket endpoint URL for CDP connection. Required when connect_over_cdp is True. Defaults to None. (Only supported in TypeScript mode) **kwargs: Additional keyword arguments passed to the implementation.

**Returns:**

  HybridBrowserToolkit instance of the specified implementation.
