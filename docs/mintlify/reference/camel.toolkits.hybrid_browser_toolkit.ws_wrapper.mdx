<a id="camel.toolkits.hybrid_browser_toolkit.ws_wrapper"></a>

<a id="camel.toolkits.hybrid_browser_toolkit.ws_wrapper.action_logger"></a>

## action_logger

```python
def action_logger(func):
```

Decorator to add logging to action methods.

<a id="camel.toolkits.hybrid_browser_toolkit.ws_wrapper.WebSocketBrowserWrapper"></a>

## WebSocketBrowserWrapper

```python
class WebSocketBrowserWrapper:
```

Python wrapper for the TypeScript hybrid browser
toolkit implementation using WebSocket.

<a id="camel.toolkits.hybrid_browser_toolkit.ws_wrapper.WebSocketBrowserWrapper.__init__"></a>

### __init__

```python
def __init__(self, config: Optional[Dict[str, Any]] = None):
```

Initialize the wrapper.

**Parameters:**

- **config**: Configuration dictionary for the browser toolkit
