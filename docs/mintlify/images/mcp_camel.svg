<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="-8 -8 416.9375 147.1875" style="max-width: 416.9375px;" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-svg-374783"><style>#mermaid-svg-374783{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}#mermaid-svg-374783 .error-icon{fill:#552222;}#mermaid-svg-374783 .error-text{fill:#552222;stroke:#552222;}#mermaid-svg-374783 .edge-thickness-normal{stroke-width:2px;}#mermaid-svg-374783 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-svg-374783 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-svg-374783 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-svg-374783 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-svg-374783 .marker{fill:#333333;stroke:#333333;}#mermaid-svg-374783 .marker.cross{stroke:#333333;}#mermaid-svg-374783 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-svg-374783 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#mermaid-svg-374783 .cluster-label text{fill:#333;}#mermaid-svg-374783 .cluster-label span,#mermaid-svg-374783 p{color:#333;}#mermaid-svg-374783 .label text,#mermaid-svg-374783 span,#mermaid-svg-374783 p{fill:#333;color:#333;}#mermaid-svg-374783 .node rect,#mermaid-svg-374783 .node circle,#mermaid-svg-374783 .node ellipse,#mermaid-svg-374783 .node polygon,#mermaid-svg-374783 .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#mermaid-svg-374783 .flowchart-label text{text-anchor:middle;}#mermaid-svg-374783 .node .label{text-align:center;}#mermaid-svg-374783 .node.clickable{cursor:pointer;}#mermaid-svg-374783 .arrowheadPath{fill:#333333;}#mermaid-svg-374783 .edgePath .path{stroke:#333333;stroke-width:2.0px;}#mermaid-svg-374783 .flowchart-link{stroke:#333333;fill:none;}#mermaid-svg-374783 .edgeLabel{background-color:#e8e8e8;text-align:center;}#mermaid-svg-374783 .edgeLabel rect{opacity:0.5;background-color:#e8e8e8;fill:#e8e8e8;}#mermaid-svg-374783 .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#mermaid-svg-374783 .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#mermaid-svg-374783 .cluster text{fill:#333;}#mermaid-svg-374783 .cluster span,#mermaid-svg-374783 p{color:#333;}#mermaid-svg-374783 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#mermaid-svg-374783 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#mermaid-svg-374783 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="6" viewBox="0 0 10 10" class="marker flowchart" id="mermaid-svg-374783_flowchart-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart" id="mermaid-svg-374783_flowchart-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart" id="mermaid-svg-374783_flowchart-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart" id="mermaid-svg-374783_flowchart-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart" id="mermaid-svg-374783_flowchart-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart" id="mermaid-svg-374783_flowchart-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-svg-374783_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-A LE-B" id="L-A-B-0" d="M106.656,65.594L110.823,65.594C114.99,65.594,123.323,65.594,130.773,65.594C138.223,65.594,144.79,65.594,148.073,65.594L151.356,65.594"></path><path marker-end="url(#mermaid-svg-374783_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-B LE-C" id="L-B-C-0" d="M235.2,45.297L241.777,41.13C248.355,36.964,261.509,28.63,271.37,24.464C281.231,20.297,287.797,20.297,291.081,20.297L294.364,20.297"></path><path marker-end="url(#mermaid-svg-374783_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-B LE-D" id="L-B-D-0" d="M235.2,85.891L241.777,90.057C248.355,94.224,261.509,102.557,271.588,106.724C281.667,110.891,288.67,110.891,292.171,110.891L295.673,110.891"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(53.328125, 65.59375)" id="flowchart-A-62" class="node default default flowchart-label"><rect height="40.59375" width="106.65625" y="-20.296875" x="-53.328125" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-45.828125, -12.796875)" style="" class="label"><rect></rect><foreignObject height="25.59375" width="91.65625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">CAMEL Agent</span></div></foreignObject></g></g><g transform="translate(203.16015625, 65.59375)" id="flowchart-B-63" class="node default default flowchart-label"><rect height="40.59375" width="93.0078125" y="-20.296875" x="-46.50390625" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-39.00390625, -12.796875)" style="" class="label"><rect></rect><foreignObject height="25.59375" width="78.0078125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">MCPToolkit</span></div></foreignObject></g></g><g transform="translate(350.30078125, 20.296875)" id="flowchart-C-65" class="node default default flowchart-label"><rect height="40.59375" width="101.2734375" y="-20.296875" x="-50.63671875" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-43.13671875, -12.796875)" style="" class="label"><rect></rect><foreignObject height="25.59375" width="86.2734375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">MCP Servers</span></div></foreignObject></g></g><g transform="translate(350.30078125, 110.890625)" id="flowchart-D-67" class="node default default flowchart-label"><rect height="40.59375" width="98.65625" y="-20.296875" x="-49.328125" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-41.828125, -12.796875)" style="" class="label"><rect></rect><foreignObject height="25.59375" width="83.65625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">_MCPServer</span></div></foreignObject></g></g></g></g></g></svg>