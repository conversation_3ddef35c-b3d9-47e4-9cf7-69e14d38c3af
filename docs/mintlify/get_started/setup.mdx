---
title: "Setup"
icon: gear
description: "Configure API credentials and select a model provider"
---

CAMEL-AI supports multiple model backends. Choose one below and configure your environment variables.

## 1. OpenAI API

Obtain your `OPENAI_API_KEY` from [OpenAI Dashboard](https://platform.openai.com/account/api-keys).

<Steps>
  <Step title="Unix (macOS/Linux)">
    ```bash
    echo 'export OPENAI_API_KEY="<YOUR_KEY>"' >> ~/.zshrc
    source ~/.zshrc
    ```
    _Replace `~/.zshrc` with `~/.bashrc` if using bash._
  </Step>
  <Step title="Windows (Permanent)">
    ```powershell
    setx OPENAI_API_KEY "<YOUR_KEY>" /M
    ```
    _You may need to restart your terminal for changes to apply._
  </Step>
  <Step title=".env File (Project-specific)">
    Create a `.env` file in your project root:
    ```dotenv
    OPENAI_API_KEY=<YOUR_KEY>
    ```
    Load in Python:
    ```python
    from dotenv import load_dotenv
    load_dotenv()
    import os
    print(os.getenv("OPENAI_API_KEY"))
    ```
  </Step>
</Steps>

<Note>
  To configure an `API_BASE_URL` (e.g., Azure), also set:
  ```bash
  export OPENAI_API_BASE_URL="<YOUR_BASE_URL>"
  ```
</Note>

## 2. Other Hosted APIs

For non-OpenAI providers, see [Using Models by API Calling](../key_modules/models).

## 3. Local Models

To run fully on-device with open-source models, refer to [Local Models Guide](../key_modules/models)
