{"openapi": "3.0.1", "info": {"title": "Outschool Plugin", "description": "Search for top-quality online classes and teachers on Outschool.", "version": "v1"}, "servers": [{"url": "https://chatgpt-plugin.outschool.com/api"}], "paths": {"/classes": {"get": {"operationId": "searchClasses", "description": "Returns a list of online classes", "parameters": [{"name": "timeZone", "in": "query", "required": true, "description": "IANA Time Zone identifier of the user. Either provided by user or derived from their location. Since Outschool parents and teachers can be from different time zones, this is required to search classes that are available in parent's timezone at reasonable hours. Only IANA format is accepted.", "schema": {"type": "string"}, "examples": {"losAngeles": {"value": "America/Los_Angeles"}, "newYork": {"value": "America/New_York"}, "london": {"value": "Europe/London"}}}, {"name": "age", "in": "query", "required": true, "description": "Outschool has several classes serving different age groups. The age of the learner(s) helps to find classes that match the best. This is a comma separated list. If the age difference between the children is more than 5 years, it may be better to search for different ages separately to get better search results.", "schema": {"type": "string", "minimum": 3, "maximum": 18}, "examples": {"12": {"value": "12"}, "1213": {"value": "12,13"}, "5617": {"value": "5,6,17"}}}, {"name": "q", "in": "query", "required": false, "description": "Keywords to use to search in the class list. Classes matching the keyword closest will be returned.", "schema": {"type": "string"}}, {"name": "delivery", "in": "query", "required": false, "explode": true, "description": "Filters classes by delivery type. Description for different enum values:\n              One-time: Classes that meets once\n              Ongoing: Weekly classes that learners can enroll in any week\n              Semester course: Multi-week/session classes, usually more than 4 weeks\n              Short course: Multi-week/session classes, usually around 4 weeks\n              Camp: Semester or short courses during summer and school breaks\n              Group: Async chat groups on a specific topic where learners share ideas and experiences, like clubs", "schema": {"type": "array", "items": {"type": "string", "enum": ["One-time", "Ongoing", "Semester course", "Short course", "Camp", "Group"]}}}, {"name": "userUid", "in": "query", "required": false, "description": "Only search classes taught by a specific teacher. The userUid is the id of the teacher", "schema": {"type": "string", "format": "uuid"}}, {"name": "order", "in": "query", "description": "Sort results by either upcoming, new, or relevance. Upcoming sorts by next section start date in ascending order, new sorts by class published date in descending order, and relevance sorts by the keyword relevance and popularity of the class.", "schema": {"type": "string", "enum": ["upcoming", "new", "relevance"], "default": "relevance"}}, {"name": "offset", "in": "query", "required": false, "description": "The offset for the results. Offset and limit used in combination to paginate in results. For instance, if limit is 10, to get next 10 results, the offset should be set to 10.", "schema": {"type": "number", "default": 0}}, {"name": "limit", "in": "query", "required": false, "description": "Number of results to return.", "schema": {"type": "number", "default": 10}}, {"name": "startAfter", "in": "query", "required": false, "description": "Search classes that have a section starting on or after a given date. Only today or future dates are allowed.", "schema": {"type": "string", "format": "date"}, "examples": {"April152023": {"value": "2023-04-15"}}}, {"name": "dow", "in": "query", "description": "The day of week to filter classes and only return classes that have a section on given days of the week.", "schema": {"type": "array", "items": {"type": "string", "enum": ["Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>", "Sat", "Sun"]}}, "style": "form", "explode": true, "required": false, "examples": {"Mon": {"value": "Mon"}, "Mon_Tue": {"value": "Mon,Tue"}, "Mon_Thu": {"value": "<PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON>hu"}, "Weekdays": {"value": "<PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>"}, "Weekend": {"value": "Sat, Sun"}}}, {"name": "startAfterTime", "in": "query", "description": "The start time of the class in 24 hour format as hour of the day normalized by the user's timezone", "schema": {"type": "number", "minimum": 6, "maximum": 22}}, {"name": "endByTime", "in": "query", "description": "The end time of the class in 24 hour format as hour of the day normalized by the user's timezone", "schema": {"type": "number", "minimum": 6, "maximum": 22}}], "responses": {"200": {"description": "A list of classes", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/class"}}}}}}}}, "/teachers": {"get": {"operationId": "searchTeachers", "description": "Returns a list of teachers", "parameters": [{"name": "name", "in": "query", "required": true, "description": "Name of the teacher to search for", "schema": {"type": "string"}}, {"name": "limit", "in": "query", "required": false, "description": "Number of results to return.", "schema": {"type": "number", "default": 10}}], "responses": {"200": {"description": "A list of teachers", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/teacher"}}}}}}}}}, "components": {"schemas": {"class": {"type": "object", "properties": {"uid": {"type": "string", "format": "uuid", "description": "Unique ID of the class in the system that can be used in other API end points"}, "title": {"type": "string", "description": "Title of the class"}, "summary": {"type": "string", "description": "Summary of the class"}, "url": {"type": "string", "format": "uri", "description": "URL to the class detail page"}, "photo": {"type": "string", "format": "uri", "description": "Photo of the class"}, "is_ongoing_weekly": {"type": "boolean", "description": "Whether this class is an ongoing class or not. When a class is an ongoing class, parents can enroll their children for any week of an ongoing class, because the sections of that class meet every week and the weeks don't depend on each other."}, "age_min": {"type": "number", "description": "The minimum age a learner should be to enroll in the class. Although Outschool has classes for different age groups, individual classes may only be appropriate for a certain age range."}, "age_max": {"type": "number", "description": "The maximum age a learner should be to enroll in the class. Although Outschool has classes for different age groups, individual classes may only be appropriate for a certain age range."}, "teacher": {"$ref": "#/components/schemas/teacher"}, "nextSection": {"$ref": "#/components/schemas/section", "nullable": true, "description": "The next section of the class that the parent/caregiver can enroll their children in. This is usually what parents are looking for to enroll in a class."}}}, "teacher": {"type": "object", "properties": {"uid": {"type": "string", "format": "uuid", "description": "Unique ID of the teacher in the system that can be used in other API end points"}, "name": {"type": "string", "description": "Name of the teacher"}, "about": {"type": "string", "description": "A short summary the teacher provides about themselves"}, "photo": {"type": "string", "format": "uri", "description": "Photo of the teacher"}, "url": {"type": "string", "format": "uri", "description": "URL to the Outschool profile page of the teacher"}}}, "section": {"type": "object", "description": "Sections are what parents enroll their children in for a given class. They are separate cohorts of a class.", "properties": {"uid": {"type": "string", "format": "uuid", "description": "Unique ID of the section in the system that can be used in other API end points"}, "url": {"type": "string", "format": "uri", "description": "URL pointing to the section page"}, "start_time": {"type": "string", "format": "datetime", "description": "The start time for the first meeting of a section."}, "end_time": {"type": "string", "format": "datetime", "description": "The end time for the last meeting of a section."}, "size_max": {"type": "number", "description": "How many learners can enroll in the section."}, "filledSpaceCount": {"type": "number", "description": "How many learners are enrolled in the section. size_max - filledSpaceCount gives how many seats are left to enroll in."}, "nextOngoingMeeting": {"$ref": "#/components/schemas/meeting", "nullable": true, "description": "If the class is an ongoing class, this points to the next meeting for the section."}}}, "meeting": {"type": "object", "description": "The online meeting for a section. Meetings are held on Zoom.", "properties": {"uid": {"type": "string", "format": "uuid", "description": "Unique ID of the meeting in the system that can be used in other API end points"}, "start_time": {"type": "string", "format": "datetime", "description": "The start time of the meeting."}, "end_time": {"type": "string", "format": "datetime", "description": "The end time of the meeting."}}}}}}