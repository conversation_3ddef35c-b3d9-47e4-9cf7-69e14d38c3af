# ========= Copyright 2023-2024 @ CAMEL-AI.org. All Rights Reserved. =========
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ========= Copyright 2023-2024 @ CAMEL-AI.org. All Rights Reserved. =========
from __future__ import annotations

from typing import Dict, List, Optional, Union

from pydantic import Field

from camel.configs.base_config import BaseConfig
from camel.types import NotGiven


class QianfanConfig(BaseConfig):
    r"""Defines the parameters for generating chat completions using OpenAI
    compatibility.

    Reference: https://cloud.baidu.com/doc/qianfan-api/s/3m7of64lb

     Args:
        presence_penalty (float, optional): Number between :obj:`-2.0` and
            :obj:`2.0`. Positive values penalize new tokens based on whether
            they appear in the text so far, increasing the model's likelihood
            to talk about new topics. See more information about frequency and
            presence penalties. (default: :obj:`None`)
        frequency_penalty (float, optional): Number between :obj:`-2.0` and
            :obj:`2.0`. Positive values penalize new tokens based on their
            existing frequency in the text so far, decreasing the model's
            likelihood to repeat the same line verbatim. See more information
            about frequency and presence penalties. (default: :obj:`None`)
        repetition_penalty (float, optional): Penalizes new tokens based on
            their appearance in the prompt and generated text.
            (default: :obj:`None`)
        stream (bool, optional): Whether to stream the response.
            (default: :obj:`None`)
        temperature (float, optional): Controls randomness in the response.
            Higher values make output more random, lower values make it more
            deterministic. Range: [0.0, 2.0]. (default: :obj:`None`)
        top_p (float, optional): Controls diversity via nucleus sampling.
            Range: [0.0, 1.0]. (default: :obj:`None`)
        logit_bias (dict, optional): Modify the likelihood of specified tokens
            appearing in the completion. Accepts a json object that maps tokens
            (specified by their token ID in the tokenizer) to an associated
            bias value from :obj:`-100` to :obj:`100`. Mathematically, the bias
            is added to the logits generated by the model prior to sampling.
            The exact effect will vary per model, but values between:obj:` -1`
            and :obj:`1` should decrease or increase likelihood of selection;
            values like :obj:`-100` or :obj:`100` should result in a ban or
            exclusive selection of the relevant token. (default: :obj:`None`)
        max_tokens (Union[int, NotGiven], optional): Maximum number of tokens
            to generate. If not provided, model will use its default maximum.
            (default: :obj:`None`)
        stop (Optional[List[str]], optional): List of stop sequences.
            (default: :obj:`None`)
        n (Optional[int], optional): Number of chat completion choices to
            generate for each input message. (default: :obj:`None`)
        tools (List, optional): Specifies an array of tools that the model can
            call. It can contain one or more tool objects. During a function
            call process, the model will select one tool from the array.
            (default: :obj:`None`)
    """

    stream: Optional[bool] = Field(default=None)
    temperature: Optional[float] = Field(default=None)
    top_p: Optional[float] = Field(default=None)
    presence_penalty: Optional[float] = Field(default=None)
    frequency_penalty: Optional[float] = Field(default=None)
    repetition_penalty: Optional[float] = Field(default=None)
    max_tokens: Optional[Union[int, NotGiven]] = Field(default=None)
    stop: Optional[List[str]] = Field(default=None)
    n: Optional[int] = Field(default=None)
    logit_bias: Optional[Dict[str, float]] = Field(default=None)


QIANFAN_API_PARAMS = {param for param in QianfanConfig.model_fields.keys()}
