## Description

Describe your changes in detail (optional if the linked issue already contains a detailed description of the changes).

## Checklist

Go over all the following points, and put an `x` in all the boxes that apply.

- [ ] I have read the [CONTRIBUTION](https://github.com/camel-ai/camel/blob/master/CONTRIBUTING.md) guide (**required**)
- [ ] I have linked this PR to an issue using the Development section on the right sidebar or by adding `Fixes #issue-number` in the PR description (**required**)
- [ ] I have checked if any dependencies need to be added or updated in `pyproject.toml` and `uv lock`
- [ ] I have updated the tests accordingly (*required for a bug fix or a new feature*)
- [ ] I have updated the documentation if needed:
- [ ] I have added examples if this is a new feature

If you are unsure about any of these, don't hesitate to ask. We are here to help!
