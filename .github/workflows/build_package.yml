name: Build Package

on:
  push:
    branches: [ "master", "qa" ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Free disk space
        run: |
          sudo apt-get clean
          sudo apt-get autoremove -y
          sudo rm -rf /var/lib/apt/lists/*
          sudo rm -rf /usr/share/dotnet
          sudo rm -rf /opt/ghc
          sudo rm -rf /usr/local/share/boost
          sudo rm -rf /usr/local/lib/android
          sudo rm -rf /tmp/*
          sudo rm -rf $HOME/.cache/pip
          df -h

      - name: Set up Python environment
        uses: actions/setup-python@v3
        with:
          python-version: "3.10"

      - name: Install uv
        run: |
          pip install uv

      - name: Create virtual environment
        run: |
          uv venv .venv --python=3.10
          df -h

      - name: Install core dependencies
        run: |
          source .venv/bin/activate
          # Install just the core dependencies first
          uv pip install -e "."
          df -h

      - name: Install dev dependencies
        run: |
          source .venv/bin/activate
          # Install dev dependencies separately
          uv pip install -e ".[dev]"
          df -h

      - name: Install docs dependencies
        run: |
          source .venv/bin/activate
          # Install docs dependencies separately
          uv pip install -e ".[docs]"
          df -h

      - name: Install build tools
        run: |
          source .venv/bin/activate
          uv pip install build
          df -h

      - name: Build
        run: |
          source .venv/bin/activate
          python -m build

      - name: Set up test environment
        run: |
          python -m venv test_venv
          source test_venv/bin/activate

      - name: Install the package
        run: |
          source test_venv/bin/activate
          WHEEL_FILE=$(ls dist/*.whl)
          pip install "${WHEEL_FILE}[all,test]"

      - name: Test import
        run: |
          source test_venv/bin/activate
          python -c "import camel"

      - name: Run tests with Pytest
        env:
          OPENAI_API_KEY: "${{ secrets.OPENAI_API_KEY }}"
          GOOGLE_API_KEY: "${{ secrets.GOOGLE_API_KEY }}"
          SEARCH_ENGINE_ID: "${{ secrets.SEARCH_ENGINE_ID }}"
          OPENWEATHERMAP_API_KEY: "${{ secrets.OPENWEATHERMAP_API_KEY }}"
          ANTHROPIC_API_KEY: "${{ secrets.ANTHROPIC_API_KEY }}"
          GROQ_API_KEY: "${{ secrets.GROQ_API_KEY }}"
          COHERE_API_KEY: "${{ secrets.COHERE_API_KEY }}"
          HF_TOKEN: "${{ secrets.HF_TOKEN }}"
          AZURE_OPENAI_API_KEY: "${{ secrets.AZURE_OPENAI_API_KEY }}"
          AZURE_API_VERSION: "${{ secrets.AZURE_API_VERSION }}"
          AZURE_DEPLOYMENT_NAME: "${{ secrets.AZURE_DEPLOYMENT_NAME }}"
          AZURE_OPENAI_BASE_URL: "${{ secrets.AZURE_OPENAI_BASE_URL }}"
          MISTRAL_API_KEY: "${{ secrets.MISTRAL_API_KEY }}"
          REKA_API_KEY: "${{ secrets.REKA_API_KEY }}"
          NEO4J_URI: "${{ secrets.NEO4J_URI }}"
          NEO4J_USERNAME: "${{ secrets.NEO4J_USERNAME }}"
          NEO4J_PASSWORD: "${{ secrets.NEO4J_PASSWORD }}"
          FIRECRAWL_API_KEY: "${{ secrets.FIRECRAWL_API_KEY }}"
          ASKNEWS_CLIENT_ID: "${{ secrets.ASKNEWS_CLIENT_ID }}"
          ASKNEWS_CLIENT_SECRET: "${{ secrets.ASKNEWS_CLIENT_SECRET }}"
          CHUNKR_API_KEY: "${{ secrets.CHUNKR_API_KEY }}"
          MESHY_API_KEY: "${{ secrets.MESHY_API_KEY }}"
          YI_API_KEY: "${{ secrets.YI_API_KEY }}"
          TOGETHER_API_KEY: "${{ secrets.TOGETHER_API_KEY }}"
          SAMBA_API_KEY: "${{ secrets.SAMBA_API_KEY }}"
          QWEN_API_KEY: "${{ secrets.QWEN_API_KEY }}"
          GEMINI_API_KEY: "${{ secrets.GEMINI_API_KEY }}"
          DEEPSEEK_API_KEY: "${{ secrets.DEEPSEEK_API_KEY }}"
          DAPPIER_API_KEY: "${{ secrets.DAPPIER_API_KEY }}"
          DISCORD_BOT_TOKEN: "${{ secrets.DISCORD_BOT_TOKEN }}"
          INTERNLM_API_KEY: "${{ secrets.INTERNLM_API_KEY }}"
          JINA_API_KEY: "${{ secrets.JINA_API_KEY }}"
          SILICONFLOW_API_KEY: "${{ secrets.SILICONFLOW_API_KEY }}"
          MOONSHOT_API_KEY: "${{ secrets.MOONSHOT_API_KEY }}"
          AIML_API_KEY: "${{ secrets.AIML_API_KEY }}"
          NVIDIA_API_KEY: "${{ secrets.NVIDIA_API_KEY }}"
          VOLCANO_API_KEY: "${{ secrets.VOLCANO_API_KEY }}"
          OPENROUTER_API_KEY: "${{ secrets.OPENROUTER_API_KEY }}"
          MODELSCOPE_SDK_TOKEN: "${{ secrets.MODELSCOPE_SDK_TOKEN }}"
          NOTION_TOKEN: "${{ secrets.NOTION_TOKEN }}"
          GOOGLE_CLIENT_ID: "${{ secrets.GOOGLE_CLIENT_ID }}"
          GOOGLE_CLIENT_SECRET: "${{ secrets.GOOGLE_CLIENT_SECRET }}"
          PPIO_API_KEY: "${{ secrets.PPIO_API_KEY }}"
          BEDROCK_API_KEY: "${{ secrets.BEDROCK_API_KEY }}"
          BEDROCK_API_BASE_URL: "${{ secrets.BEDROCK_API_BASE_URL }}"
          NETMIND_API_KEY: "${{ secrets.NETMIND_API_KEY }}"
          NOVITA_API_KEY: "${{ secrets.NOVITA_API_KEY }}"
          WATSONX_API_KEY: "${{ secrets.WATSONX_API_KEY }}"
          WATSONX_PROJECT_ID: "${{ secrets.WATSONX_PROJECT_ID }}"
          QIANFAN_API_KEY: "${{ secrets.QIANFAN_API_KEY }}"
          ACI_API_KEY: "${{ secrets.ACI_API_KEY }}"
          BOHRIUM_API_KEY: "${{ secrets.BOHRIUM_API_KEY }}"
          CRYNUX_API_KEY: "${{ secrets.CRYNUX_API_KEY }}"
        run: |
          source test_venv/bin/activate
          pytest --fast-test-mode -m "not heavy_dependency" \
            --ignore=test/datagen/test_evol_instruct.py \
            --ignore=test/datagen/test_self_improving_cot_pipeline.py \
            --ignore=test/datasets/test_base_dataset.py \
            --ignore=test/datasets/test_self_instruct_generator.py \
            --ignore=test/embeddings/test_open_source_embeddings.py \
            --ignore=test/environments/test_multi_step_env.py \
            --ignore=test/environments/test_single_step_env.py \
            --ignore=test/environments/test_tictactoe_env.py \
            --ignore=test/interpreters/test_python_interpreter.py \
            --ignore=test/models/reward/test_nemotron_model.py \
            --ignore=test/toolkits/test_video_analysis_toolkit.py \
            --ignore=test/toolkits/test_jina_reranker_toolkit.py \
            ./test

      - name: Clean up after build
        if: always()
        run: |
          sudo apt-get clean
          sudo apt-get autoremove -y
          sudo rm -rf /var/lib/apt/lists/*
          sudo rm -rf /usr/share/dotnet
          sudo rm -rf /opt/ghc
          sudo rm -rf /usr/local/share/boost
          sudo rm -rf /usr/local/lib/android
          sudo rm -rf /tmp/*
          sudo rm -rf $HOME/.cache/pip
          df -h
