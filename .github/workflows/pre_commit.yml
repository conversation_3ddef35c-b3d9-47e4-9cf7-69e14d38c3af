name: Pre Commit Check

on:
  push:
    branches: [ "master", "qa" ]
  pull_request:
    branches: [ "master", "qa" ]

jobs:
  pre-commit:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python environment and install dependencies
      uses: ./.github/actions/camel_install
      with:
        python-version: "3.10"
    - name: Run pre-commit
      run: |
        source .venv/bin/activate
        pre-commit run --all-files
      shell: bash
