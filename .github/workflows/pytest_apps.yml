# This workflow will install Python dependencies, run tests and lint with a single version of Python
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-python

name: Pytest Gradio Apps and Examples

on:
  push:
    branches: [ "master", "qa" ]
  pull_request:
    branches: [ "master", "qa" ]

permissions:
  contents: read

jobs:
  pytest_apps:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3
    - name: Set up Python environment and install dependencies
      uses: ./.github/actions/camel_install
      with:
        python-version: "3.10"
    - name: Run pytest
      env:
        OPENAI_API_KEY: "${{ secrets.OPENAI_API_KEY }}"
        GOOGLE_API_KEY: "${{ secrets.GOOGLE_API_KEY }}"
        SEARCH_ENGINE_ID: "${{ secrets.SEARCH_ENGINE_ID }}"
      run: |
        source .venv/bin/activate
        uv pip install -e ".[all, dev, docs]"
        pytest -v apps/

  pytest_examples:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3
    - name: Set up Python environment and install dependencies
      uses: ./.github/actions/camel_install
      with:
        python-version: "3.10"
    - name: Run pytest
      env:
        OPENAI_API_KEY: "${{ secrets.OPENAI_API_KEY }}"
        GOOGLE_API_KEY: "${{ secrets.GOOGLE_API_KEY }}"
        SEARCH_ENGINE_ID: "${{ secrets.SEARCH_ENGINE_ID }}"
        COHERE_API_KEY: "${{ secrets.COHERE_API_KEY }}"
        INTERNLM_API_KEY: "${{ secrets.INTERNLM_API_KEY }}"
        MOONSHOT_API_KEY: "${{ secrets.MOONSHOT_API_KEY }}"
        SILICONFLOW_API_KEY: "${{ secrets.SILICONFLOW_API_KEY }}"
        AIML_API_KEY: "${{ secrets.AIML_API_KEY }}"
      run: |
        source .venv/bin/activate
        uv pip install -e ".[all, dev, docs]"
        pytest -v examples/
