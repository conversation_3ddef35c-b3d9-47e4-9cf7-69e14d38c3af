name: Publish CAMEL to PyPI / GitHub

on:
  push:
    tags:
      - "v*"

  workflow_dispatch:

jobs:
  build-and-publish-pypi:
    name: Build and publish to PyPI
    runs-on: ubuntu-latest
    permissions:
      contents: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'

      - name: Install uv
        run: pip install uv

      - name: Setup virtual environment
        run: uv venv .venv --python=3.10

      - name: Build package
        run: |
          source .venv/bin/activate
          uv pip install -e ".[all, dev, docs]"
          uv pip install build
          python -m build

      - name: Publish to PyPI
        env:
          TWINE_USERNAME: __token__
          TWINE_PASSWORD: ${{ secrets.PYPI_API_KEY }}
        run: |
          source .venv/bin/activate
          uv pip install twine
          twine upload dist/*

      - name: Upload built artifacts
        uses: actions/upload-artifact@v4
        with:
          name: dist
          path: dist

  create-release:
    name: Create GitHub Release
    runs-on: ubuntu-latest
    needs: build-and-publish-pypi
    permissions:
      contents: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download built artifacts
        uses: actions/download-artifact@v4
        with:
          name: dist
          path: dist

      - name: Check tag for pre-release
        id: prerelease_check
        run: |
          if [[ "${{ github.ref_name }}" == *a* ]]; then
            echo "prerelease=true" >> $GITHUB_OUTPUT
          else
            echo "prerelease=false" >> $GITHUB_OUTPUT
          fi

      - name: Release to GitHub
        uses: softprops/action-gh-release@v2
        with:
          generate_release_notes: true
          draft: false
          prerelease: ${{ steps.prerelease_check.outputs.prerelease }}
          files: dist/*

  build-and-publish-image:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and Push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: .container/minimal_build/Dockerfile
          push: true
          tags: |
            ghcr.io/${{ github.repository }}:latest
            ghcr.io/${{ github.repository }}:${{ github.ref_name }}
          platforms: linux/amd64,linux/arm64
          sbom: false
          provenance: false
          labels: |
            org.opencontainers.image.source=https://github.com/${{ github.repository }}
            org.opencontainers.image.revision=${{ github.sha }}
