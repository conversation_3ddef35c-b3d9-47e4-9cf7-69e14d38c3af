# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Installation and Setup
```bash
# Install with basic dependencies
pip install camel-ai

# Install with all optional dependencies
pip install "camel-ai[all]"

# Install in development mode
make install-editable

# Install development tools
make ruff-install mypy-install pytest-install
```

### Code Quality and Testing
```bash
# Format code
make format

# Lint with ruff (primary linter)
make ruff

# Fix linting issues
make ruff-fix

# Type checking
make mypy

# Run tests
make pytest-install
pytest

# Run tests with coverage
pytest --cov=camel

# Run specific test categories
pytest -m "not model_backend"  # Skip tests requiring API keys
pytest -m "not very_slow"      # Skip slow tests
```

### Build and Release
```bash
# Build package
make build

# Clean build artifacts
make clean

# Run pre-commit hooks
make pre-commit
```

## Architecture Overview

CAMEL is a comprehensive multi-agent framework built around four core principles: **Evolvability**, **Scalability**, **Statefulness**, and **Code-as-Prompt**. The framework enables researchers and developers to build sophisticated AI agent systems with minimal boilerplate.

### Core Components

#### Agent System
- **BaseAgent**: Abstract base class defining `reset()` and `step()` interface
- **ChatAgent**: Primary implementation with full feature set (tools, memory, streaming)
- **Specialized Agents**: RoleAssignment, Critic, DeductiveReasoner, KnowledgeGraph, etc.

#### Model Layer
- **BaseModelBackend**: Unified interface for 40+ LLM providers
- **ModelFactory**: Creates appropriate model instances based on configuration
- **Automatic Message Preprocessing**: Handles thinking tags, tool calls, formatting

#### Message System
- **BaseMessage**: Multi-modal message support (text, images, video)
- **FunctionCallingMessage**: Tool/function call specialization
- **Format Conversion**: OpenAI, ShareGPT, and custom formats

#### Multi-Agent Coordination
- **RolePlaying**: Conversational agent pairs with optional task/critic agents
- **Workforce**: Advanced orchestration system with task channels and structured coordination
- **Society**: Base abstractions for multi-agent systems

#### Task Management
- **Task**: Hierarchical task representation with state management
- **TaskManager**: Coordinates execution and dependencies
- **Task Operations**: Decomposition, composition, evolution

#### Toolkit System
- **BaseToolkit**: Abstract base for tool collections with automatic timeout management
- **50+ Toolkits**: Web interaction, file operations, external APIs, specialized tasks
- **MCP Integration**: Model Context Protocol server support

#### Memory System
- **AgentMemory**: Main interface with multiple storage backends
- **MemoryBlock**: Abstract base for memory components
- **Context Creators**: Strategies for retrieving relevant context

### Key Design Patterns

1. **Abstract Factory**: ModelFactory for creating model instances
2. **Strategy Pattern**: Different memory backends, context creators, toolkits
3. **Template Method**: BaseAgent with abstract methods
4. **Observer Pattern**: Memory systems tracking agent interactions
5. **Command Pattern**: Function calling and tool execution
6. **State Pattern**: Task state management
7. **Builder Pattern**: Message construction with various formats

## Development Guidelines

### Code Style
- **Line Length**: 79 characters (enforced by ruff)
- **Formatting**: Use `make format` which applies yapf, isort, and ruff
- **Docstrings**: Google style convention
- **Type Hints**: Required for all new code

### Testing Guidelines
- **Unit Tests**: Required for all new features
- **Test Categories**: Use markers for `model_backend`, `very_slow`, `heavy_dependency`
- **Test Structure**: Mirror package structure in `test/` directory
- **Mocking**: Use mock for external dependencies when possible

### Adding New Features

#### New Agent Types
1. Inherit from `BaseAgent` or appropriate subclass
2. Implement `reset()` and `step()` methods
3. Add comprehensive tests
4. Update documentation

#### New Model Providers
1. Implement `BaseModelBackend` interface
2. Add model types to `unified_model_type.py`
3. Update `ModelFactory` registration
4. Add configuration class in `configs/`

#### New Toolkits
1. Inherit from `BaseToolkit` or `RegisteredAgentToolkit`
2. Implement tools as methods with proper error handling
3. Add timeout considerations
4. Include example usage

#### New Memory Backends
1. Implement `MemoryBlock` interface
2. Add storage-specific functionality
3. Include proper cleanup and resource management
4. Add tests with realistic data

### Dependency Management
- **Core Dependencies**: Minimal set in `[project.dependencies]`
- **Optional Dependencies**: Grouped by functionality (`rag`, `web_tools`, `model_platforms`, etc.)
- **Development Dependencies**: In `[project.optional-dependencies.dev]`
- **Use `uv`**: For faster dependency resolution and management

### Common Development Workflows

#### Adding a New Agent
```bash
# Create agent file
touch camel/agents/new_agent.py

# Create test file
touch test/agents/test_new_agent.py

# Create example
touch examples/agents/new_agent_example.py

# Run tests
pytest test/agents/test_new_agent.py -v

# Format and lint
make format && make ruff && make mypy
```

#### Adding a New Model Provider
```bash
# Create model backend
touch camel/models/new_model.py

# Create config
touch camel/configs/new_model_config.py

# Create test
touch test/models/test_new_model.py

# Create example
touch examples/models/new_model_example.py

# Update type definitions
# Edit camel/types/unified_model_type.py

# Update factory
# Edit camel/models/model_factory.py
```

### Important Files to Understand

#### Core Architecture
- `camel/agents/base.py`: Base agent interface
- `camel/models/base_model.py`: Model backend abstraction
- `camel/messages/base.py`: Message system foundation
- `camel/tasks/task.py`: Task management core

#### Configuration
- `pyproject.toml`: Project configuration and dependencies
- `camel/configs/`: Model provider configurations
- `camel/types/`: Type definitions and enums

#### Multi-Agent Systems
- `camel/societies/role_playing.py`: Conversational agents
- `camel/societies/workforce/`: Advanced orchestration

#### Tool Integration
- `camel/toolkits/base.py`: Toolkit foundation
- `camel/toolkits/`: Individual toolkit implementations

### Performance Considerations
- **Async Support**: Use async operations for I/O-bound tasks
- **Token Counting**: Automatic tracking for cost management
- **Timeout Management**: Built into toolkit system via metaclass
- **Resource Cleanup**: Proper context managers for external resources

### Documentation and Examples
- **Examples**: All features should have examples in `examples/`
- **Cookbooks**: Complex workflows documented in `docs/cookbooks/`
- **API Docs**: Auto-generated from docstrings
- **Type Hints**: Serve as live documentation

### Debugging and Troubleshooting
- **Logging**: Comprehensive logging throughout the system
- **Error Handling**: Proper exception hierarchy and handling
- **Testing**: Use `pytest` markers to control test execution
- **Memory Management**: Proper cleanup of resources and connections